import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
// import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';

class ErrorContainer extends StatelessWidget {
  final String errorMessage;
  final bool showErrorImage;
  final Color? errorMessageColor;
  final Function onTapRetry;
  final bool showRTryButton;
  final double topMargin;
  final bool? showBackButton;

  const ErrorContainer({
    required this.errorMessage,
    required this.onTapRetry,
    required this.showErrorImage,
    super.key,
    this.errorMessageColor,
    this.topMargin = 0.1,
    this.showBackButton,
    this.showRTryButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // صورة الخطأ
          if (showErrorImage) ...[
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).primaryColor.withOpacity(0.1),
              ),
              child: Center(
                child: SvgPicture.asset(
                  Assets.error,
                  width: 120,
                  height: 120,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            const SizedBox(height: 40),
          ],

          // رسالة الخطأ
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              '${context.tr(errorMessage) ?? errorMessage}',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: errorMessageColor ??
                    Theme.of(context).colorScheme.onTertiary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16),

          // رسالة توضيحية
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30),
            child: Text(
              'يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى',
              style: TextStyle(
                fontSize: 14,
                color:
                    Theme.of(context).colorScheme.onTertiary.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 40),

          // زر إعادة المحاولة
          if (showRTryButton)
            Container(
              width: MediaQuery.of(context).size.width * 0.5,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).primaryColor.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: onTapRetry as VoidCallback,
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.refresh_rounded,
                          color: Theme.of(context).scaffoldBackgroundColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'إعادة المحاولة',
                          style: TextStyle(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

// class ErrorContainer extends StatelessWidget {
//   const ErrorContainer({
//     required this.errorMessage,
//     required this.onTapRetry,
//     required this.showErrorImage,
//     super.key,
//     this.errorMessageColor,
//     this.topMargin = 0.1,
//     this.showBackButton,
//     this.showRTryButton = true,
//   });

//   final String errorMessage;
//   final Function onTapRetry;
//   final bool showErrorImage;
//   final bool showRTryButton;
//   final double topMargin;
//   final Color? errorMessageColor;
//   final bool? showBackButton;

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin:
//           EdgeInsets.only(top: MediaQuery.of(context).size.height * topMargin),
//       width: MediaQuery.of(context).size.width,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           if (showErrorImage) ...[
//             SvgPicture.asset(
//               Assets.error,
//               width: 200,
//               height: 200,
//             ),
//             const SizedBox(height: 25),
//           ],
//           Container(
//             alignment: Alignment.center,
//             padding: const EdgeInsets.symmetric(horizontal: 20),
//             child: Text(
//               '${context.tr(errorMessage) ?? errorMessage} :(',
//               style: TextStyle(
//                 fontSize: 18,
//                 color: errorMessageColor ??
//                     Theme.of(context).colorScheme.onTertiary,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           const SizedBox(height: 25),
//           if (showRTryButton)
//             CustomRoundedButton(
//               widthPercentage: 0.375,
//               backgroundColor: Theme.of(context).colorScheme.surface,
//               buttonTitle: context.tr(retryLbl),
//               radius: 5,
//               showBorder: false,
//               height: 40,
//               titleColor: Theme.of(context).colorScheme.onTertiary,
//               elevation: 5,
//               onTap: onTapRetry as VoidCallback,
//             )
//           else
//             const SizedBox(),
//         ],
//       ),
//     );
//   }
// }
