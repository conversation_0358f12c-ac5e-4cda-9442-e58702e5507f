import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/screens/subscription/subscription_manager.dart';
import 'package:flutterquiz/ui/widgets/premium_content_dialog.dart';
import 'package:flutterquiz/utils/constants/app_constants.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

/// ويدجت أزرار النتائج المحسن
class ResultButtons extends StatelessWidget {
  final ResultData resultData;
  final ResultStateData resultState;
  final ScreenshotController screenshotController;
  final VoidCallback onUpdateUserDetails;

  const ResultButtons({
    super.key,
    required this.resultData,
    required this.resultState,
    required this.screenshotController,
    required this.onUpdateUserDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // زر الإعادة أو المستوى التالي
        if (_shouldShowPlayAgainButton()) ...[
          _buildPlayAgainButton(context),
          const SizedBox(height: 15),
        ],

        // زر مراجعة الإجابات
        if (_shouldShowReviewButton()) ...[
          _buildReviewAnswersButton(context),
          const SizedBox(height: 15),
        ],

        // زر مشاركة النتائج
        _buildShareButton(context),
        const SizedBox(height: 15),

        // زر العودة للرئيسية
        _buildHomeButton(context),
        const SizedBox(height: 15),
      ],
    );
  }

  /// تحديد ما إذا كان يجب عرض زر الإعادة
  bool _shouldShowPlayAgainButton() {
    return resultData.quizType != QuizTypes.exam &&
        resultData.quizType != QuizTypes.oneVsOneBattle &&
        resultData.quizType != QuizTypes.selfChallenge &&
        resultData.quizType != QuizTypes.funAndLearn &&
        resultData.quizType != QuizTypes.contest;
  }

  /// تحديد ما إذا كان يجب عرض زر مراجعة الإجابات
  bool _shouldShowReviewButton() {
    return resultData.quizType == QuizTypes.quizZone ||
        resultData.quizType == QuizTypes.dailyQuiz ||
        resultData.quizType == QuizTypes.selfChallenge ||
        resultData.quizType == QuizTypes.funAndLearn ||
        resultData.quizType == QuizTypes.bookmarkQuiz;
  }

  /// بناء زر الإعادة أو المستوى التالي
  Widget _buildPlayAgainButton(BuildContext context) {
    if (resultData.quizType == QuizTypes.quizZone) {
      return _buildQuizZoneButton(context);
    }

    return _buildGenericPlayAgainButton(context);
  }

  /// بناء زر منطقة الاختبار (المستوى التالي أو إعادة اللعب)
  Widget _buildQuizZoneButton(BuildContext context) {
    if (resultState.isWinner) {
      final maxLevel = int.parse(resultData.subcategoryMaxLevel!);
      final currentLevel = int.parse(resultData.questions!.first.level!);

      if (maxLevel == currentLevel) {
        return const SizedBox.shrink(); // لا يوجد مستوى تالي
      }

      return _buildButton(
        context: context,
        title: 'المستوى التالي',
        icon: Icons.arrow_forward_rounded,
        color: Colors.orange,
        onTap: () => _handleNextLevel(context),
      );
    }

    // إعادة اللعب للمستوى الحالي
    return _buildButton(
      context: context,
      title: 'إعادة',
      icon: Icons.replay_rounded,
      color: Colors.green,
      onTap: () => _handlePlayAgain(context),
    );
  }

  /// بناء زر الإعادة العام
  Widget _buildGenericPlayAgainButton(BuildContext context) {
    return _buildButton(
      context: context,
      title: 'إعادة',
      icon: Icons.replay_rounded,
      color: Colors.green,
      onTap: () => _handlePlayAgain(context),
    );
  }

  /// بناء زر مراجعة الإجابات
  Widget _buildReviewAnswersButton(BuildContext context) {
    return _buildButton(
      context: context,
      title: 'مراجعة الإجابات',
      icon: Icons.quiz_rounded,
      color: Colors.purple,
      onTap: () => _handleReviewAnswers(context),
    );
  }

  /// بناء زر المشاركة
  Widget _buildShareButton(BuildContext context) {
    return _buildButton(
      context: context,
      title: 'مشاركة النتيجة',
      icon: Icons.share_rounded,
      color: Colors.deepOrange,
      onTap: () => _handleShare(context),
    );
  }

  /// بناء زر العودة للرئيسية
  Widget _buildHomeButton(BuildContext context) {
    return _buildButton(
      context: context,
      title: 'الرئيسية',
      icon: Icons.home_rounded,
      color: Colors.blue,
      onTap: () => _handleHome(context),
    );
  }

  /// بناء زر موحد
  Widget _buildButton({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.1),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          splashColor: color.withOpacity(0.2),
          highlightColor: color.withOpacity(0.1),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: color.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            child: Container(
              height: 60,
              width: MediaQuery.of(context).size.width * 0.85,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // أيقونة الزر
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 15),

                  // نص الزر
                  Text(
                    title,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// معالجة الانتقال للمستوى التالي
  Future<void> _handleNextLevel(BuildContext context) async {
    final currentLevel = int.parse(resultData.questions!.first.level!);

    // التحقق من المحتوى المميز
    if (resultData.isPremiumCategory && currentLevel >= maxMiumlever) {
      final isSubscribed =
          await SubscriptionManager.instance.checkSubscriptionStatus();

      if (!isSubscribed) {
        if (!context.mounted) return;
        showDialog(
          context: context,
          builder: (context) => const PremiumContentDialog(
            title: AppConstants.premiumContentTitle,
            message: AppConstants.premiumContentMessage,
          ),
        );
        return;
      }
    }

    final unlockedLevel = currentLevel == resultData.unlockedLevel
        ? (resultData.unlockedLevel! + 1)
        : resultData.unlockedLevel;

    if (!context.mounted) return;
    Navigator.of(context).pushReplacementNamed(
      Routes.quiz,
      arguments: {
        'numberOfPlayer': resultData.numberOfPlayers,
        'quizType': resultData.quizType,
        'categoryId': resultData.categoryId,
        'subcategoryId': resultData.subcategoryId,
        'level': (currentLevel + 1).toString(),
        'subcategoryMaxLevel': resultData.subcategoryMaxLevel,
        'unlockedLevel': unlockedLevel,
      },
    );
  }

  /// معالجة إعادة اللعب
  void _handlePlayAgain(BuildContext context) {
    onUpdateUserDetails();

    Navigator.of(context).pushReplacementNamed(
      Routes.quiz,
      arguments: {
        'numberOfPlayer': resultData.numberOfPlayers,
        'quizType': resultData.quizType,
        'categoryId': resultData.categoryId,
        'subcategoryId': resultData.subcategoryId,
        'level': resultData.questions?.first.level,
        'unlockedLevel': resultData.unlockedLevel,
        'subcategoryMaxLevel': resultData.subcategoryMaxLevel,
      },
    );
  }

  /// معالجة مراجعة الإجابات
  void _handleReviewAnswers(BuildContext context) {
    Navigator.of(context).pushNamed(
      Routes.reviewAnswers,
      arguments: {
        'quizType': resultData.quizType,
        'questions': resultData.questions,
        'guessTheWordQuestions': [], // معلق
      },
    );
  }

  /// معالجة المشاركة
  Future<void> _handleShare(BuildContext context) async {
    try {
      // حفظ appLink قبل العمليات غير المتزامنة
      final appLink = context.read<SystemConfigCubit>().appUrl;

      final image = await screenshotController.capture();
      if (image == null) return;

      final directory = await getTemporaryDirectory();
      final file = File(
          '${directory.path}/quiz_result_${DateTime.now().millisecondsSinceEpoch}.png');
      await file.writeAsBytes(image.buffer.asUint8List());

      final scoreText = '$appName\n'
          'نتيجتي في الاختبار: ${resultState.correctAnswers}/${resultState.totalQuestions}\n'
          'انضم إلينا وشارك في التحدي!\n'
          '$appLink';

      if (!context.mounted) return;
      await UiUtils.share(
        scoreText,
        files: [XFile(file.path)],
        context: context,
      );
    } catch (e) {
      if (!context.mounted) return;
      UiUtils.showSnackBar('حدث خطأ أثناء المشاركة', context);
    }
  }

  /// معالجة العودة للرئيسية
  void _handleHome(BuildContext context) {
    onUpdateUserDetails();
    Navigator.of(context).pushNamedAndRemoveUntil(
      Routes.home,
      (_) => false,
      arguments: false,
    );
  }
}
