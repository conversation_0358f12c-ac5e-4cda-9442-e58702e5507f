<svg id="_03" data-name="03" xmlns="http://www.w3.org/2000/svg" width="142.571" height="143.413" viewBox="0 0 142.571 143.413">
  <g id="Group_17202" data-name="Group 17202" transform="translate(13.169 17.797)">
    <g id="Group_17201" data-name="Group 17201">
      <path id="Path_18020" data-name="Path 18020" d="M50.339,127.754a1.368,1.368,0,0,0,.458.755c-1.129,1.284.726,3.156,1.936,1.9q4.26-4.439,8.513-8.884c4.434-4.476,8.968-8.858,13.55-13.166-1.594-.894-2.443-2.991-.655-4.447a11.482,11.482,0,0,1,10.968-2.223c.274-.278.549-.558.826-.836q4.874-4.932,9.745-9.865a1.235,1.235,0,0,0,.194-1.5c.936-.8,1.865-1.613,2.8-2.414q6.835-5.837,13.714-11.62a1.336,1.336,0,0,0,.142-1.849c.326-.268.649-.542.975-.81q7.038-5.755,14.25-11.3c1.4-1.071.142-3.459-1.426-2.443a247.964,247.964,0,0,0-25.144,19.113q4.414-4.41,8.832-8.816,4.826-4.8,9.648-9.6c1.029-1.026-.361-2.991-1.6-2.078l-4.582,3.362a1.516,1.516,0,0,0-2.162-.645q-10.271,6.83-19.936,14.586L108.629,58.74a1.276,1.276,0,0,0-.849-2.191,1.123,1.123,0,0,0-1.7-.978C99.8,59.7,93.8,64.168,87.993,68.87c-.71.355-1.42.713-2.123,1.081.187-.165.371-.329.555-.494l12.34-10.91c1.275-1.126-.565-2.891-1.846-1.846q-7.595,6.2-15.2,12.385Q88.36,63.045,95.08,57.091c.984-.868-.281-2.446-1.384-1.794-4.2,2.478-8.267,5.166-12.243,7.98q1.292-1.191,2.588-2.378c.829-.758-.229-2.043-1.175-1.523-7.609,4.2-14.182,9.671-20.533,15.441A161.415,161.415,0,0,1,74.9,61.412c1.165-1.1-.542-2.733-1.733-1.733-3.753,3.159-7.474,6.357-11.21,9.535-1.843,1.568-3.679,3.12-5.424,4.782q-2.7,2.57-5.4,5.14c-.4.384.142,1.133.607.787q2.982-2.207,5.96-4.411c.5-.374.991-.768,1.484-1.155a109.75,109.75,0,0,0-7.925,10.752,1.117,1.117,0,0,0,.874,1.662q-3.408,3.1-6.773,6.254c-1.194,1.123.6,2.9,1.794,1.794q5.867-5.16,11.6-10.468c7.4-6.486,15.024-12.727,23-18.448q-5.91,5.31-11.765,10.684a.806.806,0,0,0-1,.032A110.273,110.273,0,0,0,46.934,97.876a1.173,1.173,0,0,0,.629,1.93q-3.146,3.1-6.347,6.138c-1.223,1.158.584,2.917,1.833,1.833,11.73-10.2,23.334-20.542,34.96-30.862.568-.371,1.139-.742,1.713-1.107-4.989,4.347-9.849,8.848-14.634,13.44a.734.734,0,0,0-.871.065c-2.185,1.826-4.243,3.788-6.241,5.815-.987,1-1.949,2.02-2.849,3.1a24.494,24.494,0,0,0-2.549,3.417.523.523,0,0,0,.484.784,1.147,1.147,0,0,0,.5.381c-.384.429-.781.845-1.162,1.281q-4.782,5.45-9.155,11.233c-.784,1.036.887,2.243,1.768,1.365q6.7-6.7,13.453-13.363,5.16-4.777,10.362-9.51a1.341,1.341,0,0,0,1.43.791Q64.882,99.811,59.5,105q-2.319,2.023-4.6,4.1c-3.695,3.388-7.306,6.877-10.694,10.575a1.387,1.387,0,0,0,1.962,1.959q3.209-3.151,6.418-6.309-3.374,3.742-6.767,7.47a1.4,1.4,0,0,0,1.972,1.972c1.936-1.723,3.85-3.466,5.776-5.2-.2.239-.41.474-.61.716a62.946,62.946,0,0,0-7.551,10.655.637.637,0,0,0,1,.768c1.378-1.262,2.675-2.6,3.943-3.963Zm6.141-44.915A140.91,140.91,0,0,1,73.65,67.582c-5.728,5.079-11.478,10.139-17.17,15.257Zm23.266-3.5c3.295-3.075,6.6-6.141,9.971-9.126q2.44-1.965,4.927-3.872c.665-.507,1.336-1,2.007-1.5q-1.094,1.089-2.185,2.185-8.195,6.6-16.073,13.588c.452-.423.9-.852,1.352-1.271Zm-5.566,23.6a1.6,1.6,0,0,0,.177.184q-1.6,1.35-3.185,2.717c.687-.658,1.371-1.32,2.062-1.975C73.553,103.562,73.863,103.249,74.179,102.939Zm-3.521-4.147c.284-.232.571-.461.855-.691a1.786,1.786,0,0,0,.555.587q-3.529,3.238-7.06,6.476,2.822-3.185,5.647-6.373Z" transform="translate(-40.809 -55.144)" fill="#eac485"/>
      <path id="Path_18021" data-name="Path 18021" d="M171.129,128.348c.407-.7-.29-1.865-1.158-1.507a16.264,16.264,0,0,0-3.93,2.74c-1.326,1.094-2.636,2.214-3.934,3.34q-3.8,3.3-7.557,6.657-3.049,2.74-6.041,5.55-6.336,5.179-12.666,10.365c6.622-6.964,13.527-13.666,20.52-20.258q5.053-4.758,10.145-9.474c1.4-1.3-.662-3.314-2.088-2.088q-1.825,1.573-3.65,3.143c1.175-1.065,2.349-2.123,3.517-3.195,1.723-1.581,3.743-3.188,4.879-5.266.223-.407-.155-1.049-.658-.855-2.146.829-3.953,2.517-5.708,3.972-1.81,1.5-3.614,3-5.4,4.534-2.685,2.3-5.347,4.618-8.012,6.935,2.056-2.007,4.082-4.043,6.018-6.147a94.587,94.587,0,0,0,9.958-12.646c.881-1.365-1.029-2.636-2.146-1.655q-7.352,6.423-14.708,12.85c2.256-2.136,4.5-4.276,6.783-6.386q5.658-5.242,11.4-10.391c1.375-1.233-.629-3.179-2.01-2.01q-18.093,15.3-36.174,30.6l19.094-19.229q5.963-6.007,11.93-12.014c1.323-1.333-.681-3.32-2.056-2.056q-9.618,8.858-19.236,17.719a3.018,3.018,0,0,1,1.058,4.037c-.755,1.262-2.749,2.017-4.037,1.058a6.83,6.83,0,0,0-1.575-.9q-1.733,1.6-3.469,3.2a.833.833,0,0,0-.139-.161c1.075-1.055,2.156-2.1,3.233-3.146a4.308,4.308,0,0,0-4.247,1.217c-2.853,2.491-7.038-1.672-4.172-4.172,3.792-3.311,8.642-4.3,12.917-1.42q4.061-3.921,8.164-7.793,6.81-6.433,13.672-12.814c1.117-1.042-.4-3.12-1.672-2.165-7.483,5.628-14.653,11.639-21.646,17.854q6.07-5.973,12.291-11.785,5.45-5.107,10.942-10.168c1.291-1.194-.571-2.914-1.885-1.885l-6.16,4.834.565-.507c1.507-1.355-.671-3.414-2.191-2.191-6.925,5.576-13.55,11.53-20.162,17.474q-7.449,6.7-14.647,13.666-2.977,2.74-5.87,5.573a1.43,1.43,0,0,0-.455-1.488q9.41-8.785,18.829-17.564,7.241-6.762,14.486-13.521a1.487,1.487,0,0,0,.052-2.065q5.5-4.235,11.036-8.435c1.087-.826.1-2.675-1.1-1.885a350.513,350.513,0,0,0-45.819,36.051c.565-.607,1.123-1.217,1.688-1.82,7.925-8.245,16.322-16.076,24.918-23.576,1.336-1.126,2.675-2.249,4.024-3.356a1.539,1.539,0,0,0,.342-.4c2.782-2.365,5.586-4.7,8.429-6.993,1.449-1.165-.61-3.211-2.046-2.046q-4.347,3.533-8.6,7.18a1.514,1.514,0,0,0-.236.148q-5.3,4.095-10.423,8.409,1.873-1.709,3.743-3.421a1.265,1.265,0,0,0,.455-.994c.487-.394.968-.8,1.459-1.191,2.523-2.007,5.063-4,7.574-6.021q1.878-1.51,3.782-2.995a17.219,17.219,0,0,0,3.614-3.027c.278-.381-.181-.907-.6-.781a15.919,15.919,0,0,0-4.014,2.511q-1.98,1.38-3.937,2.794c-2.62,1.891-5.163,3.908-7.645,5.983-.532.445-1.052.9-1.578,1.349a1.4,1.4,0,0,0-1.126.355q-9.889,8.5-19.361,17.47c-2.63,2.5-5.266,5.037-7.838,7.632a26.927,26.927,0,0,1,3.385,1.226c3.479,1.51.471,6.593-2.978,5.1a9.207,9.207,0,0,0-5.431-1.036c-.926,1.023-1.846,2.052-2.736,3.1a1.219,1.219,0,0,0-.281-.658q1.065-1.084,2.136-2.165a5.071,5.071,0,0,0-1.22.739,3.039,3.039,0,0,1-3.437.323q-7.44,7.512-14.924,14.983c-3.272,3.262-6.6,6.473-9.813,9.8a1.471,1.471,0,0,0,.181,2.259c-.923.981-1.843,1.965-2.74,2.969-1.068,1.2.332,3.353,1.81,2.346a19.7,19.7,0,0,0,5.408-5.512c2.927-3.124,5.8-6.305,8.716-9.416q5.813-6.2,11.63-12.391a.7.7,0,0,0,.284.665c-.378.474-.739.962-1.11,1.439-.077.084-.155.165-.232.248-2.343,2.552-4.627,5.15-6.809,7.841-1.078,1.329-2.146,2.669-3.188,4.024a18.214,18.214,0,0,0-1.139,1.733q-1.51,1.873-3,3.766c-2.514,3.2-4.973,6.454-7.332,9.771-.984,1.384,1.133,2.878,2.288,1.765q4.9-4.715,9.778-9.458c-.055.058-.1.126-.155.184-1.175,1.278-2.3,2.569-3.362,3.934l-.055.048a.047.047,0,0,1,.01.006c-.087.11-.181.213-.265.326-1.139,1.5,1.142,3.114,2.472,2.078q-1.849,2.28-3.634,4.611c-.013.013-.023.026-.035.039h0c-.081.107-.165.207-.242.313-1.055,1.388,1.275,3.182,2.42,1.868q3.34-3.824,6.741-7.586-3.892,5.242-7.551,10.658c-.907,1.342,1.226,3.03,2.272,1.752q8.519-10.426,17.667-20.32c-.171.232-.352.455-.523.687a128.222,128.222,0,0,0-7.209,10.91,1.333,1.333,0,0,0-.181.591c-1.933,2.42-3.779,4.911-5.5,7.493-.307.461.387.991.765.591.223-.236.439-.474.658-.71-.594.742-1.2,1.478-1.8,2.223-1.249,1.568.862,3.814,2.256,2.256q2.73-3.054,5.466-6.1a1.619,1.619,0,0,0,1.53.713c-.084.1-.171.2-.252.3a39.2,39.2,0,0,0-5.089,7.228.593.593,0,0,0,.926.716,67.089,67.089,0,0,0,6-6.409c1.949-2.2,3.934-4.366,5.938-6.522,1.988-2.139,3.985-4.276,6.015-6.383-.01.048-.035.094-.042.142-2.665,2.943-5.3,5.918-7.78,9.019a93.166,93.166,0,0,0-7.9,11.333,1.521,1.521,0,0,0,2.378,1.836c2.172-2.1,4.327-4.221,6.489-6.334a1.543,1.543,0,0,0,2.511.691l4.518-3.908-6.457,6.438c-1.271,1.265.629,3.117,1.952,1.952s2.646-2.33,3.966-3.495c-.536.694-1.078,1.384-1.6,2.085a1.5,1.5,0,0,0,.39,1.949,1.542,1.542,0,0,0,1.985-.119q6.888-5.4,13.779-10.781-6.21,6.428-12.424,12.856c-1.268,1.307.6,3.066,1.959,1.959q4.482-3.654,8.939-7.338c-.926,1.278-1.807,2.594-2.62,3.959-.678,1.136.765,2.833,1.939,1.939q4.318-3.287,8.69-6.5l-5.779,5.3A1.421,1.421,0,0,0,120,183.838q6.53-4.206,13.03-8.464c-1.985,2.088-3.924,4.218-5.763,6.438-.9,1.091.31,3.137,1.652,2.143q7.227-5.349,14.063-11.188a1.223,1.223,0,0,0,.561-.3c1.2-1.11,2.414-2.207,3.627-3.3q3.011-2.667,5.941-5.421a1.223,1.223,0,0,0-.136-1.926q3.558-3.156,6.935-6.522a1.484,1.484,0,0,0-2.1-2.1q-3.567,3.567-7.412,6.844c6.354-6.838,12.682-13.695,19.081-20.488,1.355-1.436-.642-3.3-2.12-2.12q-10.8,8.645-21.611,17.283,9.84-9.8,19.684-19.591a1.347,1.347,0,0,0,.381-1.462c.639-.574,1.281-1.139,1.917-1.717a16.626,16.626,0,0,0,3.385-3.595ZM79.624,147.635q2.033-2.522,4.127-4.989a1.284,1.284,0,0,0,1.233-.429q5.687-6.447,11.681-12.588a1.662,1.662,0,0,0,.7.936q-8.882,8.524-17.745,17.07Zm27.935-2.962a.523.523,0,0,0,.032-.058c.958-.913,1.9-1.836,2.853-2.762a1.387,1.387,0,0,0,.474.52q-2.653,2.29-5.247,4.644c.636-.778,1.281-1.546,1.891-2.339Zm-5.089,19.978q-1.21,1.171-2.423,2.343c2.314-2.769,4.718-5.466,7.138-8.154l.858-.726C106.082,160.2,104.216,162.379,102.471,164.651Zm9.619,5.382q.862-.978,1.736-1.939a1.312,1.312,0,0,0,.142.487q-.939.726-1.878,1.455Zm-7.025-3.766q4.506-4.443,9.016-8.884a1.565,1.565,0,0,0,.875.461Q110.014,162.056,105.065,166.267Zm8.855-29.852-.9.658c1.062-1.11,2.12-2.22,3.191-3.317q-1.152,1.321-2.291,2.659Zm40.307-12.388c.052-.045.1-.09.152-.132-6.712,7.425-14.408,13.982-21.682,20.92a1.473,1.473,0,0,0-1.384-.655l22.911-20.133Zm-29.042,32.8a1.115,1.115,0,0,0,.09-.084.566.566,0,0,0,.252-.107c1.013-.707,2.033-1.388,3.046-2.085q-1.641,1.413-3.275,2.824-1,.862-2,1.73Q124.218,157.95,125.185,156.829Zm-1.772,15.8a1.4,1.4,0,0,0-.455,1.265c-.616.452-1.229.9-1.849,1.352a110.5,110.5,0,0,1,11.142-11.391q-4.419,4.385-8.839,8.774Zm28.661-19.074q-7.7,8.069-15.325,16.209c-1.523.975-3.043,1.956-4.566,2.927q6.05-5.566,12.1-11.133a1.336,1.336,0,0,0,.077-1.814q3.858-3.1,7.716-6.189Z" transform="translate(-54.238 -73.043)" fill="#eac485"/>
    </g>
    <path id="Path_18022" data-name="Path 18022" d="M238.321,286.782a5.685,5.685,0,0,0-1.9-1.826c-.552-.342-1.187-.568-1.723-.933a3.671,3.671,0,0,1,.265-.9c.229-.139.465-.271.684-.413a5.155,5.155,0,0,0,2.723-4.056,3.574,3.574,0,0,0-3.017-3.663,6.348,6.348,0,0,0-2.44.139c-.639-.442-1.284-.874-1.952-1.252a1.876,1.876,0,0,0-1.894,0A1.719,1.719,0,0,0,228.2,275a1.882,1.882,0,0,0,.484,1.826c.126.1.261.187.39.287a1.443,1.443,0,0,0,.558.787,3.118,3.118,0,0,0,1.359.555c.155.1.31.207.465.313.371.248.736.526,1.12.755.171.1.226.165.242.168a1.427,1.427,0,0,1-.148.52,4.48,4.48,0,0,0-1.271,1.007,3.447,3.447,0,0,0-.4,3.9,6.646,6.646,0,0,0,.723,2.562c.336.662,1,1.368,1.016,2.143a1.893,1.893,0,0,1-1.081,1.62,6.533,6.533,0,0,0-3.185,1.423,1.917,1.917,0,0,0-.152,2.533,1.972,1.972,0,0,0,2.488.5c.326-.142.649-.294.968-.449.981-.278,2.11-.2,3.078-.449a5.691,5.691,0,0,0,3.824-3.056,5.508,5.508,0,0,0-.355-5.16Z" transform="translate(-167.539 -203.122)" fill="#eb8486"/>
    <path id="Path_18023" data-name="Path 18023" d="M273.638,202.571c-.549-1.833-2.3-2.62-4.069-2.869a10.26,10.26,0,0,0-4.8.413,8.884,8.884,0,0,0-4.582,3.2,1.569,1.569,0,0,0,.407,2.033,1.61,1.61,0,0,0,2.072-.123c.094-.074.177-.158.265-.239a1.721,1.721,0,0,0,.4.29,10.129,10.129,0,0,0,3.433.681c.958.116,1.917.21,2.878.3a4.042,4.042,0,0,0,3.256-.7,2.846,2.846,0,0,0,.739-2.988Z" transform="translate(-189.251 -152.978)" fill="#75757c"/>
    <path id="Path_18024" data-name="Path 18024" d="M160.484,200.954a2.008,2.008,0,0,0-1.41-1.849,1.373,1.373,0,0,0-.394-.11c-.513-.136-.652-.142-.419-.016-.045-.006-.094-.023-.139-.032a12.488,12.488,0,0,0-3.33-1.381,14.1,14.1,0,0,0-5.815-.361,4.649,4.649,0,0,0-2.746,1.1,2.673,2.673,0,0,0-.187,3.462A3.234,3.234,0,0,0,149.451,203c.936-.184,1.8-.642,2.736-.855.187-.042.336-.074.474-.1a8.74,8.74,0,0,0,1.817.794,7.7,7.7,0,0,0,3.785.161,1.617,1.617,0,0,0,1.72-.752c.01-.019.013-.039.023-.058a1.733,1.733,0,0,0,.474-1.233Z" transform="translate(-111.716 -151.261)" fill="#75757c"/>
  </g>
  <g id="Group_17203" data-name="Group 17203">
    <path id="Path_18025" data-name="Path 18025" d="M99.018,278.361c-20.994-.936-50.614-13.324-59.778-43.605a26.094,26.094,0,0,1-1.036-4.711,2.059,2.059,0,0,1,1.181-1.552,1.973,1.973,0,0,1,1.81.539,14.215,14.215,0,0,1,1.507,3.408c8.164,25.454,27.051,37.532,52.2,41.575a33.13,33.13,0,0,0,20.455-3.511A77.449,77.449,0,0,0,143.8,246.166c4.811-6.725,7.225-14.434,8.732-22.469.465-2.475.784-4.986,1.42-7.412a2.4,2.4,0,0,1,1.943-1.317c.623.084,1.229,1.068,1.62,1.778.223.4.035,1.055-.029,1.591-1.817,14.87-6.828,28.252-17.732,39.042a90.776,90.776,0,0,1-21.488,16.176,41.965,41.965,0,0,1-19.255,4.8Z" transform="translate(-25.874 -145.593)" fill="#262c54"/>
    <path id="Path_18026" data-name="Path 18026" d="M0,78.018c1.539-26.932,14.921-48.91,41.724-62.263a21.312,21.312,0,0,1,5.069-1.6c.394-.084,1.146.745,1.446,1.307a1.492,1.492,0,0,1-.394,1.407,27.5,27.5,0,0,1-3.7,2.123C22.7,29.272,9.332,45.994,5.218,69.415c-3.734,21.256,1.868,40.252,15.963,56.674a26.009,26.009,0,0,1,2.769,3.285,2.485,2.485,0,0,1-.065,2.333,2.347,2.347,0,0,1-2.3.2,19.889,19.889,0,0,1-3.514-3.3C6.121,114.876.252,98.861,0,78.018Z" transform="translate(0 -9.582)" fill="#262c54"/>
    <path id="Path_18027" data-name="Path 18027" d="M374.819,119.953c-.358,15.88-3.814,29.452-12.353,41.453A49.29,49.29,0,0,1,347.19,175.65c-2.2,1.288-3.753,1.165-4.166-.171-.584-1.888.974-2.5,2.146-3.24,13.324-8.364,20.575-20.846,23.837-35.809,3.959-18.164,1.291-35.4-8.377-51.385-1.572-2.6-3.64-4.9-5.45-7.357A16.012,16.012,0,0,1,353.46,75a1.543,1.543,0,0,1,.355-1.449c.516-.355,1.52-.713,1.875-.455a19.265,19.265,0,0,1,3.553,3.22c8.3,9.945,12.882,21.517,14.7,34.234.51,3.553.674,7.154.878,9.4Z" transform="translate(-232.248 -49.446)" fill="#262c54"/>
    <path id="Path_18028" data-name="Path 18028" d="M187.9,39.61c19.452,1.655,36.316,7.383,48.213,23.443,3.966,5.353,8.051,10.613,10.142,17.048a18.389,18.389,0,0,1,.771,4.708c.023.432-.823,1.071-1.4,1.307a1.529,1.529,0,0,1-1.375-.523A18.35,18.35,0,0,1,242.9,82.7c-7.909-19.342-21.156-32.611-42.111-37.245a57.026,57.026,0,0,0-19.268-1.117c-.891.106-1.778.261-2.665.407-1.355.219-2.743.065-3.017-1.452-.281-1.559,1.055-2.149,2.362-2.346,3.366-.507,6.741-.933,9.694-1.333Z" transform="translate(-119.073 -26.825)" fill="#262c54"/>
    <path id="Path_18029" data-name="Path 18029" d="M220.212,267.748c6.773-.077,10.381,4.321,8.842,9.645-.629,2.181-.939,3.83.432,5.918,3,4.569-1.736,10.749-6.089,10.568A90.978,90.978,0,0,1,212.8,292.44a1.535,1.535,0,0,1-.939-1.175c.045-.626.41-1.649.826-1.755a15.571,15.571,0,0,1,4.631-.574c1.768.1,3.5.829,5.27.929a3.367,3.367,0,0,0,3.546-2.72c.458-1.668-.526-2.775-1.807-3.54-.907-.542-2.039-.694-2.969-1.207-1.565-.862-1.575-2.417-.032-3.35.755-.458,1.684-.616,2.456-1.052a3.036,3.036,0,0,0,1.378-3.724,2.983,2.983,0,0,0-3.127-2.459,31.986,31.986,0,0,0-5.2,1.133,42.093,42.093,0,0,1-5.182,1.236,2.483,2.483,0,0,1-1.994-1.32,2.318,2.318,0,0,1,1.01-2.062c3.524-1.259,7.122-2.3,9.542-3.053Z" transform="translate(-141.983 -181.345)" fill="#262c54"/>
    <path id="Path_18030" data-name="Path 18030" d="M255.881,188.583c7.593.226,12.056,7.357,12.065,13.395,0,1.923-1.849,2.924-3.34,1.617-5.389-4.708-11.23-1.784-16.912-1.217-.655.065-1.126,1.31-1.823,1.81a1.775,1.775,0,0,1-2.962-1.53c.487-7.215,5.46-13.676,12.972-14.079Zm.361,8.274a26.691,26.691,0,0,0,3.35.129,8.95,8.95,0,0,0,1.8-.61,5.525,5.525,0,0,0-.878-1.791c-3.114-2.811-7.277-2.472-10.371.839a6.384,6.384,0,0,0-.833,1.852,6.992,6.992,0,0,0,1.855.3c2.1-.236,4.182-.584,5.079-.713Z" transform="translate(-164.521 -127.724)" fill="#262c54"/>
    <path id="Path_18031" data-name="Path 18031" d="M154.712,197.743c.071,3.456-1.275,4.808-3.417,3.614-5.789-3.23-11.759-2.491-17.813-1.391-2.546.465-3.314-.194-3.059-2.653.826-7.99,10.429-12.824,17.712-8.8,3.821,2.11,6.257,5.321,6.576,9.229Zm-12.8-6.938c-1.53.755-3.414,1.633-5.228,2.636-.342.19-.41.884-.6,1.346a4.649,4.649,0,0,0,1.329.436c2.652,0,5.305-.09,7.958-.084,1.2,0,2.407.168,3.611.258-.019-1.949-1.546-2.707-2.927-3.4A24.691,24.691,0,0,0,141.914,190.805Z" transform="translate(-88.305 -126.7)" fill="#262c54"/>
    <path id="Path_18032" data-name="Path 18032" d="M36.783,148.254c.949-11.314,3.759-21.788,9.71-31.285.381-.607.691-1.4,1.255-1.717a12.327,12.327,0,0,1,2.791-.833,19.023,19.023,0,0,1,.352,2.9c-.01.468-.487.936-.774,1.391-5.666,9.019-8.442,18.948-9.29,29.481-.058.716.152,1.559-.165,2.12a11.737,11.737,0,0,1-1.943,2.117,13.354,13.354,0,0,1-1.8-2.307,4.591,4.591,0,0,1-.136-1.875Z" transform="translate(-24.889 -77.495)" fill="#262c54"/>
    <path id="Path_18033" data-name="Path 18033" d="M194.822.068A60.038,60.038,0,0,1,215.8,3.944c1.42.532,3.556.984,2.9,3.12-.71,2.307-2.594,1.178-4.043.668C205.571,4.55,196.309,3.059,186.7,4.541a3.432,3.432,0,0,1-1.581-.035c-.445-.152-1.068-.578-1.11-.939-.068-.632.071-1.73.439-1.888A20.924,20.924,0,0,1,189.468.123a37.044,37.044,0,0,1,5.357-.058Z" transform="translate(-124.617 0.009)" fill="#262c54"/>
    <path id="Path_18034" data-name="Path 18034" d="M121.63,147.843c2.294.565,5.8,1.313,9.206,2.365.607.187.816,1.642,1.213,2.514a6.914,6.914,0,0,1-2.281.894,12.463,12.463,0,0,1-3.159-.652,23.289,23.289,0,0,0-18.69,2.514c-1.233.745-2.656,1.978-3.74.352-1.2-1.8.461-2.969,1.791-3.872,4.331-2.946,9.316-3.611,15.66-4.118Z" transform="translate(-70.28 -100.131)" fill="#262c54"/>
    <path id="Path_18035" data-name="Path 18035" d="M231.28,421.725c-.436-.3-1.726-.72-1.965-1.468-.481-1.523.813-2.146,2.136-2.414,1.752-.355,3.511-.7,5.276-.994,6.021-1.007,11.972-2.156,17.151-5.757.616-.429,1.872.058,2.833.123-.2.962-.1,2.191-.662,2.827a12.624,12.624,0,0,1-3.462,2.485c-3.7,2.023-16.535,5.182-21.307,5.2Z" transform="translate(-155.248 -278.316)" fill="#262c54"/>
    <path id="Path_18036" data-name="Path 18036" d="M265.946,143.7c2.643.471,5.334.771,7.916,1.471a20.281,20.281,0,0,1,4.815,2.317c1.139.674,1.981,1.852,1.223,3.166-.836,1.452-2.091.736-3.282.2a27.068,27.068,0,0,0-5.967-2.33c-4.066-.736-8.219-1.213-11.923,1.507-1.139.836-2.5.939-3.262-.4s.161-2.4,1.278-3.137a16.451,16.451,0,0,1,9.2-2.8Z" transform="translate(-172.84 -97.326)" fill="#262c54"/>
    <path id="Path_18037" data-name="Path 18037" d="M158.688,424.61c-4.853-1.007-9.216-1.884-13.559-2.84a3.155,3.155,0,0,1-1.82-1.036,3.433,3.433,0,0,1-.6-2.291c.065-.4,1.155-.987,1.678-.894,5.076.913,10.142,1.9,15.176,3.03.623.139,1.584,1.607,1.436,1.833a12.381,12.381,0,0,1-2.307,2.2Z" transform="translate(-96.637 -282.8)" fill="#262c54"/>
    <path id="Path_18038" data-name="Path 18038" d="M117.681,61.622a8.432,8.432,0,0,1-1.481,1.81c-3.753,2.7-7.519,5.392-11.394,7.912-.678.439-1.891.055-2.856.052.132-.962-.074-2.22.458-2.84a32.091,32.091,0,0,1,12.185-8.49c1.226-.516,2.472-.336,3.088,1.555Z" transform="translate(-69.052 -40.512)" fill="#262c54"/>
  </g>
</svg>
