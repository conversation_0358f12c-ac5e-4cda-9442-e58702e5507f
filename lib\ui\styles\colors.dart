import 'package:flutter/material.dart';

// ========================================
// 🎨 نظام الألوان المحسن للتطبيق
// ========================================

// ========================================
// 🌞 ألوان الثيم الفاتح (Light Theme)
// ========================================

/// الخلفيات والأسطح
const klBackgroundColor = Color(0xffF8FAFC); // خلفية فاتحة ونظيفة
const klCanvasColor = Color(0xffF1F5F9); // لون القماش الفاتح
const klPageBackgroundColor = Color(0xffE2E8F0); // خلفية الصفحة الرئيسية
const klSurfaceColor = Color(0xffFFFFFF); // لون السطح الأبيض
const klCardColor = Color(0xffFFFFFF); // لون البطاقات

/// الألوان الأساسية
const klPrimaryColor = Color(0xff0C73E9); // اللون الأساسي الأزرق الجميل
const klSecondaryColor = Color(0xff6366F1); // لون ثانوي بنفسجي
const klAccentColor = Color(0xff10B981); // لون مميز أخضر

/// ألوان النصوص
const klPrimaryTextColor = Color(0xff1E293B); // نص أساسي داكن
const klSecondaryTextColor = Color(0xff64748B); // نص ثانوي رمادي
const klHintTextColor = Color(0xff94A3B8); // نص التلميحات

/// ألوان الحدود والفواصل
const klBorderColor = Color(0xffE2E8F0); // لون الحدود
const klDividerColor = Color(0xffCBD5E1); // لون الفواصل

// ========================================
// 🌙 ألوان الثيم الداكن (Dark Theme)
// ========================================

/// الخلفيات والأسطح
const kdBackgroundColor = Color(0xff0F172A); // خلفية داكنة عميقة
const kdCanvasColor = Color(0xff1E293B); // لون القماش الداكن
const kdPageBackgroundColor = Color(0xff0F172A); // خلفية الصفحة الداكنة
const kdSurfaceColor = Color(0xff1E293B); // لون السطح الداكن
const kdCardColor = Color(0xff334155); // لون البطاقات الداكن

/// الألوان الأساسية
const kdPrimaryColor = Color(0xff3B82F6); // اللون الأساسي الأزرق المضيء
const kdSecondaryColor = Color(0xff8B5CF6); // لون ثانوي بنفسجي مضيء
const kdAccentColor = Color(0xff10B981); // لون مميز أخضر مضيء

/// ألوان النصوص
const kdPrimaryTextColor = Color(0xffF8FAFC); // نص أساسي فاتح
const kdSecondaryTextColor = Color(0xffCBD5E1); // نص ثانوي رمادي فاتح
const kdHintTextColor = Color(0xff64748B); // نص التلميحات الداكن

/// ألوان الحدود والفواصل
const kdBorderColor = Color(0xff334155); // لون الحدود الداكن
const kdDividerColor = Color(0xff475569); // لون الفواصل الداكن

// ========================================
// 🎯 الألوان المشتركة (Common Colors)
// ========================================

/// ألوان الحالات
const kSuccessColor = Color(0xff10B981); // أخضر للنجاح
const kErrorColor = Color(0xffEF4444); // أحمر للخطأ
const kWarningColor = Color(0xffF59E0B); // برتقالي للتحذير
const kInfoColor = Color(0xff3B82F6); // أزرق للمعلومات

/// ألوان خاصة بالتطبيق
const kCorrectAnswerColor = Color(0xff10B981); // أخضر للإجابة الصحيحة
const kWrongAnswerColor = Color(0xffEF4444); // أحمر للإجابة الخاطئة
const kPendingColor = Color(0xffF59E0B); // برتقالي للانتظار
const kBadgeLockedColor = Color(0xff94A3B8); // رمادي للعناصر المقفلة
const kHurryUpTimerColor = Color(0xffEF4444); // أحمر للوقت المنتهي

/// ألوان التدرجات الجميلة
const kGradientStart = Color(0xff667EEA); // بداية التدرج
const kGradientEnd = Color(0xff764BA2); // نهاية التدرج

/// ألوان شفافة مفيدة
const kTransparentWhite = Color(0x80FFFFFF); // أبيض شفاف
const kTransparentBlack = Color(0x80000000); // أسود شفاف
const kTransparentPrimary = Color(0x200C73E9); // أزرق شفاف

// ========================================
// 💫 ألوان شفافة للثيم الفاتح
// ========================================

/// ألوان شفافة للثيم الفاتح
const klPrimaryTransparent10 = Color(0x1A0C73E9); // 10% شفافية
const klPrimaryTransparent20 = Color(0x330C73E9); // 20% شفافية
const klPrimaryTransparent30 = Color(0x4D0C73E9); // 30% شفافية

/// ألوان شفافة عامة
const kWhiteTransparent10 = Color(0x1AFFFFFF); // أبيض 10%
const kWhiteTransparent20 = Color(0x33FFFFFF); // أبيض 20%
const kWhiteTransparent30 = Color(0x4DFFFFFF); // أبيض 30%

const kBlackTransparent10 = Color(0x1A000000); // أسود 10%
const kBlackTransparent20 = Color(0x33000000); // أسود 20%
const kBlackTransparent30 = Color(0x4D000000); // أسود 30%

// ========================================
// 💫 ألوان شفافة للثيم الداكن
// ========================================

/// ألوان شفافة للثيم الداكن
const kdPrimaryTransparent10 = Color(0x1A3B82F6); // 10% شفافية
const kdPrimaryTransparent20 = Color(0x333B82F6); // 20% شفافية
const kdPrimaryTransparent30 = Color(0x4D3B82F6); // 30% شفافية
