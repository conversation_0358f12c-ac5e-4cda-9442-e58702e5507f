import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/audioQuestionBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
// تم إزالة import لأن اختبارات تخمين الكلمة لم تعد مدعومة
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
// تم إزالة import لأن اختبارات تخمين الكلمة لم تعد مدعومة
import 'package:flutterquiz/features/quiz/cubits/questionsCubit.dart';
// تم إزالة import لأن اختبارات تخمين الكلمة لم تعد مدعومة
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/features/systemConfig/model/answer_mode.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/audioQuestionContainer.dart';
// تم إزالة import لأن اختبارات تخمين الكلمة لم تعد مدعومة
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/exitGameDialog.dart';
import 'package:flutterquiz/ui/widgets/questionsContainer.dart';
import 'package:flutterquiz/ui/widgets/text_circular_timer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/explanation_dialog.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/app/routes.dart';

class BookmarkQuizScreen extends StatefulWidget {
  const BookmarkQuizScreen({required this.quizType, super.key});

  final QuizTypes quizType;

  @override
  State<BookmarkQuizScreen> createState() => _BookmarkQuizScreenState();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<QuestionsCubit>(
            create: (_) => QuestionsCubit(QuizRepository()),
          ),
          // تم إزالة GuessTheWordQuizCubit لأن اختبارات تخمين الكلمة لم تعد مدعومة
          BlocProvider<UpdateBookmarkCubit>(
            create: (_) => UpdateBookmarkCubit(BookmarkRepository()),
          ),
        ],
        child: BookmarkQuizScreen(
          quizType: routeSettings.arguments! as QuizTypes,
        ),
      ),
    );
  }
}

class _BookmarkQuizScreenState extends State<BookmarkQuizScreen>
    with TickerProviderStateMixin {
  late AnimationController questionAnimationController;
  late AnimationController questionContentAnimationController;
  late AnimationController timerAnimationController = AnimationController(
    vsync: this,
    duration: Duration(
      seconds:
          context.read<SystemConfigCubit>().quizTimer(QuizTypes.bookmarkQuiz),
    ),
  )..addStatusListener(currentUserTimerAnimationStatusListener);
  late Animation<double> questionSlideAnimation;
  late Animation<double> questionScaleUpAnimation;
  late Animation<double> questionScaleDownAnimation;
  late Animation<double> questionContentAnimation;
  late AnimationController animationController;
  late AnimationController topContainerAnimationController;
  int currentQuestionIndex = 0;

  bool completedQuiz = false;

  //to track if setting dialog is open
  bool isSettingDialogOpen = false;

  bool isExitDialogOpen = false;

  // تم إزالة guessTheWordQuestionContainerKeys لأن اختبارات تخمين الكلمة لم تعد مدعومة

  late List<GlobalKey<AudioQuestionContainerState>> audioQuestionContainerKeys =
      [];

  late AnimationController showOptionAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 500),
  );

  // متغيرات أدوات المساعدة
  Map<String, bool> questionsUsingHelp = {};
  Map<String, bool> eliminatedQuestions = {};

  void _getQuestions() {
    /// Fetch Questions from Local Bookmark Cubits to Questions Cubit
    Future.delayed(Duration.zero, () {
      if (widget.quizType == QuizTypes.audioQuestions) {
        final bookmarkedQuestions = context
            .read<AudioQuestionBookmarkCubit>()
            .questions()
            .map((e) => e.copyWith(submittedAnswer: '', attempted: false))
            .toList(growable: false);

        context.read<QuestionsCubit>().updateState(
              QuestionsFetchSuccess(
                questions: bookmarkedQuestions,
                currentPoints: 0,
                quizType: QuizTypes.bookmarkQuiz,
              ),
            );

        for (final _ in bookmarkedQuestions) {
          audioQuestionContainerKeys
              .add(GlobalKey<AudioQuestionContainerState>());
        }
      } else if (widget.quizType == QuizTypes.quizZone) {
        final bookmarkedQuestions = context
            .read<BookmarkCubit>()
            .questions()
            .map((e) => e.copyWith(submittedAnswer: '', attempted: false))
            .toList(growable: false);

        context.read<QuestionsCubit>().updateState(
              QuestionsFetchSuccess(
                questions: bookmarkedQuestions,
                currentPoints: 0,
                quizType: QuizTypes.bookmarkQuiz,
              ),
            );
        timerAnimationController.forward();
        // تم إزالة الشرط المتعلق بـ GuessTheWord لأن اختبارات تخمين الكلمة لم تعد مدعومة
      }
    });
  }

  @override
  void initState() {
    initializeAnimation();
    _getQuestions();
    super.initState();
  }

  void initializeAnimation() {
    questionContentAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    )..forward();
    questionAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 525),
    );
    questionSlideAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    questionScaleUpAnimation = Tween<double>(begin: 0, end: 0.1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0, 0.5, curve: Curves.easeInQuad),
      ),
    );
    questionContentAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionContentAnimationController,
        curve: Curves.easeInQuad,
      ),
    );
    questionScaleDownAnimation = Tween<double>(begin: 0, end: 0.05).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0.5, 1, curve: Curves.easeOutQuad),
      ),
    );
  }

  void toggleSettingDialog() {
    isSettingDialogOpen = !isSettingDialogOpen;
  }

  //change to next Question
  void changeQuestion() {
    questionAnimationController.forward(from: 0).then((value) {
      //need to dispose the animation controllers
      questionAnimationController.dispose();
      questionContentAnimationController.dispose();
      //initializeAnimation again
      setState(() {
        initializeAnimation();
        currentQuestionIndex++;
      });
      //load content(options, image etc) of question
      questionContentAnimationController.forward();
    });
  }

  //if user has submitted the answer for current question
  bool hasSubmittedAnswerForCurrentQuestion() {
    // تم إزالة الشرط المتعلق بـ GuessTheWord لأن اختبارات تخمين الكلمة لم تعد مدعومة
    return context
        .read<QuestionsCubit>()
        .questions()[currentQuestionIndex]
        .attempted;
  }

  Future<void> submitAnswer(String submittedAnswer) async {
    timerAnimationController.stop();
    if (!context
        .read<QuestionsCubit>()
        .questions()[currentQuestionIndex]
        .attempted) {
      context.read<QuestionsCubit>().updateQuestionWithAnswerAndLifeline(
            context.read<QuestionsCubit>().questions()[currentQuestionIndex].id,
            submittedAnswer,
            context.read<UserDetailsCubit>().getUserFirebaseId(),
            context
                .read<SystemConfigCubit>()
                .quizCorrectAnswerCreditScore(QuizTypes.bookmarkQuiz),
            context
                .read<SystemConfigCubit>()
                .quizWrongAnswerDeductScore(QuizTypes.bookmarkQuiz),
          ); //change question
      await Future<void>.delayed(
        const Duration(seconds: inBetweenQuestionTimeInSeconds),
      );
      if (currentQuestionIndex !=
          (context.read<QuestionsCubit>().questions().length - 1)) {
        changeQuestion();
        if (widget.quizType == QuizTypes.quizZone) {
          await timerAnimationController.forward(from: 0);
        } else {
          timerAnimationController.value = 0.0;
        }
      } else {
        navigateToResult();
      }
    }
  }

  //listener for current user timer
  void currentUserTimerAnimationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      submitAnswer('-1');
    }
  }

  void navigateToResult() {
    if (isSettingDialogOpen) {
      Navigator.of(context).pop();
    }
    if (isExitDialogOpen) {
      Navigator.of(context).pop();
    }

    // Get questions based on quiz type
    List<Question> questions = [];
    // تم إزالة GuessTheWordQuestion لأن اختبارات تخمين الكلمة لم تعد مدعومة

    // تم إزالة الشرط المتعلق بـ GuessTheWord لأن اختبارات تخمين الكلمة لم تعد مدعومة
    questions = context.read<QuestionsCubit>().questions();

    Navigator.of(context).pushReplacementNamed(
      Routes.result,
      arguments: {
        'numberOfPlayer': 1,
        'myPoints': 0, // No points for bookmark quiz
        'quizType': QuizTypes.bookmarkQuiz,
        'questions': questions,
        // تم إزالة guessTheWordQuestions لأن اختبارات تخمين الكلمة لم تعد مدعومة
        'entryFee': 0,
        'timeTakenToCompleteQuiz': 0.0,
        'isPlayed': true,
        'comprehension': null,
        'playWithBot': false,
        'isPremiumCategory': false,
        'hasUsedAnyLifeline': questionsUsingHelp.values.any((used) => used),
        'questionsUsingHelp': questionsUsingHelp,
        'numberOfQuestionsWithHelp':
            questionsUsingHelp.values.where((used) => used).length,
        'resultFromBookmarkQuiz': true,
      },
    );
  }

  @override
  void dispose() {
    timerAnimationController
      ..removeStatusListener(currentUserTimerAnimationStatusListener)
      ..dispose();
    questionAnimationController.dispose();
    questionContentAnimationController.dispose();
    showOptionAnimationController.dispose();
    super.dispose();
  }

  void onTapBackButton() {
    isExitDialogOpen = true;
    showDialog<void>(
      context: context,
      builder: (_) => const ExitGameDialog(),
    ).then((_) => isExitDialogOpen = false);
  }

  Widget _buildQuestions() {
    return BlocBuilder<QuestionsCubit, QuestionsState>(
      bloc: context.read<QuestionsCubit>(),
      builder: (context, state) {
        if (state is QuestionsFetchInProgress || state is QuestionsIntial) {
          return Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          );
        }
        if (state is QuestionsFetchFailure) {
          return Center(
            child: ErrorContainer(
              showBackButton: true,
              errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
              onTapRetry: () {
                _getQuestions();
              },
              showErrorImage: true,
            ),
          );
        }
        final questions = (state as QuestionsFetchSuccess).questions;

        return QuestionsContainer(
          timerAnimationController: timerAnimationController,
          quizType: widget.quizType,
          topPadding: 20,
          answerMode: AnswerMode.showAnswerCorrectnessAndCorrectAnswer,
          lifeLines: const {},
          hasSubmittedAnswerForCurrentQuestion:
              hasSubmittedAnswerForCurrentQuestion,
          questions: questions,
          submitAnswer: submitAnswer,
          questionContentAnimation: questionContentAnimation,
          questionScaleDownAnimation: questionScaleDownAnimation,
          questionScaleUpAnimation: questionScaleUpAnimation,
          questionSlideAnimation: questionSlideAnimation,
          currentQuestionIndex: currentQuestionIndex,
          questionAnimationController: questionAnimationController,
          questionContentAnimationController:
              questionContentAnimationController,
        );
      },
    );
  }

  Widget _buildBottomButton() {
    if (widget.quizType == QuizTypes.audioQuestions) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * 0.05,
          vertical: 15.0,
        ),
        child: SlideTransition(
          position: Tween<Offset>(begin: Offset.zero, end: const Offset(0, 1.5))
              .animate(
            CurvedAnimation(
              parent: showOptionAnimationController,
              curve: Curves.easeInOut,
            ),
          ),
          child: CustomRoundedButton(
            widthPercentage: 0.8,
            backgroundColor: Theme.of(context).primaryColor,
            buttonTitle: context.tr(showOptionsKey) ?? "عرض الخيارات",
            elevation: 5,
            titleColor: Theme.of(context).colorScheme.onPrimary,
            fontWeight: FontWeight.bold,
            onTap: () {
              if (!showOptionAnimationController.isAnimating) {
                showOptionAnimationController.reverse();
                audioQuestionContainerKeys[currentQuestionIndex]
                    .currentState!
                    .changeShowOption();
                timerAnimationController.forward(from: 0);
              }
            },
            radius: 15,
            showBorder: false,
            height: 50,
          ),
        ),
      );
    }
    return const SizedBox();
  }

  Widget _buildQuestionCounter() {
    return BlocBuilder<QuestionsCubit, QuestionsState>(
      bloc: context.read<QuestionsCubit>(),
      builder: (context, state) {
        if (state is QuestionsFetchSuccess) {
          return Text(
            '${currentQuestionIndex + 1}/${state.questions.length}',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          );
        }
        return const SizedBox();
      },
    );
  }

  Widget _buildHelpSection() {
    // للأنواع غير QuizZone، عرض رسالة بسيطة
    if (widget.quizType != QuizTypes.quizZone) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.05),
              Colors.transparent,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(25),
            bottomRight: Radius.circular(25),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lightbulb_outline,
              color: Theme.of(context).primaryColor.withOpacity(0.7),
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              context.tr('reviewBookmarkedQuestions') ??
                  'مراجعة الأسئلة المحفوظة',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).primaryColor.withOpacity(0.8),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    // للـ QuizZone، عرض أدوات المساعدة
    final questions = context.read<QuestionsCubit>().questions();
    if (questions.isEmpty || currentQuestionIndex >= questions.length) {
      return const SizedBox();
    }

    final currentQuestion = questions[currentQuestionIndex];
    final hasExplanation =
        currentQuestion.note != null && currentQuestion.note!.isNotEmpty;
    final canEliminate = currentQuestion.answerOptions != null &&
        currentQuestion.answerOptions!.length > 2 &&
        !eliminatedQuestions.containsKey(currentQuestion.id);

    // إظهار أدوات المساعدة حتى لو لم تكن متاحة (لضمان الاتساق)
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.white.withOpacity(0.95),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.help_outline,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'أدوات المساعدة',
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // أزرار المساعدة
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 10,
            runSpacing: 10,
            children: [
              if (hasExplanation)
                _buildHelpButton(
                  icon: Icons.lightbulb_outline,
                  title: "اشرح لي",
                  color: Theme.of(context).primaryColor,
                  onTap: () => _showExplanationDialog(context, currentQuestion),
                )
              else
                _buildHelpButton(
                  icon: Icons.lightbulb_outline,
                  title: "اشرح لي",
                  color: Colors.grey,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text('لا يوجد شرح متاح لهذا السؤال'),
                        backgroundColor: Colors.orange,
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.all(8),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                      ),
                    );
                  },
                ),
              if (canEliminate)
                _buildHelpButton(
                  icon: Icons.remove_circle_outline,
                  title: "احذف إجابتين",
                  color: Colors.orange,
                  onTap: _eliminateWrongAnswers,
                )
              else
                _buildHelpButton(
                  icon: Icons.remove_circle_outline,
                  title: "احذف إجابتين",
                  color: Colors.grey,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text('لا يمكن استخدام هذه الميزة الآن'),
                        backgroundColor: Colors.orange,
                        behavior: SnackBarBehavior.floating,
                        margin: const EdgeInsets.all(8),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                      ),
                    );
                  },
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          left: 16,
          right: 16,
          bottom: MediaQuery.of(context).padding.bottom + 16,
          top: 16,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white.withOpacity(0.95),
              Colors.white.withOpacity(0.8),
            ],
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: _buildBottomButton(),
      ),
    );
  }

  // دالة محسنة لإنشاء أزرار المساعدة بتصميم جميل وموحد
  Widget _buildHelpButton({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    bool isLoading = false,
  }) {
    return Container(
      margin: EdgeInsets.zero,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color,
            color.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.4),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
        ],
        border: Border.all(
          color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 18),
            child: isLoading
                ? SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(
                      color: Theme.of(context).colorScheme.onPrimary,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .onPrimary
                              .withOpacity(0.25),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          icon,
                          color: Theme.of(context).colorScheme.onPrimary,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Text(
                        title,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.2),
                              offset: const Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  void _showExplanationDialog(BuildContext context, Question question) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ExplanationDialog(
          question: question,
        );
      },
    );

    // تحديث حالة استخدام المساعدة للسؤال الحالي
    final questionId = question.id ?? '';
    if (questionId.isNotEmpty) {
      setState(() {
        questionsUsingHelp[questionId] = true;
      });
    }
  }

  void _eliminateWrongAnswers() {
    final questions = context.read<QuestionsCubit>().questions();
    if (questions.isEmpty || currentQuestionIndex >= questions.length) {
      return;
    }
    final currentQuestion = questions[currentQuestionIndex];

    // التحقق من الشروط الأساسية
    if (currentQuestion.answerOptions == null ||
        currentQuestion.answerOptions!.length <= 2 ||
        eliminatedQuestions.containsKey(currentQuestion.id) ||
        currentQuestion.correctAnswer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('لا يمكن استخدام هذه الميزة الآن'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
      return;
    }

    try {
      // نسخ قائمة الخيارات الحالية
      final allOptions =
          List<AnswerOption>.from(currentQuestion.answerOptions!);

      // الحصول على الإجابة الصحيحة
      final correctAnswerId = AnswerEncryption.decryptCorrectAnswer(
        correctAnswer: currentQuestion.correctAnswer!,
        rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
      );

      // التأكد من وجود الإجابة الصحيحة في القائمة
      final correctOption = allOptions.firstWhere(
        (option) => option.id == correctAnswerId,
      );

      // تجميع الإجابات الخاطئة فقط
      final wrongAnswers = allOptions
          .where((option) => option.id != correctAnswerId)
          .toList()
        ..shuffle();

      // التحقق من وجود إجابات خاطئة كافية
      if (wrongAnswers.isEmpty) {
        throw Exception('لا توجد إجابات خاطئة كافية');
      }

      // إنشاء قائمة الإجابات النهائية وترتيبها عشوائياً
      final remainingAnswers = [
        correctOption,
        wrongAnswers.first,
      ]..shuffle();

      // تسجيل استخدام المساعدة
      final questionId = currentQuestion.id ?? '';
      if (questionId.isNotEmpty) {
        setState(() {
          questionsUsingHelp[questionId] = true;
          eliminatedQuestions[questionId] = true;
          currentQuestion.answerOptions?.clear();
          currentQuestion.answerOptions?.addAll(remainingAnswers);
        });
      }

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم حذف إجابتين خاطئتين'),
          backgroundColor: Theme.of(context).primaryColor,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('حدث خطأ أثناء محاولة حذف الإجابات'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: completedQuiz,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        onTapBackButton();
      },
      child: Scaffold(
        body: Stack(
          children: [
            // خلفية الصفحة بتصميم محسن ومتدرج جميل
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor.withOpacity(0.9),
                    Theme.of(context).primaryColor.withOpacity(0.6),
                    Colors.white.withOpacity(0.95),
                    Colors.white,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  stops: const [0.0, 0.3, 0.7, 1.0],
                ),
              ),
            ),

            // زخارف الخلفية المحسنة
            Positioned(
              top: -80,
              right: -80,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.3),
                      Theme.of(context).primaryColor.withOpacity(0.1),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

            Positioned(
              bottom: -100,
              left: -100,
              child: Container(
                width: 250,
                height: 250,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.25),
                      Theme.of(context).primaryColor.withOpacity(0.08),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

            // زخارف إضافية للجمالية
            Positioned(
              top: MediaQuery.of(context).size.height * 0.3,
              right: -30,
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                ),
              ),
            ),

            Positioned(
              top: MediaQuery.of(context).size.height * 0.6,
              left: -20,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).primaryColor.withOpacity(0.12),
                ),
              ),
            ),
            // شريط علوي محسن مع المؤقت وعداد الأسئلة
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + 10,
                  left: 16,
                  right: 16,
                  bottom: 16,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.95),
                      Theme.of(context).primaryColor.withOpacity(0.8),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر الرجوع المحسن
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.white.withOpacity(0.9),
                            Colors.white.withOpacity(0.7),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(15),
                          onTap: onTapBackButton,
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Icon(
                              Icons.arrow_back_ios_rounded,
                              color: Theme.of(context).primaryColor,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // العنوان المحسن
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors:
                                Theme.of(context).brightness == Brightness.dark
                                    ? [
                                        Theme.of(context)
                                            .colorScheme
                                            .surface
                                            .withOpacity(0.9),
                                        Theme.of(context)
                                            .colorScheme
                                            .surface
                                            .withOpacity(0.7),
                                      ]
                                    : [
                                        Colors.white.withOpacity(0.9),
                                        Colors.white.withOpacity(0.7),
                                      ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context)
                                  .shadowColor
                                  .withOpacity(0.1),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Text(
                          context.tr('bookmarkLbl') ?? "المفضلة",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),

                    // المؤقت المحسن
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors:
                              Theme.of(context).brightness == Brightness.dark
                                  ? [
                                      Theme.of(context)
                                          .colorScheme
                                          .surface
                                          .withOpacity(0.9),
                                      Theme.of(context)
                                          .colorScheme
                                          .surface
                                          .withOpacity(0.7),
                                    ]
                                  : [
                                      Colors.white.withOpacity(0.9),
                                      Colors.white.withOpacity(0.7),
                                    ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color:
                                Theme.of(context).shadowColor.withOpacity(0.1),
                            blurRadius: 15,
                            offset: const Offset(0, 4),
                            spreadRadius: 1,
                          ),
                        ],
                        border: Border.all(
                          color: Theme.of(context)
                              .colorScheme
                              .outline
                              .withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      padding: const EdgeInsets.all(6),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).primaryColor.withOpacity(0.2),
                              Theme.of(context).primaryColor.withOpacity(0.1),
                            ],
                          ),
                        ),
                        child: widget.quizType != QuizTypes.funAndLearn
                            ? TextCircularTimer(
                                animationController: timerAnimationController,
                                arcColor: Theme.of(context).primaryColor,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onTertiary
                                    .withOpacity(0.2),
                                size: 52.0,
                                strokeWidth: 5.0,
                              )
                            : Container(
                                width: 52.0,
                                height: 52.0,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.1),
                                ),
                                child: Icon(
                                  Icons.timer_off,
                                  color: Theme.of(context).primaryColor,
                                  size: 24,
                                ),
                              ),
                      ),
                    ),

                    // عداد الأسئلة المحسن
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 18.0,
                        vertical: 10.0,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors:
                              Theme.of(context).brightness == Brightness.dark
                                  ? [
                                      Theme.of(context).colorScheme.surface,
                                      Theme.of(context)
                                          .colorScheme
                                          .surface
                                          .withOpacity(0.95),
                                    ]
                                  : [
                                      Colors.white,
                                      Colors.white.withOpacity(0.95),
                                    ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.2),
                            blurRadius: 15,
                            offset: const Offset(0, 4),
                            spreadRadius: 1,
                          ),
                        ],
                        border: Border.all(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: _buildQuestionCounter(),
                    ),
                  ],
                ),
              ),
            ),

            // محتوى الأسئلة المحسن
            Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + 80,
                left: 16,
                right: 16,
                bottom: 80,
              ),
              child: Card(
                elevation: 12,
                shadowColor: Theme.of(context).primaryColor.withOpacity(0.2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.surface,
                        Theme.of(context).colorScheme.surface.withOpacity(0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Expanded(
                        child: _buildQuestions(),
                      ),
                      _buildHelpSection(),
                    ],
                  ),
                ),
              ),
            ),

            // أزرار التنقل المحسنة
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }
}
