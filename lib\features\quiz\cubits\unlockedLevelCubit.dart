import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';

@immutable
abstract class UnlockedLevelState {}

class UnlockedLevelInitial extends UnlockedLevelState {}

class UnlockedLevelFetchInProgress extends UnlockedLevelState {}

class UnlockedLevelFetchSuccess extends UnlockedLevelState {
  UnlockedLevelFetchSuccess(
    this.categoryId,
    this.subcategoryId,
    this.unlockedLevel,
  );

  final int unlockedLevel;
  final String? categoryId;
  final String? subcategoryId;
}

class UnlockedLevelFetchFailure extends UnlockedLevelState {
  UnlockedLevelFetchFailure(this.errorMessage);

  final String errorMessage;
}

class UnlockedLevelCubit extends Cubit<UnlockedLevelState> {
  UnlockedLevelCubit(this._quizRepository) : super(UnlockedLevelInitial());
  final QuizRepository _quizRepository;

  Future<void> fetchUnlockLevel(String category, String subCategory) async {
    if (isClosed) return; // تحقق من أن الـ Cubit لم يتم إغلاقه
    
    emit(UnlockedLevelFetchInProgress());
    try {
      final result = await _quizRepository.getUnlockedLevel(
        category,
        subCategory,
      );
      
      if (isClosed) return; // تحقق مرة أخرى قبل emit
      emit(UnlockedLevelFetchSuccess(category, subCategory, result));
    } catch (e) {
      if (isClosed) return;
      emit(UnlockedLevelFetchFailure(e.toString()));
    }
  }
}
