<svg id="wrteam_logo" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="84.552" height="23.579" viewBox="0 0 84.552 23.579">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#6abdbd"/>
      <stop offset="1" stop-color="#117f7c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#579d9e"/>
      <stop offset="1" stop-color="#00615e"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-0.189" y1="0.688" x2="1.18" y2="0.095" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="0.562" y1="-0.124" x2="0.457" y2="0.932" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f07"/>
      <stop offset="1" stop-color="#c60264"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="12.243" y1="0.374" x2="14.05" y2="0.374" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-6" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f07"/>
      <stop offset="1" stop-color="#79003b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="0.01" y1="-0.214" x2="0.422" y2="0.373" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a7c62a"/>
      <stop offset="1" stop-color="#6f8200"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="0.506" y1="0.25" x2="0.48" y2="1.14" gradientUnits="objectBoundingBox">
      <stop offset="0.004" stop-color="#718b32"/>
      <stop offset="1" stop-color="#3c4e1f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="0.426" y1="-0.303" x2="0.688" y2="1.055" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-10" x1="0.406" y1="-0.242" x2="0.54" y2="0.816" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fda037"/>
      <stop offset="1" stop-color="#c96400"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="-0.228" y1="-0.007" x2="1.901" y2="1.476" xlink:href="#linear-gradient-10"/>
  </defs>
  <path id="madeby" d="M-21.065-2.819h.5V-6.528l1.556,2.265h.025l1.556-2.253v3.7h.5V-7.495h-.439l-1.632,2.372-1.632-2.372h-.439Zm6.464.075a1.549,1.549,0,0,0,.7-.157,1.2,1.2,0,0,0,.477-.4v.483h.471V-5.129a1.078,1.078,0,0,0-.16-.587,1.1,1.1,0,0,0-.446-.4,1.434,1.434,0,0,0-.656-.144,1.48,1.48,0,0,0-.546.1,1.614,1.614,0,0,0-.468.279,1.1,1.1,0,0,0-.3.389l.427.232a.947.947,0,0,1,.345-.4.955.955,0,0,1,.54-.154.847.847,0,0,1,.571.188.614.614,0,0,1,.22.489v.176l-1.217.2a1.359,1.359,0,0,0-.816.383.986.986,0,0,0-.257.678.853.853,0,0,0,.144.489.988.988,0,0,0,.4.336A1.286,1.286,0,0,0-14.6-2.744Zm-.609-.966a.565.565,0,0,1,.16-.4.921.921,0,0,1,.524-.232l1.1-.182v.251a1.1,1.1,0,0,1-.144.559,1.067,1.067,0,0,1-.4.4,1.192,1.192,0,0,1-.6.147.674.674,0,0,1-.461-.157.491.491,0,0,1-.179-.383Zm5.058.966a1.577,1.577,0,0,0,.715-.163,1.271,1.271,0,0,0,.515-.458v.546h.471V-7.57h-.471v1.9A1.482,1.482,0,0,0-9.443-6.1a1.564,1.564,0,0,0-.709-.157,1.593,1.593,0,0,0-.85.229,1.645,1.645,0,0,0-.59.624,1.851,1.851,0,0,0-.217.9,1.856,1.856,0,0,0,.217.9A1.632,1.632,0,0,0-11-2.973,1.6,1.6,0,0,0-10.152-2.744Zm.025-.471a1.105,1.105,0,0,1-.606-.169,1.192,1.192,0,0,1-.42-.461,1.385,1.385,0,0,1-.154-.656,1.381,1.381,0,0,1,.154-.659,1.192,1.192,0,0,1,.424-.458,1.11,1.11,0,0,1,.6-.169,1.137,1.137,0,0,1,.612.169,1.226,1.226,0,0,1,.433.461,1.341,1.341,0,0,1,.16.656,1.341,1.341,0,0,1-.16.656,1.226,1.226,0,0,1-.433.461A1.137,1.137,0,0,1-10.127-3.215Zm4.487.471a1.66,1.66,0,0,0,.631-.116,1.612,1.612,0,0,0,.493-.308,1.275,1.275,0,0,0,.311-.417l-.4-.213a1.411,1.411,0,0,1-.408.439,1.057,1.057,0,0,1-.621.176A1.158,1.158,0,0,1-6.2-3.328a1.172,1.172,0,0,1-.43-.4A1.216,1.216,0,0,1-6.81-4.36h2.648q.013-.082.019-.16t.006-.141a1.664,1.664,0,0,0-.107-.587,1.65,1.65,0,0,0-.308-.515,1.479,1.479,0,0,0-.486-.364,1.5,1.5,0,0,0-.653-.132,1.543,1.543,0,0,0-.825.226,1.62,1.62,0,0,0-.58.618,1.854,1.854,0,0,0-.213.9,1.865,1.865,0,0,0,.22.91,1.64,1.64,0,0,0,.6.628A1.614,1.614,0,0,0-5.64-2.744Zm-.05-3.075a1.042,1.042,0,0,1,.518.132A1.022,1.022,0,0,1-4.8-5.33a1.032,1.032,0,0,1,.151.533H-6.8a1.176,1.176,0,0,1,.2-.543,1.115,1.115,0,0,1,.4-.355,1.088,1.088,0,0,1,.515-.126Zm4.142,3H.423a1.536,1.536,0,0,0,1.083-.367,1.286,1.286,0,0,0,.4-.995,1.255,1.255,0,0,0-.188-.678,1.288,1.288,0,0,0-.53-.477,1.067,1.067,0,0,0,.533-.985,1.1,1.1,0,0,0-.351-.85A1.345,1.345,0,0,0,.42-7.495H-1.545Zm.5-2.7V-7.024H.376a.934.934,0,0,1,.606.188A.65.65,0,0,1,1.21-6.3a.748.748,0,0,1-.229.565.838.838,0,0,1-.606.22Zm0,2.228V-5.066H.423a1.02,1.02,0,0,1,.712.238.833.833,0,0,1,.267.65.821.821,0,0,1-.267.65,1.037,1.037,0,0,1-.712.235ZM3.115-1.439a1.107,1.107,0,0,0,.565-.135,1.2,1.2,0,0,0,.386-.355,2.389,2.389,0,0,0,.251-.464l1.538-3.8H5.333l-1.1,2.78-1.1-2.78H2.629l1.346,3.37-.119.289a1.275,1.275,0,0,1-.32.493.746.746,0,0,1-.471.126,1.239,1.239,0,0,1-.2-.016q-.1-.016-.16-.022v.439a1.011,1.011,0,0,0,.2.053,1.36,1.36,0,0,0,.2.019Z" transform="translate(49.935 7.57)" fill="#fff"/>
  <g id="wrteam_logo-2" data-name="wrteam_logo" transform="translate(0 9.195)">
    <g id="wrteam" transform="translate(30.284 0.869)">
      <path id="Path_52" data-name="Path 52" d="M2298.746,817.281l-.323,1.683h-7.367c0-.031-.008-.063-.012-.092a2.186,2.186,0,0,0-.376-.934,2.076,2.076,0,0,0-.807-.657Z" transform="translate(-2269.85 -817.279)" fill="#fff"/>
      <path id="Path_53" data-name="Path 53" d="M2403.337,912.837l-1.149,5.982h2.408l1.149-5.982Z" transform="translate(-2379.475 -910.54)" fill="#fff"/>
      <path id="Path_54" data-name="Path 54" d="M1460.083,822.416l-.913-5.13h-2.47l1.763,8.284Z" transform="translate(-1456.7 -817.284)" fill="#fff"/>
      <path id="Path_55" data-name="Path 55" d="M1536.953,821.766l-.968-4.481H1534.3l-4.25,8.276v.006h2l2.425-4.819.941,4.016Z" transform="translate(-1528.288 -817.284)" fill="#fff"/>
      <path id="Path_56" data-name="Path 56" d="M1766.706,817.286l-4.253,8.284h1.952l4.407-8.284Z" transform="translate(-1755.108 -817.284)" fill="#fff"/>
      <path id="Path_57" data-name="Path 57" d="M1953.649,822.34a3.442,3.442,0,0,0,.4-.12,3.175,3.175,0,0,0,1.039-.615,3.1,3.1,0,0,0,.976-1.717,2.7,2.7,0,0,0,.023-.914,1.928,1.928,0,0,0-.334-.83,1.886,1.886,0,0,0-.8-.621,3.234,3.234,0,0,0-1.318-.239h-5.924l-.321,1.675h4.6v.006h.251a2.474,2.474,0,0,1,1.186.125q.3.218.183.81a1.255,1.255,0,0,1-.26.6.961.961,0,0,1-.523.287,4.406,4.406,0,0,1-.941.078h-.52l.247-1.288H1949.2l-1.148,5.981h2.413l.6-3.105h.45l1.168,3.105h2.3Z" transform="translate(-1935.522 -817.282)" fill="#fff"/>
      <path id="Path_58" data-name="Path 58" d="M2876.307,1013.66h0a.918.918,0,0,1-.425.116.436.436,0,0,1-.346-.153.5.5,0,0,1-.077-.439.947.947,0,0,1,.446-.637,5.141,5.141,0,0,1,.684-.347l.179-.932-.135.043-.661.207a10.518,10.518,0,0,0-1.25.459,2.552,2.552,0,0,0-.809.57,1.736,1.736,0,0,0-.43.879,1.531,1.531,0,0,0,.02.683.936.936,0,0,0,.42.55,1.716,1.716,0,0,0,.941.219,2.379,2.379,0,0,0,1.074-.259,1.568,1.568,0,0,0,.211-.137Z" transform="translate(-2839.416 -1006.604)" fill="#fff"/>
      <path id="Path_59" data-name="Path 59" d="M2937.506,917.525a.206.206,0,0,1-.208-.257l.5-2.6a1.356,1.356,0,0,0-.354-1.335c-.334-.314-.811-.473-1.952-.484h-1.8l-.251,1.307h1.75c.279.019.459.093.527.23a1.046,1.046,0,0,1,.038.658h0l-.176.919h0l-.262,1.366h0l-.131.681a.634.634,0,0,0,.586.816h2.729l.251-1.3Z" transform="translate(-2897.962 -910.548)" fill="#fff"/>
      <path id="Path_60" data-name="Path 60" d="M3147.93,914.742a2.546,2.546,0,0,0-.352-1.063,2,2,0,0,0-.826-.748,2.824,2.824,0,0,0-1.269-.263,3.571,3.571,0,0,0-1.245.22,3.482,3.482,0,0,0-.979.565,2.037,2.037,0,0,0-.666-.524,2.824,2.824,0,0,0-1.27-.263,3.572,3.572,0,0,0-1.244.22,3.519,3.519,0,0,0-1.115.677,3.89,3.89,0,0,0-.829,1.05,4.342,4.342,0,0,0-.465,1.292c-.008.046-.017.094-.022.129l-.5,2.618h1.885l.556-2.895a1.336,1.336,0,0,1,1.332-1.1.885.885,0,0,1,.945,1.013l-.572,2.978h1.9l.515-2.683c.016-.084.03-.162.041-.236a1.328,1.328,0,0,1,1.327-1.072.885.885,0,0,1,.945,1.013l-.571,2.978h1.9l.506-2.636A3.882,3.882,0,0,0,3147.93,914.742Z" transform="translate(-3096.776 -910.375)" fill="#fff"/>
      <path id="Path_61" data-name="Path 61" d="M2603.769,916.361h5.98l.079-.412a3.232,3.232,0,0,0-.068-1.8,1.868,1.868,0,0,0-.86-1,2.676,2.676,0,0,0-1.268-.306,4.219,4.219,0,0,0-1.847.387,3.38,3.38,0,0,0-1.3,1.067,3.853,3.853,0,0,0-.658,1.538,2.727,2.727,0,0,0-.063.451A.651.651,0,0,1,2603.769,916.361Zm2.627-1.912a1.253,1.253,0,0,1,.871-.341.762.762,0,0,1,.708.335,1.247,1.247,0,0,1,.1.9h-2.152a1.57,1.57,0,0,1,.477-.893Z" transform="translate(-2576.213 -910.545)" fill="#fff"/>
      <path id="Path_62" data-name="Path 62" d="M2606.252,1077.82H2604.1c.009.085.018.171.035.255a2.024,2.024,0,0,0,.467.941,2.225,2.225,0,0,0,.9.605,3.344,3.344,0,0,0,1.228.212h1.013l.289-1.506h-.875A1.071,1.071,0,0,1,2606.252,1077.82Z" transform="translate(-2576.537 -1071.561)" fill="#fff"/>
    </g>
    <g id="icon" transform="translate(0 0)">
      <path id="Path_63" data-name="Path 63" d="M874.347,781.051l-3.577,12.4c-.049.148-.094.287-.137.419-.319.969-.52,1.514-1.013,1.561h-5.39L866.4,787.9l.21-.73h0l1.765-6.122Z" transform="translate(-848.178 -781.051)" fill="url(#linear-gradient)"/>
      <path id="Path_64" data-name="Path 64" d="M958.523,1042.594c-.319.97-.52,1.514-1.013,1.561h-.763l-.42-.37-2.035-7.161-.008-.03.219-.7.019.063.056.181.088.284q.058.188.117.373l.141.446q.08.251.162.5.088.274.178.549.095.289.188.577t.2.592q.1.3.205.591t.206.576q.1.274.2.546.1.251.2.5.09.222.188.439c.055.125.11.247.171.368a1.565,1.565,0,0,0,.259.414.753.753,0,0,0,.376.206.676.676,0,0,0,.237,0,1,1,0,0,0,.318-.11A2.083,2.083,0,0,0,958.523,1042.594Z" transform="translate(-936.068 -1029.773)" fill="url(#linear-gradient-2)"/>
      <path id="Path_65" data-name="Path 65" d="M1042.24,791.507l3.012-10.452h-5.91a25.414,25.414,0,0,0,2.9,10.452Z" transform="translate(-1019.088 -781.054)" fill="url(#linear-gradient-3)"/>
      <path id="Path_66" data-name="Path 66" d="M628.592,795.44h-5.969c-.574-.054-.771-.776-1.179-2.066-.011-.033-.565-1.937-1.188-4.094l-.377-1.3c-.708-2.444-1.394-4.807-1.411-4.861-.408-1.29-.6-2.011-1.179-2.066h5.951c.432.041.65.458.9,1.211h0c.084.251.173.534.274.854.035.112,2.958,10.139,2.994,10.251C627.821,794.664,628.018,795.385,628.592,795.44Z" transform="translate(-607.17 -781.057)" fill="url(#linear-gradient-4)"/>
      <path id="Path_67" data-name="Path 67" d="M734.876,1094.786c-.581-2.117-2.166-4.49-3.7-4.49.714,2.462,1.406,4.85,1.418,4.888.408,1.29.6,2.011,1.179,2.066h2.7A3.93,3.93,0,0,1,734.876,1094.786Z" transform="translate(-718.321 -1082.868)" fill="url(#linear-gradient-5)"/>
      <path id="Path_68" data-name="Path 68" d="M724.993,782.271c-.007-.016-.015-.031-.023-.046a.8.8,0,0,0-.794-.376.657.657,0,0,0-.4.172,1.347,1.347,0,0,0-.243.36l-.016.03c-.17.33-.319.67-.459,1.014s-.271.7-.4,1.047c-.248.682-.479,1.369-.7,2.059q-.291.892-.565,1.79-.142.465-.281.931l-.009.031-.479.469c-.023,0,.051-1.051.1-1.765.028-.369.049-.649.049-.649l1.29-4.211.981-1.6.415-.471h.628C724.519,781.1,724.738,781.518,724.993,782.271Z" transform="translate(-708.018 -781.059)" fill="url(#linear-gradient-6)"/>
      <path id="Path_69" data-name="Path 69" d="M206.254,795.441h-5.389c-.487-.046-.69-.576-1-1.52l-.151-.459-3.577-12.4h5.969l1.753,6.077.051.179Z" transform="translate(-196.134 -781.058)" fill="url(#linear-gradient-7)"/>
      <path id="Path_70" data-name="Path 70" d="M355.507,1036.595l-.008.03-2.035,7.161-.42.37h-.737c-.487-.046-.69-.576-1-1.52a2.006,2.006,0,0,0,.474.369,1,1,0,0,0,.318.11.67.67,0,0,0,.237,0,.759.759,0,0,0,.377-.207,1.561,1.561,0,0,0,.259-.414c.063-.121.117-.244.171-.368l.188-.439q.1-.251.2-.5.1-.272.2-.546t.206-.576c.069-.2,1.375-4.136,1.381-4.162l.039.134Z" transform="translate(-347.576 -1029.771)" fill="url(#linear-gradient-8)"/>
      <path id="Path_71" data-name="Path 71" d="M199.495,792.7a24.889,24.889,0,0,1,3.138-9.811l-.529-1.834h-5.969Z" transform="translate(-196.134 -781.054)" fill="url(#linear-gradient-9)"/>
      <path id="Path_72" data-name="Path 72" d="M403.85,783.121c-.035.112-2.95,10.174-2.975,10.251-.408,1.29-.6,2.011-1.179,2.066h-5.969c.574-.054.771-.776,1.179-2.066.035-.112,2.958-10.139,2.994-10.251.408-1.29.6-2.011,1.179-2.066h5.95C404.455,781.109,404.258,781.83,403.85,783.121Z" transform="translate(-388.978 -781.054)" fill="url(#linear-gradient-10)"/>
      <path id="Path_73" data-name="Path 73" d="M396.564,795.438c.732-1.388,1.325-2.84,2-4.26a24.271,24.271,0,0,1,2.8-4.508,27.118,27.118,0,0,1,2.151-2.4c.2-.684.326-1.123.333-1.146.408-1.29.6-2.011,1.179-2.066h-5.95c-.574.054-.771.776-1.179,2.066-.035.112-2.958,10.14-2.994,10.251-.408,1.29-.6,2.011-1.179,2.066Z" transform="translate(-388.978 -781.054)" fill="url(#linear-gradient-11)"/>
    </g>
    <path id="tagline" d="M.828,45.99l.653-1.22h.168L.906,46.138V47H.746v-.85L0,44.77H.178Zm4.016-.109a1.245,1.245,0,0,1-.263.838.914.914,0,0,1-.732.309.912.912,0,0,1-.73-.309,1.25,1.25,0,0,1-.265-.842,1.229,1.229,0,0,1,.267-.838.921.921,0,0,1,.731-.306.911.911,0,0,1,.73.308,1.246,1.246,0,0,1,.264.84Zm-1.824,0a1.142,1.142,0,0,0,.213.741.746.746,0,0,0,.613.262.753.753,0,0,0,.615-.259,1.143,1.143,0,0,0,.213-.742,1.13,1.13,0,0,0-.213-.739.749.749,0,0,0-.612-.257.753.753,0,0,0-.614.259,1.121,1.121,0,0,0-.213.736ZM8,44.77v1.443a.8.8,0,0,1-.223.6.853.853,0,0,1-.621.217.753.753,0,0,1-.822-.823V44.77H6.5v1.443a.621.621,0,0,0,.683.68.61.61,0,0,0,.67-.665V44.77Zm1.771,1.255V47H9.62V44.77h.531a1,1,0,0,1,.613.153.547.547,0,0,1,.2.461.6.6,0,0,1-.118.377.657.657,0,0,1-.359.221L11.091,47H10.9l-.571-.968Zm0-.134H10.2a.679.679,0,0,0,.439-.126.449.449,0,0,0,.156-.372.436.436,0,0,0-.152-.37.832.832,0,0,0-.5-.116H9.777ZM16.081,47h-.126l-.489-1.72q-.063-.212-.092-.348-.024.132-.07.3T14.81,47h-.126l-.613-2.229h.163l.39,1.436.043.161q.02.074.036.139t.029.126q.013.063.024.121.036-.207.156-.628l.381-1.352h.173l.447,1.553q.078.269.111.433.02-.11.051-.233t.471-1.752H16.7Zm3.891-1.118a1.245,1.245,0,0,1-.265.84,1.02,1.02,0,0,1-1.462,0,1.25,1.25,0,0,1-.265-.842,1.229,1.229,0,0,1,.267-.838.921.921,0,0,1,.731-.306.911.911,0,0,1,.73.308,1.246,1.246,0,0,1,.269.842Zm-1.824,0a1.142,1.142,0,0,0,.213.741.855.855,0,0,0,1.228,0,1.143,1.143,0,0,0,.213-.742,1.13,1.13,0,0,0-.213-.739.857.857,0,0,0-1.226,0,1.121,1.121,0,0,0-.211.744Zm3.5.151V47H21.5V44.77h.532a1,1,0,0,1,.613.153.547.547,0,0,1,.2.461.6.6,0,0,1-.118.377.657.657,0,0,1-.359.221L22.967,47h-.188l-.574-.968Zm0-.134h.425a.679.679,0,0,0,.439-.126.449.449,0,0,0,.156-.372.436.436,0,0,0-.152-.37.832.832,0,0,0-.5-.116h-.371ZM25.859,47H25.67l-.856-1.179-.3.262V47h-.156V44.77h.156v1.159l.247-.247.874-.912h.2l-.913.941Zm4.486-1.589a.592.592,0,0,1-.22.5.987.987,0,0,1-.622.173h-.345V47H29V44.77h.546q.8,0,.8.641Zm-1.186.533h.306a.929.929,0,0,0,.544-.126.469.469,0,0,0,.168-.4.456.456,0,0,0-.159-.382.793.793,0,0,0-.491-.126h-.369Zm3.753.269h-.907L31.692,47h-.173l.9-2.239h.1L33.394,47h-.171Zm-.849-.141h.8l-.3-.8q-.038-.095-.092-.262a2.449,2.449,0,0,1-.09.265Zm2.814-.044V47h-.154V44.77h.531a1,1,0,0,1,.613.153.547.547,0,0,1,.2.461.6.6,0,0,1-.118.377.657.657,0,0,1-.359.221L36.194,47h-.188l-.574-.968Zm0-.134H35.3a.679.679,0,0,0,.439-.126A.449.449,0,0,0,35.9,45.4a.436.436,0,0,0-.152-.37.832.832,0,0,0-.5-.116h-.369ZM38.167,47H38.01V44.917h-.724V44.77h1.606v.146h-.724Zm3.685,0H41.7l-1.318-1.979h-.012q.018.354.018.533V47h-.149V44.77h.156l1.318,1.976h.009q-.014-.274-.014-.521V44.77h.151Zm2.859,0H43.49V44.77h1.218v.143H43.649v.843h1V45.9h-1v.956h1.063Zm1.67-.971V47h-.156V44.77h.528a1,1,0,0,1,.613.153.547.547,0,0,1,.2.461.6.6,0,0,1-.118.377.657.657,0,0,1-.359.221L47.695,47h-.188l-.572-.97Zm0-.134h.425a.679.679,0,0,0,.439-.126A.449.449,0,0,0,47.4,45.4a.436.436,0,0,0-.153-.37.832.832,0,0,0-.5-.116h-.369Z" transform="translate(31.995 -33.861)" fill="#fff"/>
    <path id="registed" d="M3635.921,814.8h0a.771.771,0,0,0-.77-.771h0a.771.771,0,0,0-.771.771h0a.77.77,0,0,0,.77.771h0a.767.767,0,0,0,.544-.226.787.787,0,0,0,.063-.072.77.77,0,0,0,.162-.473Zm-1.114.395v-.816h.419a.6.6,0,0,1,.178.02.189.189,0,0,1,.1.074.222.222,0,0,1,.038.132.218.218,0,0,1-.109.2.3.3,0,0,1-.088.032.256.256,0,0,1,.063.03.269.269,0,0,1,.04.043.279.279,0,0,1,.035.051l.122.236h-.284l-.134-.251a.193.193,0,0,0-.046-.063.106.106,0,0,0-.063-.019h-.022v.33h-.251Zm.251-.484h.106a.39.39,0,0,0,.067-.011.065.065,0,0,0,.04-.026.071.071,0,0,0,.016-.047.073.073,0,0,0-.025-.063.152.152,0,0,0-.094-.021h-.11v.165Zm1.071.089h0a.974.974,0,0,1-.207.6,1.014,1.014,0,0,1-.081.092.978.978,0,0,1-.69.287h0a.98.98,0,0,1-.98-.98h0a.979.979,0,0,1,.979-.98h0a.979.979,0,0,1,.98.98Z" transform="translate(-3551.579 -813.035)" fill="#fff"/>
  </g>
</svg>
