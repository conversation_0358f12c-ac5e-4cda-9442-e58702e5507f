import 'package:flutter/material.dart';

/// مؤشر مستوى الصوت
/// يعرض مستوى الصوت بشكل بصري
class AudioVolumeIndicator extends StatelessWidget {
  /// مستوى الصوت (0.0 - 1.0)
  final double volume;
  
  /// حجم المؤشر
  final double size;
  
  /// لون المؤشر عندما يكون نشطًا
  final Color activeColor;
  
  /// لون المؤشر عندما يكون غير نشط
  final Color inactiveColor;
  
  /// عدد الأشرطة في المؤشر
  final int barCount;
  
  /// سماكة الأشرطة
  final double barWidth;
  
  /// المسافة بين الأشرطة
  final double spacing;
  
  /// ما إذا كان الميكروفون مكتومًا
  final bool isMuted;
  
  /// ما إذا كان المؤشر يعرض مستوى صوت المستخدم المحلي
  final bool isLocalUser;
  
  /// ما إذا كان المؤشر يعرض بشكل أفقي
  final bool isHorizontal;

  const AudioVolumeIndicator({
    super.key,
    required this.volume,
    this.size = 24.0,
    this.activeColor = Colors.green,
    this.inactiveColor = Colors.grey,
    this.barCount = 5,
    this.barWidth = 3.0,
    this.spacing = 2.0,
    this.isMuted = false,
    this.isLocalUser = false,
    this.isHorizontal = false,
  });

  @override
  Widget build(BuildContext context) {
    // إذا كان الميكروفون مكتومًا، نعرض أيقونة الميكروفون المكتوم
    if (isMuted) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.mic_off,
          color: Colors.red,
          size: size * 0.6,
        ),
      );
    }

    // إذا كان مستوى الصوت منخفضًا جدًا، نعرض أيقونة الميكروفون العادية
    if (volume < 0.05) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: inactiveColor.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.mic,
          color: inactiveColor,
          size: size * 0.6,
        ),
      );
    }

    // عرض مؤشر مستوى الصوت
    return Container(
      width: isHorizontal ? null : size,
      height: isHorizontal ? size : null,
      padding: EdgeInsets.all(size * 0.1),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        shape: BoxShape.circle,
      ),
      child: isHorizontal
          ? _buildHorizontalBars()
          : _buildVerticalBars(),
    );
  }

  /// بناء الأشرطة العمودية
  Widget _buildVerticalBars() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: List.generate(barCount, (index) {
        final threshold = index / barCount;
        final isActive = volume >= threshold;
        final barHeight = size * 0.8 * (index + 1) / barCount;
        
        return Container(
          margin: EdgeInsets.symmetric(horizontal: spacing / 2),
          width: barWidth,
          height: barHeight,
          decoration: BoxDecoration(
            color: isActive ? activeColor : inactiveColor.withOpacity(0.3),
            borderRadius: BorderRadius.circular(barWidth / 2),
          ),
        );
      }),
    );
  }

  /// بناء الأشرطة الأفقية
  Widget _buildHorizontalBars() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(barCount, (index) {
        final threshold = index / barCount;
        final isActive = volume >= threshold;
        final barWidth = size * 0.8 * (index + 1) / barCount;
        
        return Container(
          margin: EdgeInsets.symmetric(vertical: spacing / 2),
          width: barWidth,
          height: this.barWidth,
          decoration: BoxDecoration(
            color: isActive ? activeColor : inactiveColor.withOpacity(0.3),
            borderRadius: BorderRadius.circular(this.barWidth / 2),
          ),
        );
      }),
    );
  }

  /// الحصول على لون خلفية المؤشر
  Color _getBackgroundColor() {
    if (isMuted) {
      return Colors.red.withOpacity(0.2);
    }
    
    if (volume < 0.05) {
      return inactiveColor.withOpacity(0.1);
    }
    
    // تغيير اللون بناءً على مستوى الصوت
    if (volume > 0.7) {
      return activeColor.withOpacity(0.3);
    } else if (volume > 0.4) {
      return activeColor.withOpacity(0.2);
    } else {
      return activeColor.withOpacity(0.1);
    }
  }
}

/// مؤشر مستوى الصوت المتحرك
/// يعرض مستوى الصوت بشكل متحرك
class AnimatedAudioVolumeIndicator extends StatelessWidget {
  /// مستوى الصوت (0.0 - 1.0)
  final double volume;
  
  /// حجم المؤشر
  final double size;
  
  /// لون المؤشر عندما يكون نشطًا
  final Color activeColor;
  
  /// لون المؤشر عندما يكون غير نشط
  final Color inactiveColor;
  
  /// ما إذا كان الميكروفون مكتومًا
  final bool isMuted;
  
  /// ما إذا كان المؤشر يعرض مستوى صوت المستخدم المحلي
  final bool isLocalUser;
  
  /// مدة الحركة
  final Duration duration;

  const AnimatedAudioVolumeIndicator({
    super.key,
    required this.volume,
    this.size = 24.0,
    this.activeColor = Colors.green,
    this.inactiveColor = Colors.grey,
    this.isMuted = false,
    this.isLocalUser = false,
    this.duration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: duration,
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        shape: BoxShape.circle,
        boxShadow: volume > 0.3 && !isMuted
            ? [
                BoxShadow(
                  color: activeColor.withOpacity(0.3 * volume),
                  blurRadius: 4.0 * volume,
                  spreadRadius: 2.0 * volume,
                )
              ]
            : null,
      ),
      child: Center(
        child: Icon(
          isMuted ? Icons.mic_off : (volume > 0.05 ? Icons.mic : Icons.mic_none),
          color: isMuted
              ? Colors.red
              : (volume > 0.05 ? activeColor : inactiveColor),
          size: size * 0.6,
        ),
      ),
    );
  }

  /// الحصول على لون خلفية المؤشر
  Color _getBackgroundColor() {
    if (isMuted) {
      return Colors.red.withOpacity(0.2);
    }
    
    if (volume < 0.05) {
      return inactiveColor.withOpacity(0.1);
    }
    
    // تغيير اللون بناءً على مستوى الصوت
    if (volume > 0.7) {
      return activeColor.withOpacity(0.3);
    } else if (volume > 0.4) {
      return activeColor.withOpacity(0.2);
    } else {
      return activeColor.withOpacity(0.1);
    }
  }
}

/// مؤشر مستوى الصوت الدائري
/// يعرض مستوى الصوت بشكل دائري
class CircularAudioVolumeIndicator extends StatelessWidget {
  /// مستوى الصوت (0.0 - 1.0)
  final double volume;
  
  /// حجم المؤشر
  final double size;
  
  /// لون المؤشر عندما يكون نشطًا
  final Color activeColor;
  
  /// لون المؤشر عندما يكون غير نشط
  final Color inactiveColor;
  
  /// ما إذا كان الميكروفون مكتومًا
  final bool isMuted;
  
  /// ما إذا كان المؤشر يعرض مستوى صوت المستخدم المحلي
  final bool isLocalUser;
  
  /// سماكة الدائرة
  final double strokeWidth;

  const CircularAudioVolumeIndicator({
    super.key,
    required this.volume,
    this.size = 24.0,
    this.activeColor = Colors.green,
    this.inactiveColor = Colors.grey,
    this.isMuted = false,
    this.isLocalUser = false,
    this.strokeWidth = 2.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        children: [
          // دائرة الخلفية
          Center(
            child: Container(
              width: size - strokeWidth * 2,
              height: size - strokeWidth * 2,
              decoration: BoxDecoration(
                color: _getBackgroundColor(),
                shape: BoxShape.circle,
              ),
            ),
          ),
          
          // مؤشر مستوى الصوت
          if (!isMuted && volume > 0.05)
            Center(
              child: SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: volume,
                  strokeWidth: strokeWidth,
                  valueColor: AlwaysStoppedAnimation<Color>(activeColor),
                  backgroundColor: inactiveColor.withOpacity(0.2),
                ),
              ),
            ),
          
          // أيقونة الميكروفون
          Center(
            child: Icon(
              isMuted ? Icons.mic_off : (volume > 0.05 ? Icons.mic : Icons.mic_none),
              color: isMuted
                  ? Colors.red
                  : (volume > 0.05 ? activeColor : inactiveColor),
              size: size * 0.5,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون خلفية المؤشر
  Color _getBackgroundColor() {
    if (isMuted) {
      return Colors.red.withOpacity(0.1);
    }
    
    if (volume < 0.05) {
      return inactiveColor.withOpacity(0.1);
    }
    
    // تغيير اللون بناءً على مستوى الصوت
    if (volume > 0.7) {
      return activeColor.withOpacity(0.1);
    } else if (volume > 0.4) {
      return activeColor.withOpacity(0.05);
    } else {
      return Colors.transparent;
    }
  }
}
