// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';


// Stub implementation of AwesomeNotificationManager
// This is a placeholder implementation that doesn't use the awesome_notifications package
// It provides the same interface but doesn't actually show notifications

AwesomeNotificationManager awesomeNotificationManager =
    AwesomeNotificationManager();

class AwesomeNotificationManager {
  Future<void> init() async {
    try {
      log('Notification manager initialized (stub implementation)');
      // No actual initialization needed in this stub implementation
    } catch (e) {
      log('Error initializing notification manager: ${e.toString()}');
    }
  }

  Future<bool> isNotificationEnabled() async {
    // Always return true in this stub implementation
    return true;
  }

  Future<void> listen() async {
    // No actual listener in this stub implementation
    log('Notification listener set up (stub implementation)');
  }

  Future<void> cancelAllNotifications() async {
    // No actual cancellation in this stub implementation
    log('All notifications cancelled (stub implementation)');
  }

  Future<void> cancelNotificationById({
    required int id,
  }) async {
    // No actual cancellation in this stub implementation
    log('Notification with ID $id cancelled (stub implementation)');
  }

  Future<void> showCustomNotification({
    required String title,
    String? body,
    required String payload,
  }) async {
    // Log the notification instead of showing it
    log('NOTIFICATION: Title: $title, Body: $body, Payload: $payload');
  }

  Future<void> appOpenNotification() async {
    // Log the notification instead of showing it
    log('APP OPEN NOTIFICATION (stub implementation)');
  }

  Future<void> addCustomWeeklyReminder({
    required int id,
    required String title,
    String? body,
    required String payload,
    required Time time,
    int? weekday,
    bool needToOpen = false,
  }) async {
    // Log the notification instead of showing it
    log('WEEKLY REMINDER: ID: $id, Title: $title, Body: $body, Time: ${time.hour}:${time.minute}, Weekday: $weekday');
  }

  Future<void> addCustomDailyReminder({
    required int id,
    required String title,
    String? body,
    required Time time,
    required String payload,
    bool needToOpen = false,
  }) async {
    // Log the notification instead of showing it
    log('DAILY REMINDER: ID: $id, Title: $title, Body: $body, Time: ${time.hour}:${time.minute}');
  }

  static void onNotificationClick(String payload) {
    // No action in this stub implementation
    log('Notification clicked with payload: $payload (stub implementation)');
  }

  void dispose() {
    // No actual disposal needed in this stub implementation
  }
}

class Time {
  final int hour;
  final int minute;
  Time({
    required this.hour,
    required this.minute,
  });
}
