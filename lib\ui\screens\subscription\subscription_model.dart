import 'package:purchases_flutter/purchases_flutter.dart';

class SubscriptionModel {
  final String id;
  final String title;
  final String description;
  final bool isActive;
  final String? productIdentifier;
  final double price;

  SubscriptionModel({
    required this.id,
    required this.title,
    required this.description,
    required this.isActive,
    this.productIdentifier,
    required this.price,
  });

  factory SubscriptionModel.fromPackage(Package package) {
    return SubscriptionModel(
      id: package.identifier,
      title: package.storeProduct.title,
      description: package.storeProduct.description,
      isActive: false, // سيتم تحديثه لاحقاً من خلال CustomerInfo
      productIdentifier: package.storeProduct.identifier,
      price: double.parse(package.storeProduct.price as String),
    );
  }

  @override
  String toString() {
    return 'SubscriptionModel(id: $id, title: $title, isActive: $isActive, price: $price)';
  }
}