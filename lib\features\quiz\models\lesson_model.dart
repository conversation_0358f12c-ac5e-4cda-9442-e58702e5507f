class LessonModel {
  final String id;
  final String title;
  final String description;
  final String videoId;
  final String thumbnail;
  final String duration;
  final int views;
  final String categoryId;
  final int order;
  final String backendCategoryId; // إضافة معرف القسم من الباك اند

  LessonModel({
    required this.id,
    required this.title,
    required this.description,
    required this.videoId,
    required this.thumbnail,
    required this.duration,
    required this.views,
    required this.categoryId,
    required this.backendCategoryId, // إضافة للكونستركتور
    required this.order,
  });

  factory LessonModel.fromMap(Map<String, dynamic> map, String id) {
    return LessonModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      videoId: map['video_id'] ?? '',
      thumbnail: map['thumbnail'] ?? '',
      duration: map['duration'] ?? '00:00',
      views: map['views'] ?? 0,
      categoryId: map['category_id'] ?? '',
      backendCategoryId: map['backend_category_id'] ?? '', // قراءة المعرف من Firebase
      order: map['order'] ?? 0,
    );
  }
}
