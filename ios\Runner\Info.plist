<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>مجتهد</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeRole</key>
            <string>Editor</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>com.googleusercontent.apps.695537688649-3aa5up0ukb3da1lc2bj4sjmigoc348lf</string>
            </array>
        </dict>
    </array>
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>
    <key>GADApplicationIdentifier</key>
    <string>ca-app-pub-7999722480221428~3416572922</string>
    <key>SKAdNetworkItems</key>
    <array>
        <dict>
            <key>SKAdNetworkIdentifier</key>
            <string>cstr6suwn9.skadnetwork</string>
        </dict>
    </array>
    <key>LSApplicationQueriesSchemes</key>
    <array>
        <string>https</string>
        <string>http</string>
    </array>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
    </dict>
    <key>NSCameraUsageDescription</key>
    <string>يحتاج التطبيق إلى إذن الوصول للكاميرا لالتقاط صورة شخصية</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>يحتاج التطبيق إلى إذن الوصول لمعرض الصور لاختيار صورة شخصية</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>يحتاج التطبيق إلى إذن الوصول للميكروفون للمحادثة الصوتية أثناء التحدي</string>
    <key>NSUserTrackingUsageDescription</key>
    <string>نحتاج إلى إذنك لتحسين تجربة المستخدم وتقديم محتوى مخصص لك</string>
    <key>SKStoreReviewUsageDescription</key>
    <string>نستخدم خدمة المشتريات لتفعيل المزايا المتقدمة في التطبيق</string>
    <key>StoreKit</key>
    <dict>
        <key>AutomaticDownloads</key>
        <true/>
        <key>InAppPurchases</key>
        <true/>
        <key>SKPaymentQueue</key>
        <array>
            <dict>
                <key>default</key>
                <true/>
            </dict>
        </array>
    </dict>
    <key>StoreKit Configuration</key>
    <string>Configuration.storekit</string>
    <key>SKPaymentTransactionDescription</key>
    <string>اشتراك في الخدمات المتقدمة للتطبيق</string>
    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
    <key>UIBackgroundModes</key>
    <array>
        <string>fetch</string>
        <string>remote-notification</string>
        <string>audio</string>
    </array>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>UIRequiresFullScreen</key>
    <true/>
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationPortraitUpsideDown</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>
</dict>
</plist>