import 'dart:async';
import 'package:flutter/material.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutterquiz/services/voice_chat_service.dart';

/// وحدة تحكم المحادثة الصوتية
/// تستخدم لإدارة المحادثة الصوتية في صفحات التحديات
class VoiceChatController extends ChangeNotifier {
  // خدمة المحادثة الصوتية
  final VoiceChatService _voiceChatService = VoiceChatService();

  // حالة الميكروفون المحلي
  bool _isMicMuted = false;
  bool get isMicMuted => _isMicMuted;

  // حالة ميكروفونات المستخدمين البعيدين
  final Map<int, bool> _remoteMicMuted = {};

  // مستويات الصوت
  double _localVolumeLevel = 0.0;
  double get localVolumeLevel => _localVolumeLevel;

  final Map<int, double> _remoteVolumeLevels = {};

  // اشتراكات الأحداث
  StreamSubscription<List<AudioVolumeInfo>>? _volumeSubscription;
  StreamSubscription? _initStatusSubscription;
  StreamSubscription? _connectionStateSubscription;
  StreamSubscription? _errorSubscription;

  // حالة التخلص من الكائن
  bool _disposed = false;

  // حالة التهيئة
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  bool _isConnected = false;
  bool get isConnected => _isConnected;

  // الحصول على حالة ميكروفون مستخدم معين
  bool isRemoteMicMuted(int uid) {
    return _remoteMicMuted[uid] ?? false;
  }

  // الحصول على مستوى صوت مستخدم معين
  double getRemoteVolumeLevel(int uid) {
    return _remoteVolumeLevels[uid] ?? 0.0;
  }

  // الحصول على مستوى صوت مستخدم بعيد
  double getRemoteUserVolumeLevel(int uid) {
    return _remoteVolumeLevels[uid] ?? 0.0;
  }

  // الحصول على مستوى صوت المستخدم المحلي
  double getLocalUserVolumeLevel() {
    return _localVolumeLevel;
  }

  // تهيئة المحادثة الصوتية
  Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint("المحادثة الصوتية مهيأة بالفعل");
      return true;
    }

    try {
      debugPrint("بدء تهيئة المحادثة الصوتية...");

      // الاستماع إلى حالة التهيئة
      _initStatusSubscription = _voiceChatService.onInitializationStatus.listen((status) {
        if (!_disposed) {
          debugPrint("تم استلام تحديث حالة التهيئة: ${status.isInitialized}, ${status.isInProgress}, ${status.errorMessage}");
        }
      });

      // تهيئة محرك المحادثة الصوتية
      final initialized = await _voiceChatService.initialize();
      _isInitialized = initialized;

      if (initialized) {
        debugPrint("تم تهيئة المحادثة الصوتية بنجاح، إعداد مستمعي الأحداث");

        // الاستماع إلى تغييرات مستوى الصوت
        _volumeSubscription = _voiceChatService.onAudioVolumeIndication.listen((speakers) {
          if (_disposed) {
            debugPrint("تم تجاهل تحديث مستوى الصوت لأن وحدة التحكم تم التخلص منها");
            return;
          }

          try {
            // تحديث مستوى الصوت المحلي
            final localSpeaker = speakers.firstWhere(
              (speaker) => speaker.uid == 0, // 0 يمثل المستخدم المحلي
              orElse: () => const AudioVolumeInfo(uid: 0, volume: 0, vad: 0),
            );

            _localVolumeLevel = localSpeaker.volume! / 255.0; // تحويل القيمة إلى نطاق 0-1

            // تحديث مستويات صوت المستخدمين البعيدين
            for (final speaker in speakers) {
              if (speaker.uid != 0) { // تجاهل المستخدم المحلي
                _remoteVolumeLevels[speaker.uid!] = speaker.volume! / 255.0;
              }
            }

            // إخطار المستمعين بالتغييرات فقط إذا لم يتم التخلص من الكائن
            if (!_disposed) {
              notifyListeners();
            }
          } catch (e) {
            debugPrint("خطأ في معالجة تحديث مستوى الصوت: $e");
          }
        });

        // الاستماع إلى أحداث الخطأ
        _errorSubscription = _voiceChatService.onError.listen((error) {
          debugPrint("خطأ في محرك المحادثة الصوتية: ${error.code} - ${error.message}");
        });

        // الاستماع إلى أحداث تغيير حالة الاتصال
        _connectionStateSubscription = _voiceChatService.onConnectionStateChanged.listen((state) {
          debugPrint("تغيرت حالة الاتصال: $state");
          if (!_disposed) {
            _isConnected = state == ConnectionStateType.connectionStateConnected;
            notifyListeners();
          } else {
            debugPrint("تم تجاهل تحديث حالة الاتصال لأن وحدة التحكم تم التخلص منها");
          }
        });

        debugPrint("تم تهيئة المحادثة الصوتية وإعداد جميع المستمعين بنجاح");
      } else {
        debugPrint("فشل في تهيئة المحادثة الصوتية");
      }

      return initialized;
    } catch (e) {
      debugPrint("خطأ في تهيئة المحادثة الصوتية: $e");
      _isInitialized = false;
      return false;
    }
  }

  // الانضمام إلى قناة صوتية
  Future<bool> joinChannel(String channelId, int uid) async {
    debugPrint("محاولة الانضمام إلى القناة: $channelId بمعرف المستخدم: $uid");

    if (!_isInitialized) {
      debugPrint("المحادثة الصوتية غير مهيأة، جاري التهيئة...");
      final initialized = await initialize();
      if (!initialized) {
        debugPrint("فشل في تهيئة المحادثة الصوتية، لا يمكن الانضمام إلى القناة");
        return false;
      }
    }

    try {
      debugPrint("جاري الانضمام إلى القناة...");
      final result = await _voiceChatService.joinChannel(channelId, uid);
      _isConnected = result.success;

      debugPrint("نتيجة الانضمام إلى القناة: ${result.success}");
      if (!result.success) {
        if (result.errorMessage != null) {
          debugPrint("سبب الفشل: ${result.errorMessage}");
        }
        if (result.errorCode != null) {
          debugPrint("رمز الخطأ: ${result.errorCode}");
        }
        if (result.permissionStatus != null) {
          debugPrint("حالة الأذونات: ${result.permissionStatus}");
        }
      } else {
        debugPrint("تم الانضمام إلى القناة بنجاح");
      }

      return result.success;
    } catch (e) {
      debugPrint("خطأ غير متوقع في الانضمام إلى القناة: $e");
      _isConnected = false;
      return false;
    }
  }

  // كتم/إلغاء كتم الميكروفون المحلي
  Future<void> toggleMute() async {
    if (_disposed) {
      debugPrint("تحذير: محاولة كتم/إلغاء كتم الميكروفون بعد التخلص من وحدة التحكم");
      return;
    }

    try {
      _isMicMuted = !_isMicMuted;
      debugPrint("محاولة ${_isMicMuted ? 'كتم' : 'إلغاء كتم'} الميكروفون المحلي");

      if (!_isInitialized || !_isConnected) {
        debugPrint("تحذير: المحادثة الصوتية غير مهيأة أو غير متصلة، لا يمكن كتم/إلغاء كتم الميكروفون");
        if (!_disposed) {
          notifyListeners();
        }
        return;
      }

      await _voiceChatService.muteLocalAudio(_isMicMuted);
      debugPrint("تم ${_isMicMuted ? 'كتم' : 'إلغاء كتم'} الميكروفون المحلي بنجاح");
      if (!_disposed) {
        notifyListeners();
      }
    } catch (e) {
      debugPrint("خطأ في كتم/إلغاء كتم الميكروفون: $e");
      // إعادة الحالة إلى ما كانت عليه في حالة الفشل
      _isMicMuted = !_isMicMuted;
      if (!_disposed) {
        notifyListeners();
      }
    }
  }

  // كتم/إلغاء كتم ميكروفون مستخدم معين
  Future<void> toggleRemoteMute(int uid) async {
    if (_disposed) {
      debugPrint("تحذير: محاولة كتم/إلغاء كتم ميكروفون المستخدم $uid بعد التخلص من وحدة التحكم");
      return;
    }

    try {
      final newMuteState = !(_remoteMicMuted[uid] ?? false);
      debugPrint("محاولة ${newMuteState ? 'كتم' : 'إلغاء كتم'} ميكروفون المستخدم $uid");

      if (!_isInitialized || !_isConnected) {
        debugPrint("تحذير: المحادثة الصوتية غير مهيأة أو غير متصلة، لا يمكن كتم/إلغاء كتم ميكروفون المستخدم");
        return;
      }

      _remoteMicMuted[uid] = newMuteState;
      await _voiceChatService.muteRemoteAudio(uid, newMuteState);
      debugPrint("تم ${newMuteState ? 'كتم' : 'إلغاء كتم'} ميكروفون المستخدم $uid بنجاح");
      if (!_disposed) {
        notifyListeners();
      }
    } catch (e) {
      debugPrint("خطأ في كتم/إلغاء كتم ميكروفون المستخدم $uid: $e");
      // إعادة الحالة إلى ما كانت عليه في حالة الفشل
      _remoteMicMuted.remove(uid);
      if (!_disposed) {
        notifyListeners();
      }
    }
  }

  // مغادرة القناة الصوتية
  Future<void> leaveChannel() async {
    if (_disposed) {
      debugPrint("تحذير: محاولة مغادرة القناة بعد التخلص من وحدة التحكم");
      return;
    }

    if (!_isInitialized || !_isConnected) {
      debugPrint("تحذير: المحادثة الصوتية غير مهيأة أو غير متصلة، لا حاجة لمغادرة القناة");
      _isConnected = false;
      if (!_disposed) {
        notifyListeners();
      }
      return;
    }

    try {
      debugPrint("محاولة مغادرة القناة الصوتية...");
      await _voiceChatService.leaveChannel();
      _isConnected = false;
      debugPrint("تم مغادرة القناة الصوتية بنجاح");
      if (!_disposed) {
        notifyListeners();
      }
    } catch (e) {
      debugPrint("خطأ في مغادرة القناة: $e");
      // حتى في حالة الخطأ، نعتبر أننا غادرنا القناة
      _isConnected = false;
      if (!_disposed) {
        notifyListeners();
      }
    }
  }

  // تبديل وضع مكبر الصوت مع آلية إعادة المحاولة
  Future<bool> toggleSpeakerphone(bool enable, {int maxRetries = 3, int delayMs = 500}) async {
    if (_disposed) {
      debugPrint("تحذير: محاولة تبديل وضع مكبر الصوت بعد التخلص من وحدة التحكم");
      return false;
    }

    if (!_isInitialized) {
      debugPrint("تحذير: المحادثة الصوتية غير مهيأة، لا يمكن تبديل وضع مكبر الصوت");
      return false;
    }

    if (!_isConnected) {
      debugPrint("تحذير: المحادثة الصوتية غير متصلة، محاولة تبديل وضع مكبر الصوت على أي حال");
      // نستمر حتى لو لم تكن متصلاً، لأن بعض الأجهزة قد تسمح بذلك
    }

    try {
      debugPrint("محاولة ${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت...");
      final success = await _voiceChatService.setSpeakerphone(
        enable,
        maxRetries: maxRetries,
        delayMs: delayMs,
      );

      if (success) {
        debugPrint("تم ${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت بنجاح");
      } else {
        debugPrint("فشل في ${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت بعد $maxRetries محاولات");
      }

      return success;
    } catch (e) {
      debugPrint("خطأ في تبديل وضع مكبر الصوت: $e");
      return false;
    }
  }

  // تمكين مكبر الصوت بعد الانضمام إلى القناة
  Future<bool> enableSpeakerphoneAfterJoin({int delayMs = 1000}) async {
    if (_disposed) {
      debugPrint("تحذير: محاولة تمكين مكبر الصوت بعد التخلص من وحدة التحكم");
      return false;
    }

    if (!_isInitialized) {
      debugPrint("تحذير: المحادثة الصوتية غير مهيأة، لا يمكن تمكين مكبر الصوت");
      return false;
    }

    try {
      debugPrint("محاولة تمكين مكبر الصوت بعد الانضمام...");
      final success = await _voiceChatService.enableSpeakerphoneAfterJoin(
        delayMs: delayMs,
      );

      if (success) {
        debugPrint("تم تمكين مكبر الصوت بعد الانضمام بنجاح");
      } else {
        debugPrint("فشل في تمكين مكبر الصوت بعد الانضمام");
      }

      return success;
    } catch (e) {
      debugPrint("خطأ في تمكين مكبر الصوت بعد الانضمام: $e");
      return false;
    }
  }

  @override
  void dispose() {
    debugPrint("التخلص من وحدة تحكم المحادثة الصوتية...");

    // تعيين حالة التخلص من الكائن إلى true أولاً لمنع استدعاء notifyListeners
    _disposed = true;

    // إلغاء الاشتراكات بشكل آمن ومتزامن
    try {
      _volumeSubscription?.cancel();
      _volumeSubscription = null;

      _initStatusSubscription?.cancel();
      _initStatusSubscription = null;

      _connectionStateSubscription?.cancel();
      _connectionStateSubscription = null;

      _errorSubscription?.cancel();
      _errorSubscription = null;

      debugPrint("تم إلغاء جميع الاشتراكات بنجاح");
    } catch (e) {
      debugPrint("خطأ في إلغاء الاشتراكات: $e");
    }

    // تعيين المتغيرات إلى قيم افتراضية
    _isConnected = false;
    _isInitialized = false;
    _isMicMuted = false;
    _localVolumeLevel = 0.0;

    // محاولة مغادرة القناة بشكل متزامن قبل التخلص من وحدة التحكم
    try {
      if (_voiceChatService.isInitialized && _voiceChatService.isConnected) {
        debugPrint("[VoiceChat] جاري مغادرة القناة بشكل متزامن");
        _voiceChatService.leaveChannelSync();
        debugPrint("تم مغادرة القناة الصوتية بنجاح");
      }
    } catch (e) {
      debugPrint("خطأ في مغادرة القناة: $e");
    }

    // تنظيف البيانات
    _remoteVolumeLevels.clear();
    _remoteMicMuted.clear();

    debugPrint("تم التخلص من وحدة تحكم المحادثة الصوتية بنجاح");
    super.dispose();
  }
}
