// import 'package:flutter/material.dart';
// import 'package:flutter_tex/flutter_tex.dart';

// class ExplanationDialog extends StatefulWidget {
//   final String explanation;
//   final bool useLatex;

//   const ExplanationDialog({
//     required this.explanation,
//     this.useLatex = false,
//     Key? key,
//   }) : super(key: key);

//   @override
//   State<ExplanationDialog> createState() => _ExplanationDialogState();
// }

// class _ExplanationDialogState extends State<ExplanationDialog>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _controller;
//   late Animation<double> _scaleAnimation;

//   @override
//   void initState() {
//     super.initState();
//     _controller = AnimationController(
//       duration: const Duration(milliseconds: 300),
//       vsync: this,
//     );
//     _scaleAnimation = CurvedAnimation(
//       parent: _controller,
//       curve: Curves.easeInOut,
//     );
//     _controller.forward();
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return ScaleTransition(
//       scale: _scaleAnimation,
//       child: Dialog(
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
//         child: Container(
//           padding: const EdgeInsets.all(20),
//           decoration: BoxDecoration(
//             gradient: LinearGradient(
//               colors: [
//                 Theme.of(context).primaryColor.withOpacity(0.1),
//                 Colors.white,
//               ],
//               begin: Alignment.topLeft,
//               end: Alignment.bottomRight,
//             ),
//             borderRadius: BorderRadius.circular(20),
//           ),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Container(
//                 padding: const EdgeInsets.all(8),
//                 decoration: BoxDecoration(
//                   color: Theme.of(context).primaryColor.withOpacity(0.1),
//                   borderRadius: BorderRadius.circular(10),
//                 ),
//                 child: Row(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Icon(
//                       Icons.lightbulb_outline,
//                       color: Theme.of(context).primaryColor,
//                     ),
//                     const SizedBox(width: 8),
//                     Text(
//                       "الشرح",
//                       style: TextStyle(
//                         color: Theme.of(context).primaryColor,
//                         fontSize: 18,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               const SizedBox(height: 20),
//               widget.useLatex
//                   ? TeXView(
//                       child: TeXViewDocument(widget.explanation),
//                       style: TeXViewStyle(
//                         margin: const TeXViewMargin.all(10),
//                         padding: const TeXViewPadding.all(10),
//                         backgroundColor: Colors.grey[50],
//                         borderRadius: const TeXViewBorderRadius.all(10),
//                         textAlign: TeXViewTextAlign.right,
//                       ),
//                     )
//                   : Text(
//                       widget.explanation,
//                       style: const TextStyle(
//                         fontSize: 16,
//                         height: 1.5,
//                       ),
//                       textAlign: TextAlign.right,
//                     ),
//               const SizedBox(height: 20),
//               ElevatedButton(
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Theme.of(context).primaryColor,
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(10),
//                   ),
//                 ),
//                 onPressed: () => Navigator.pop(context),
//                 child: const Padding(
//                   padding: EdgeInsets.symmetric(
//                     horizontal: 20,
//                     vertical: 10,
//                   ),
//                   child: Text(
//                     "فهمت",
//                     style: TextStyle(color: Colors.white),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
