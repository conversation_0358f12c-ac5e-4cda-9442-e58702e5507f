<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 500 500"><defs><clipPath id="freepik--clip-path--inject-143"><path d="M211.18,151.51c12.06,16.26,37,19,61.56,21.42s49.07,0,52.43-8.34c-1.89,17.68-7.63,44-62.54,35.06C202.06,189.77,211.18,151.51,211.18,151.51Z" style="fill:none"></path></clipPath><clipPath id="freepik--clip-path-2--inject-143"><path d="M246.27,92.62c-2.15,6.24-10,12.39-20.28,13.12-5.83-2.43-19.24-24.72-17.28-37.41S235.8,38.86,240.84,38c4.6-.81-15.34,19.68-14.35,30.56S246.27,92.62,246.27,92.62Z" style="fill:none"></path></clipPath><clipPath id="freepik--clip-path-3--inject-143"><path d="M305.75,105.76c-.24,5.4,3.9,12.45,11.55,16.1,5.18-.1,22.11-13.11,24.42-23.39s-9.8-27.4-15.42-32.83c-3-2.91,5.81,19.63,1.79,27.65S305.75,105.76,305.75,105.76Z" style="fill:none"></path></clipPath></defs><g id="freepik--background-complete--inject-143"><rect y="382.4" width="500" height="0.25" style="fill:#ebebeb"></rect><rect x="52.46" y="391.92" width="33.12" height="0.25" style="fill:#ebebeb"></rect><rect x="171.14" y="389.21" width="42.53" height="0.25" style="fill:#ebebeb"></rect><rect x="93.47" y="395.43" width="19.19" height="0.25" style="fill:#ebebeb"></rect><rect x="434.67" y="399.53" width="15.23" height="0.25" style="fill:#ebebeb"></rect><rect x="391.47" y="399.53" width="34.29" height="0.25" style="fill:#ebebeb"></rect><rect x="308" y="392.33" width="23.83" height="0.25" style="fill:#ebebeb"></rect><path d="M237,337.8H43.91a5.71,5.71,0,0,1-5.7-5.71V60.66A5.71,5.71,0,0,1,43.91,55H237a5.71,5.71,0,0,1,5.71,5.71V332.09A5.71,5.71,0,0,1,237,337.8ZM43.91,55.2a5.46,5.46,0,0,0-5.45,5.46V332.09a5.46,5.46,0,0,0,5.45,5.46H237a5.47,5.47,0,0,0,5.46-5.46V60.66A5.47,5.47,0,0,0,237,55.2Z" style="fill:#ebebeb"></path><path d="M453.31,337.8H260.21a5.72,5.72,0,0,1-5.71-5.71V60.66A5.72,5.72,0,0,1,260.21,55h193.1A5.71,5.71,0,0,1,459,60.66V332.09A5.71,5.71,0,0,1,453.31,337.8ZM260.21,55.2a5.47,5.47,0,0,0-5.46,5.46V332.09a5.47,5.47,0,0,0,5.46,5.46h193.1a5.47,5.47,0,0,0,5.46-5.46V60.66a5.47,5.47,0,0,0-5.46-5.46Z" style="fill:#ebebeb"></path><path d="M103.14,142.1s4.27-4.57,10.33-4.78c4.23-3.93,12.1-3.26,13.82-.67,6.21-4.13,13.09-5.46,19.23,2.31-.84-8.56-10.24-26.37-39.36-11.48-2.63-2.39-3.45-3.17-3.45-3.17s-1.18-3.43-1.95-3.59,0,3.79,0,3.79a5.56,5.56,0,0,0-4.36.91s-.87-3.92-1.52-3.48-.36,4.08-.36,4.08-.44,1-1.9,4.28c-32.63-2-34.15,18-31.5,26.23,2.53-9.57,9.36-11.1,16.71-9.79.55-3.06,7.49-6.82,12.94-4.9C97.41,139.61,103.14,142.1,103.14,142.1Z" style="fill:#e6e6e6"></path><path d="M146.1,95.32a11.09,11.09,0,0,1,7.7-.2c3.69-1.31,8.4,1.22,8.78,3.29,5-.91,9.6.11,11.34,6.59,1.77-5.55.7-19.15-21.4-17.69-1-2.2-1.3-2.9-1.3-2.9s.18-2.45-.25-2.76S150,84,150,84a4.29,4.29,0,0,0-1.41-.61,4.14,4.14,0,0,0-1.54,0s.51-2.68,0-2.57-1.32,2.44-1.32,2.44-.55.52-2.33,2.15c-19.75-10-26.08,2.07-26.62,7.87,4.13-5.27,8.79-4.39,13-1.61,1.16-1.75,6.49-2.23,9.36.42A11.14,11.14,0,0,1,146.1,95.32Z" style="fill:#e6e6e6"></path><path d="M411.77,164.09a11.11,11.11,0,0,1,7.7-.21c3.68-1.31,8.4,1.22,8.77,3.29,5-.9,9.61.12,11.34,6.59,1.78-5.54.7-19.14-21.4-17.68-1-2.2-1.29-2.9-1.29-2.9s.18-2.46-.26-2.76-1,2.36-1,2.36a3.83,3.83,0,0,0-3-.61s.51-2.67,0-2.56-1.31,2.43-1.31,2.43-.56.53-2.33,2.16c-19.76-10-26.08,2.07-26.63,7.87,4.14-5.28,8.8-4.4,13-1.61,1.16-1.76,6.49-2.23,9.36.42A11.12,11.12,0,0,1,411.77,164.09Z" style="fill:#e6e6e6"></path><path d="M75.81,74.69a5,5,0,0,1,3.3-1.1c1.42-1,3.8-.58,4.23.27,2-1,4.17-1.22,5.77,1.35,0-2.63-2.22-8.37-11.58-4.83-.72-.81-.94-1.08-.94-1.08s-.25-1.08-.48-1.16S76,69.29,76,69.29a1.78,1.78,0,0,0-1.36.13s-.13-1.22-.34-1.1S74,69.54,74,69.54s-.17.3-.73,1.24c-9.86-1.73-11,4.33-10.47,6.91,1.09-2.82,3.22-3.06,5.41-2.41.27-.91,2.52-1.82,4.11-1A5,5,0,0,1,75.81,74.69Z" style="fill:#e6e6e6"></path><path d="M394.06,87.49c-9.46,8.58-26.52,15.69-30.45-2.47a104.7,104.7,0,0,0,13.27,2.21c-.43,1.71,1.77,4.61,5.27,4.44,3.06-.15,4.87-2.3,3.43-3.9A75.86,75.86,0,0,0,394.06,87.49Z" style="fill:#e6e6e6"></path><path d="M402.74,85.59a76.8,76.8,0,0,0,7.81-3.29c-.61,2,1.91,3.24,4.75,2.1,3.27-1.32,4-4.88,2.94-6.25a105.55,105.55,0,0,0,11.12-7.55C433.39,88.74,414.92,89.42,402.74,85.59Z" style="fill:#e6e6e6"></path><path d="M356.55,99.07l-1.69,6.45c-3-4.08-4.68-8.15-4.78-11.43A22,22,0,0,0,356.55,99.07Z" style="fill:#ebebeb"></path><path d="M367.58,103.09l-8.13,7.58a40.42,40.42,0,0,1-3.82-4.15l1.82-7A41.86,41.86,0,0,0,367.58,103.09Z" style="fill:#ebebeb"></path><path d="M375,104.18l7,18.51a50,50,0,0,1-21.76-11.34l8.59-8A60.3,60.3,0,0,0,375,104.18Z" style="fill:#ebebeb"></path><path d="M393.34,103.43l-9.2,19.75-1-.21-7-18.71A81,81,0,0,0,393.34,103.43Z" style="fill:#ebebeb"></path><path d="M397.86,102.66l14,19.33-.58.15a61.18,61.18,0,0,1-26.11,1.24l9.37-20.13C395.62,103.08,396.72,102.89,397.86,102.66Z" style="fill:#ebebeb"></path><path d="M417.61,96.91l-4.85,24.61-13.82-19.07c1.34-.26,2.73-.56,4.14-.89A88.63,88.63,0,0,0,417.61,96.91Z" style="fill:#ebebeb"></path><path d="M420.11,95.8l17.35,11.53c-5.14,6.07-12.91,11-23.67,14.12l4.93-25C419.19,96.23,419.65,96,420.11,95.8Z" style="fill:#ebebeb"></path><path d="M433,87.22l6.84,16.91c-.55.82-1.13,1.63-1.76,2.42L421.16,95.29A47.59,47.59,0,0,0,433,87.22Z" style="fill:#ebebeb"></path><path d="M441.65,65.68a62.29,62.29,0,0,1,2,6.23l-1.8-.54A13.47,13.47,0,0,0,441.65,65.68Z" style="fill:#ebebeb"></path><path d="M445.19,80.33l-7.8,1.85a25.8,25.8,0,0,0,4.28-9.82l2.26.69A50.25,50.25,0,0,1,445.19,80.33Z" style="fill:#ebebeb"></path><path d="M440.51,103.09l-6.72-16.63a34.68,34.68,0,0,0,2.68-3l8.81-2.09C445.88,89.25,444.41,96.67,440.51,103.09Z" style="fill:#ebebeb"></path></g><g id="freepik--Shadow--inject-143"><ellipse id="freepik--path--inject-143" cx="250" cy="416.24" rx="193.89" ry="11.32" style="fill:#f5f5f5"></ellipse></g><g id="freepik--Error--inject-143"><path d="M208,407.63l-15.8,1.17-.44-5.84,14.25-1-.44-6-14.25,1-.35-4.7,15.36-1.13-.46-6.33-24.55,1.81L182.94,409h25.12Z" style="fill:#407BFF"></path><path d="M237.6,403.57a12.88,12.88,0,0,0-1.42-1.75,9.53,9.53,0,0,0-1.58-1.45,9.34,9.34,0,0,0-2.47-.92,11,11,0,0,0,3.13-1.41,8.09,8.09,0,0,0,2.68-3.11,8.22,8.22,0,0,0,.74-4.34A8.14,8.14,0,0,0,237,385.9a6.85,6.85,0,0,0-3.82-2.43,21.92,21.92,0,0,0-6.54-.26l-15.26,1.13,1.82,24.7h9.25l-.58-7.77.81-.06a4,4,0,0,1,2.3.52,7.26,7.26,0,0,1,1.83,2.16l3.29,5.15H241Zm-8.52-9.81a2.38,2.38,0,0,1-1.41,1,13.77,13.77,0,0,1-2.4.59l-3.86.28-.45-6,4-.29a5.5,5.5,0,0,1,3.47.52,2.73,2.73,0,0,1,1.08,2.13A2.78,2.78,0,0,1,229.08,393.76Z" style="fill:#407BFF"></path><path d="M269.8,401.19a11,11,0,0,0-1.42-1.74A8.37,8.37,0,0,0,266.8,398a9.11,9.11,0,0,0-2.46-.92,10.9,10.9,0,0,0,3.12-1.41,7.92,7.92,0,0,0,3.43-7.45,8.17,8.17,0,0,0-1.74-4.7,7,7,0,0,0-3.81-2.43,22,22,0,0,0-6.54-.25L243.53,382l2,27.07h9.24L254,398.89l.81-.05a3.76,3.76,0,0,1,2.29.52,6.86,6.86,0,0,1,1.83,2.16l4.81,7.52h10.84Zm-8.51-9.8a2.45,2.45,0,0,1-1.42,1,14.63,14.63,0,0,1-2.39.58l-3.87.29-.44-6,4-.31a5.53,5.53,0,0,1,3.47.52,2.7,2.7,0,0,1,1.07,2.14A2.73,2.73,0,0,1,261.29,391.39Z" style="fill:#407BFF"></path><path d="M319.91,390.86q-.53-7.16-4.83-10.83c-2.85-2.45-6.76-3.5-11.69-3.14s-8.47,2-11,4.88-3.57,6.76-3.21,11.6a16.84,16.84,0,0,0,2.67,8.5,12.53,12.53,0,0,0,5.71,4.66,18.81,18.81,0,0,0,8.41,1,16.78,16.78,0,0,0,8.11-2.46,12.49,12.49,0,0,0,4.67-5.55A18.25,18.25,0,0,0,319.91,390.86Zm-10.28,7.57a6.34,6.34,0,0,1-9.11.63c-1.2-1.22-1.92-3.34-2.14-6.33s.18-5.22,1.2-6.61a5.55,5.55,0,0,1,4.3-2.29,5.79,5.79,0,0,1,4.73,1.6c1.21,1.2,1.93,3.19,2.14,6C311,394.73,310.61,397.07,309.63,398.43Z" style="fill:#407BFF"></path><path d="M351.74,394.84a9.12,9.12,0,0,0,.57-9.07,6.87,6.87,0,0,0-3.34-3,21.93,21.93,0,0,0-6.41-1.37l-15.22-1.5L324.44,409h9.28l1.06-10.72.8.08a3.86,3.86,0,0,1,2.17.92,6.7,6.7,0,0,1,1.43,2.44l3.06,7.28h10l-2.28-5.75a11.32,11.32,0,0,0-1.1-2,8.79,8.79,0,0,0-1.3-1.7,9,9,0,0,0-2.28-1.33,11.11,11.11,0,0,0,3.33-.86A8.22,8.22,0,0,0,351.74,394.84ZM344,390.52a2.73,2.73,0,0,1-.74,1.65,2.5,2.5,0,0,1-1.58.79,12.63,12.63,0,0,1-2.46.15l-3.85-.37.59-6,4,.39a5.56,5.56,0,0,1,3.33,1.1A2.79,2.79,0,0,1,344,390.52Z" style="fill:#407BFF"></path><g style="opacity:0.5"><path d="M208,407.63l-15.8,1.17-.44-5.84,14.25-1-.44-6-14.25,1-.35-4.7,15.36-1.13-.46-6.33-24.55,1.81L182.94,409h25.12Z" style="fill:#fff"></path><path d="M237.6,403.57a12.88,12.88,0,0,0-1.42-1.75,9.53,9.53,0,0,0-1.58-1.45,9.34,9.34,0,0,0-2.47-.92,11,11,0,0,0,3.13-1.41,8.09,8.09,0,0,0,2.68-3.11,8.22,8.22,0,0,0,.74-4.34A8.14,8.14,0,0,0,237,385.9a6.85,6.85,0,0,0-3.82-2.43,21.92,21.92,0,0,0-6.54-.26l-15.26,1.13,1.82,24.7h9.25l-.58-7.77.81-.06a4,4,0,0,1,2.3.52,7.26,7.26,0,0,1,1.83,2.16l3.29,5.15H241Zm-8.52-9.81a2.38,2.38,0,0,1-1.41,1,13.77,13.77,0,0,1-2.4.59l-3.86.28-.45-6,4-.29a5.5,5.5,0,0,1,3.47.52,2.73,2.73,0,0,1,1.08,2.13A2.78,2.78,0,0,1,229.08,393.76Z" style="fill:#fff"></path><path d="M269.8,401.19a11,11,0,0,0-1.42-1.74A8.37,8.37,0,0,0,266.8,398a9.11,9.11,0,0,0-2.46-.92,10.9,10.9,0,0,0,3.12-1.41,7.92,7.92,0,0,0,3.43-7.45,8.17,8.17,0,0,0-1.74-4.7,7,7,0,0,0-3.81-2.43,22,22,0,0,0-6.54-.25L243.53,382l2,27.07h9.24L254,398.89l.81-.05a3.76,3.76,0,0,1,2.29.52,6.86,6.86,0,0,1,1.83,2.16l4.81,7.52h10.84Zm-8.51-9.8a2.45,2.45,0,0,1-1.42,1,14.63,14.63,0,0,1-2.39.58l-3.87.29-.44-6,4-.31a5.53,5.53,0,0,1,3.47.52,2.7,2.7,0,0,1,1.07,2.14A2.73,2.73,0,0,1,261.29,391.39Z" style="fill:#fff"></path><path d="M319.91,390.86q-.53-7.16-4.83-10.83c-2.85-2.45-6.76-3.5-11.69-3.14s-8.47,2-11,4.88-3.57,6.76-3.21,11.6a16.84,16.84,0,0,0,2.67,8.5,12.53,12.53,0,0,0,5.71,4.66,18.81,18.81,0,0,0,8.41,1,16.78,16.78,0,0,0,8.11-2.46,12.49,12.49,0,0,0,4.67-5.55A18.25,18.25,0,0,0,319.91,390.86Zm-10.28,7.57a6.34,6.34,0,0,1-9.11.63c-1.2-1.22-1.92-3.34-2.14-6.33s.18-5.22,1.2-6.61a5.55,5.55,0,0,1,4.3-2.29,5.79,5.79,0,0,1,4.73,1.6c1.21,1.2,1.93,3.19,2.14,6C311,394.73,310.61,397.07,309.63,398.43Z" style="fill:#fff"></path><path d="M351.74,394.84a9.12,9.12,0,0,0,.57-9.07,6.87,6.87,0,0,0-3.34-3,21.93,21.93,0,0,0-6.41-1.37l-15.22-1.5L324.44,409h9.28l1.06-10.72.8.08a3.86,3.86,0,0,1,2.17.92,6.7,6.7,0,0,1,1.43,2.44l3.06,7.28h10l-2.28-5.75a11.32,11.32,0,0,0-1.1-2,8.79,8.79,0,0,0-1.3-1.7,9,9,0,0,0-2.28-1.33,11.11,11.11,0,0,0,3.33-.86A8.22,8.22,0,0,0,351.74,394.84ZM344,390.52a2.73,2.73,0,0,1-.74,1.65,2.5,2.5,0,0,1-1.58.79,12.63,12.63,0,0,1-2.46.15l-3.85-.37.59-6,4,.39a5.56,5.56,0,0,1,3.33,1.1A2.79,2.79,0,0,1,344,390.52Z" style="fill:#fff"></path></g></g><g id="freepik--Character--inject-143"><path d="M307.28,336.74c2,2.67,3.31,9.1,0,9.87s-2-9.7-2-9.7C304.36,336,305.12,333.8,307.28,336.74Z" style="fill:#407BFF"></path><path d="M299.92,391.48c2.11,1.48,4.7,5.88,2.42,7.28s-3.89-6.64-3.89-6.64C297.59,391.68,297.6,389.86,299.92,391.48Z" style="fill:#407BFF"></path><path d="M261.37,393.31c-.14,2.57-2.5,7.1-4.9,5.9s3.58-6.81,3.58-6.81C260,391.43,261.52,390.48,261.37,393.31Z" style="fill:#407BFF"></path><path d="M306.23,332.45c3.36.46,7.65,4.11,4.19,7.2s-6.43-5.54-6.43-5.54C302.63,334.12,302.53,331.94,306.23,332.45Z" style="fill:#407BFF"></path><path d="M306.19,335.81c3.09,17.23-6.51,36.58-3.69,55.14.77,5.11-7.39-.26-7.39-.26s-19-1.85-24.1.13c-.88,4.79-14.38,8-15.19-1.19s-1.57-32-1.57-32S291.86,331.7,306.19,335.81Z" style="fill:#407BFF"></path><path d="M260.78,353.36c-4,2.52-6.53,4.26-6.53,4.26s.22,6.65.55,14.21c8.78-5.35,16.26-14,16.26-14A41,41,0,0,0,260.78,353.36Z" style="opacity:0.2"></path><path d="M259.93,391.74c-.7,9.15-9.2,22.16-6.14,26s53.28,3.52,60,0-15.31-18-14.95-30.12C260.64,377.67,259.93,391.74,259.93,391.74Z" style="fill:#407BFF"></path><path d="M260.13,414.32c-4.49-4.49-12.25.11-13.43,5.51,8.92,1.87,15.07-.82,15.07-.82A6.28,6.28,0,0,0,260.13,414.32Z" style="fill:#263238"></path><path d="M308.9,413.16c4.48-4.49,12.25.12,13.42,5.51-8.91,1.88-15.07-.82-15.07-.82A6.26,6.26,0,0,1,308.9,413.16Z" style="fill:#263238"></path><path d="M298.12,414.18c4.24-5.38,11.58.14,12.69,6.61-8.42,2.25-14.24-1-14.24-1A9,9,0,0,1,298.12,414.18Z" style="fill:#263238"></path><path d="M288.36,413.49c9.87-1.4,11.46,3,7.69,8.57-10.53.18-12.15-2.25-12.15-2.25S281.39,414.49,288.36,413.49Z" style="fill:#263238"></path><path d="M166.61,304.41c-3.21.82-8.34,4.92-6.23,7.65s7.71-6.21,7.71-6.21C169.32,305.71,170.14,303.52,166.61,304.41Z" style="fill:#407BFF"></path><path d="M141.2,349.06c-2.51.6-6.54,3.73-4.93,5.88s6.06-4.75,6.06-4.75C143.29,350.1,144,348.4,141.2,349.06Z" style="fill:#407BFF"></path><path d="M173.13,373.73c-1.66,2-3,6.89-.46,7.65s2-7.42,2-7.42C175.43,373.31,175,371.56,173.13,373.73Z" style="fill:#407BFF"></path><path d="M198.88,350.18c-1.89,1.76-3.87,6.46-1.41,7.54s2.95-7.1,2.95-7.1C201.22,350.06,201,348.26,198.88,350.18Z" style="fill:#407BFF"></path><path d="M170.15,301.77c-2.89-1.78-8.52-1.69-7.82,2.89s8.49-.19,8.49-.19C171.87,305.34,173.33,303.72,170.15,301.77Z" style="fill:#407BFF"></path><path d="M180.44,293.78c-13.36,11.32-25.31,40.36-39.3,52.88-3.85,3.45,5.86,4.5,5.86,4.5s18.13,8.36,20.81,13.14c-2.38,4.25,6,15.31,12.47,8.76s21.6-23.68,21.6-23.68S194.1,299.74,180.44,293.78Z" style="fill:#407BFF"></path><path d="M114.68,380.11c-3.69-5.16-12.11-1.88-14.15,3.25,8.49,3.3,15,1.65,15,1.65A6.28,6.28,0,0,0,114.68,380.11Z" style="fill:#263238"></path><path d="M174.58,373.09c-6,7-1.64,31.81-6.24,33.6S90,394.93,116.59,376.39c6.2-4.32,20.36-16.11,24.49-27.48C180.24,353.7,174.58,373.09,174.58,373.09Z" style="fill:#407BFF"></path><path d="M165.91,400.54c5.55,3.09,3.26,11.82-1.6,14.43-4.26-8.05-3.36-14.71-3.36-14.71A6.28,6.28,0,0,1,165.91,400.54Z" style="fill:#263238"></path><path d="M120.67,386.12c-3.88-7.8-13.84-2.54-16.69,3.14,6.13,6.2,15.19,2.5,15.19,2.5S122,388.77,120.67,386.12Z" style="fill:#263238"></path><path d="M128.7,390.62c-8.17-5.7-15.09.89-14.22,7.56,9.31,4.9,15.36.08,15.36.08S134.48,394.65,128.7,390.62Z" style="fill:#263238"></path><path d="M232.42,363.48c51.31,8.81,86.59-23.88,125.3-103.77,21.54-44.46-4.65-75-19-80.72,6.51-14.35-.06-36.42-9.5-45.89,3.54,3,9.07-.11,9.07-.11-7-3.81-16.4-38.27-58.31-46.42-38.23-7.43-57.48,13.93-71.43,21.76,0,0,3.1,5.65,9.49,5.68-12.05,5.78-23.24,21.71-24.25,40.1-15.45.56-46.85,20.39-44.86,69.75C152.5,312.56,181.11,354.67,232.42,363.48Z" style="fill:#407BFF"></path><path d="M218.7,142.68c13.39-1.79,26.73,2.07,33,7.7C238.53,144.94,218.7,142.68,218.7,142.68Z" style="opacity:0.1"></path><path d="M280.93,159.87c11.47-2.4,23.26.09,29.08,4.55C298.23,160.57,280.93,159.87,280.93,159.87Z" style="opacity:0.1"></path><path d="M234.38,150.38c5.12-2.71,10.84-2.21,14.86,1.3A93.81,93.81,0,0,0,234.38,150.38Z" style="opacity:0.1"></path><path d="M148.87,222.61c9.3-10.85,16.9-25.88,18.31-35.17a64.39,64.39,0,0,0-13.4,2.31C150.43,198.76,148.49,209.65,148.87,222.61Z" style="opacity:0.2"></path><path d="M365.69,221.73a67.69,67.69,0,0,0-10.37-4.55c-.9,8.09,1.57,21.53,6.26,33.36A68.44,68.44,0,0,0,365.69,221.73Z" style="opacity:0.2"></path><path d="M221.53,151.53c-2.26-4.65-6.42-7.63-10.94-7.48-3.29.11-12.63.53-5.8,16.18C212.36,177.58,231.18,171.32,221.53,151.53Z" style="opacity:0.1"></path><path d="M323.15,158.83c3.86-1.71,7.28-1,8.74,2.22,1.06,2.32,4,9-8.69,15.27C309.12,183.29,306.73,166.1,323.15,158.83Z" style="opacity:0.1"></path><path d="M261.69,144.6a12,12,0,0,1-3.1,4.29,3.8,3.8,0,0,1-4.83.17,45.73,45.73,0,0,0-19.84-8,3.84,3.84,0,0,1-2.85-5.49l0,0C239,120.25,267.81,130,261.69,144.6Z" style="fill:#fff"></path><path d="M249.65,134.76c4.84-5.83,14.29,1.6,11,7.24C256.87,148.38,243.67,142,249.65,134.76Z" style="fill:#263238"></path><path d="M306.24,158.84a116.12,116.12,0,0,0-25.34-1,7.47,7.47,0,0,1-7-3.65A15.89,15.89,0,0,1,272,149c-3.44-20.75,36.72-24.49,42.23-2.39a26.31,26.31,0,0,1,.72,4.24A7.53,7.53,0,0,1,306.24,158.84Z" style="fill:#fff"></path><path d="M286.13,143.15c-2.34-3.33-8.79-.12-6.88,3.12S289.23,147.54,286.13,143.15Z" style="fill:#263238"></path><path d="M211.18,151.51c12.06,16.26,37,19,61.56,21.42s49.07,0,52.43-8.34c-1.89,17.68-7.63,44-62.54,35.06C202.06,189.77,211.18,151.51,211.18,151.51Z" style="fill:#263238"></path><g style="clip-path:url(#freepik--clip-path--inject-143)"><path d="M206.25,183.3c14.24-8.17,35.45-9.34,58.3,4.5,13.29-4.44,41.14-7.72,50.18,17.91C255.39,218.69,206.25,183.3,206.25,183.3Z" style="fill:#407BFF;opacity:0.5"></path><path d="M266.8,171.05c-1.3,4.77-6.92,8.32-11.47,7.19a8.8,8.8,0,0,1-7-9.17Z" style="fill:#fff"></path><path d="M243.92,168c-1.9,4-7.85,5.85-11.25,4.55-4.48-1.72-4.73-9.19-4.73-9.19Z" style="fill:#fff"></path><path d="M227.39,161.6c-2.48,2.55-7.26,2.83-9.73.53a6.34,6.34,0,0,1-1.21-8.23Z" style="fill:#fff"></path><path d="M272.29,171.9c.45,4.41,4.95,7.29,9.09,8,3.76.68,7.52-7.09,7.52-7.09Z" style="fill:#fff"></path><path d="M294.3,171.82c.86,3.45,3.62,6.19,5.61,5.56,3.35-1.07,2.63-6.26,2.63-6.26Z" style="fill:#fff"></path><path d="M308.75,170.92a4.91,4.91,0,0,0,5.76,2.85,4.18,4.18,0,0,0,2.86-4.67Z" style="fill:#fff"></path><path d="M257.22,202c1.26-4.78,6.86-8.38,11.42-7.28a8.79,8.79,0,0,1,7.06,9.12Z" style="fill:#fff"></path><path d="M284,204.33c.13-4.43,4.86-8.5,8.49-8.67,4.79-.21,8,6.53,8,6.53Z" style="fill:#fff"></path><path d="M304.45,199.62c-.12-4.78,1.71-9.22,3.73-9,3.4.34,4.11,7.35,4.11,7.35Z" style="fill:#fff"></path><path d="M247.43,198.9c1-5.53-2.42-10.29-6.23-12.33-3.48-1.86-9.78,6.65-9.78,6.65Z" style="fill:#fff"></path><path d="M225.74,187.2c1.29-3.31.63-7.14-1.37-7.77-3.35-1.07-5.76,3.59-5.76,3.59Z" style="fill:#fff"></path><path d="M220.61,183.1a4.91,4.91,0,0,0-.84-6.37,4.18,4.18,0,0,0-5.47.21Z" style="fill:#fff"></path></g><path d="M203.11,127.3c-5.77,7.78-10.8,13.82-13.39,15,5.41,2.61,8.84.5,8.84.5S209.43,118.78,203.11,127.3Z" style="fill:#407BFF"></path><path d="M294.67,90.93C287.46,85,270,86.82,258.35,75.39c-2.07,5.64,2.46,9.73,2.46,9.73S302.87,97.66,294.67,90.93Z" style="fill:#407BFF"></path><path d="M295.1,91c-4.4-5.7-15.46-3.45-22.52-14.55a12.44,12.44,0,0,0,1.3,9.65S300.1,97.46,295.1,91Z" style="fill:#407BFF"></path><path d="M104.91,191.53C96.29,195.06,89.58,211.27,74,216.1c4,4.5,9.73,2.46,9.73,2.46S114.73,187.51,104.91,191.53Z" style="fill:#407BFF"></path><path d="M85.37,270.62c0,20.88,12.1,21.45,11.63,37.77,5.54-2.31,7.1-9,7.1-9S85.38,260,85.37,270.62Z" style="fill:#407BFF"></path><path d="M415.67,230.25c5.52,7.51,10.36,20.58,22.28,37.83-5.17,3.06-10-3.86-10-3.86S409.38,221.7,415.67,230.25Z" style="fill:#407BFF"></path><path d="M105.75,190c-6.77,2.44-17.71,20.09-30.83,21A12.42,12.42,0,0,0,84,214.51S101.17,206.54,105.75,190Z" style="fill:#407BFF"></path><path d="M127.44,240.22c5.87,4.17,4,15.31,15.4,21.94a12.39,12.39,0,0,1-9.69-.92S120.75,235.47,127.44,240.22Z" style="fill:#407BFF"></path><path d="M246.27,92.62c-2.15,6.24-10,12.39-20.28,13.12-5.83-2.43-19.24-24.72-17.28-37.41S235.8,38.86,240.84,38c4.6-.81-15.34,19.68-14.35,30.56S246.27,92.62,246.27,92.62Z" style="fill:#263238"></path><path d="M305.75,105.76c-.24,5.4,3.9,12.45,11.55,16.1,5.18-.1,22.11-13.11,24.42-23.39s-9.8-27.4-15.42-32.83c-3-2.91,5.81,19.63,1.79,27.65S305.75,105.76,305.75,105.76Z" style="fill:#263238"></path><g style="clip-path:url(#freepik--clip-path-2--inject-143)"><path d="M218.49,104.14a1.5,1.5,0,0,1-1-.37,2.06,2.06,0,0,1-.65-1.36,6.67,6.67,0,0,1,2.39-5.71,20.26,20.26,0,0,1,4.44-2.86l.77-.4a57.63,57.63,0,0,0,10.89-7.57c1.42-1.25,2.75-2.64,3-4.37a.9.9,0,0,0-.08-.64,1.11,1.11,0,0,0-.77-.25c-4.91-.36-9.6,2.4-13.13,4.85-.64.44-1.27.89-1.9,1.34-3.34,2.37-6.78,4.82-10.78,5.89a7.42,7.42,0,0,1-4.31.11,3.75,3.75,0,0,1-2.72-3.37c0-1.59,1.16-3,3.45-4.17A60.89,60.89,0,0,1,216.34,82c1.28-.43,2.55-.85,3.81-1.32,3.41-1.26,7.71-3.16,10.66-6.65.6-.71,1.24-1.74.89-2.6a2.22,2.22,0,0,0-1-1c-1.53-.94-3.56-1.17-6.2-.71a22.42,22.42,0,0,0-11,5.27c-.57.5-1.11,1-1.66,1.56a18.59,18.59,0,0,1-4.57,3.6c-2.53,1.25-6.19,1-7.68-1.47-1.25-2.1-.38-4.78,1.13-6.35a12.39,12.39,0,0,1,5.82-3c2.56-.71,5.22-1.17,7.8-1.63a52.33,52.33,0,0,0,12.06-3.12,11.93,11.93,0,0,0,3.6-2.25,1.45,1.45,0,0,0,.5-.79.6.6,0,0,0-.15-.49,3,3,0,0,0-1.94-.62c-5.77-.1-11.62-.11-17.37,0-2.16.05-3.53-.72-3.65-2-.07-.86.45-1.67,1.6-2.46a21.8,21.8,0,0,1,11.38-3.83,43,43,0,0,1,7.62.58,34.81,34.81,0,0,0,8.82.46,3,3,0,0,0,2.18-1c.67-1-.22-2.5-1.39-3.4-2.33-1.78-5.14-2.57-8.11-3.4A40.21,40.21,0,0,1,224,43.58a2.86,2.86,0,0,1-1.63-1.74,1.16,1.16,0,0,1,.29-1.05,2,2,0,0,1,1-.5,20.88,20.88,0,0,1,14.54,2c2.21,1.21,3.43,2.71,3.51,4.34a.4.4,0,0,1-.38.42.39.39,0,0,1-.41-.38c-.09-1.7-1.73-2.94-3.1-3.68a20,20,0,0,0-14-1.88,1.22,1.22,0,0,0-.62.26.35.35,0,0,0-.1.35,2.17,2.17,0,0,0,1.18,1.18,40.09,40.09,0,0,0,5.39,1.8c2.91.81,5.92,1.66,8.37,3.53,1.5,1.14,2.52,3,1.57,4.47a3.68,3.68,0,0,1-2.75,1.3,36.27,36.27,0,0,1-9-.46,41.62,41.62,0,0,0-7.48-.58,21.1,21.1,0,0,0-11,3.69c-.9.62-1.31,1.19-1.26,1.74.12,1.27,2.4,1.29,2.85,1.3,5.76-.09,11.61-.08,17.39,0a3.58,3.58,0,0,1,2.52.89,1.35,1.35,0,0,1,.35,1.1,2.17,2.17,0,0,1-.74,1.27,12.75,12.75,0,0,1-3.84,2.41,52.82,52.82,0,0,1-12.23,3.17c-2.56.45-5.21.92-7.73,1.61a11.75,11.75,0,0,0-5.46,2.77c-1.27,1.32-2.06,3.65-1,5.4,1.26,2.12,4.43,2.25,6.65,1.16A18.31,18.31,0,0,0,211.32,76c.55-.54,1.11-1.08,1.69-1.58A23.14,23.14,0,0,1,224.39,69c2.85-.49,5.06-.23,6.75.81a2.94,2.94,0,0,1,1.29,1.37c.4,1,0,2.15-1,3.41-3.08,3.63-7.49,5.59-11,6.88-1.26.47-2.54.9-3.82,1.33A59.3,59.3,0,0,0,208.4,86c-2,1-3,2.2-3,3.47a3,3,0,0,0,2.18,2.62,6.72,6.72,0,0,0,3.85-.12c3.87-1,7.25-3.45,10.53-5.77.63-.46,1.27-.91,1.91-1.35,3.64-2.52,8.48-5.37,13.64-5a1.76,1.76,0,0,1,1.33.56,1.59,1.59,0,0,1,.25,1.21c-.23,2-1.68,3.52-3.21,4.87a58.07,58.07,0,0,1-11,7.68l-.77.4a19.57,19.57,0,0,0-4.27,2.74,5.88,5.88,0,0,0-2.15,5,1.39,1.39,0,0,0,.38.88c.61.51,1.71-.25,2.15-.59l2.29-1.82a140.51,140.51,0,0,1,15.28-11.05c7.14-4.24,14.07-6.55,20.61-6.85a.4.4,0,0,1,.41.38.39.39,0,0,1-.38.41c-6.4.3-13.21,2.56-20.24,6.74a141.85,141.85,0,0,0-15.19,11l-2.29,1.82A3.68,3.68,0,0,1,218.49,104.14Z" style="fill:#fff;opacity:0.30000000000000004"></path></g><g style="clip-path:url(#freepik--clip-path-3--inject-143)"><path d="M327.57,123.71a3.46,3.46,0,0,1-2.06-1.11l-.24-.21a27.19,27.19,0,0,0-3.14-2.13c-3.72-2.29-8.36-5.14-11.12-13-2-5.74-9.42-8-15.94-9.92L293,96.75a.41.41,0,0,1-.26-.5.4.4,0,0,1,.5-.26l2,.62c6.71,2,14.31,4.3,16.47,10.42,2.67,7.56,7.17,10.33,10.78,12.55a27.28,27.28,0,0,1,3.24,2.2l.25.22c.58.51,1.24,1.08,1.79.88a1.09,1.09,0,0,0,.52-.57c.58-1.25.21-2.94-1-4.52a20.26,20.26,0,0,0-3.52-3.32l-.66-.53a59.86,59.86,0,0,1-9-9.07c-1.19-1.5-2.26-3.13-2.05-4.84a1.3,1.3,0,0,1,.54-1,1.93,1.93,0,0,1,1.4-.11c5.05,1,9.09,4.58,12.06,7.56.52.52,1,1,1.55,1.58,2.66,2.74,5.4,5.57,8.92,7.41a8.11,8.11,0,0,0,3.7,1.11h0c1.11,0,2.37-.53,2.64-1.5.33-1.23-1-2.59-2.17-3.51a66.08,66.08,0,0,0-7.23-4.73c-1.14-.68-2.28-1.36-3.4-2.07-3.1-2-6.94-4.68-9.12-8.42-1-1.68-.66-2.63-.22-3.13a2.78,2.78,0,0,1,1.56-.79A11.08,11.08,0,0,1,329,93.54a25.28,25.28,0,0,1,9.8,7.36c.45.56.86,1.14,1.28,1.72a17.68,17.68,0,0,0,3.48,3.93c1.92,1.48,5.08,2.16,6.71.81,1.34-1.12,1.1-3.11.16-4.5a12.78,12.78,0,0,0-4.68-3.67c-2.29-1.21-4.74-2.28-7.12-3.3a61.56,61.56,0,0,1-11.12-5.74,13.52,13.52,0,0,1-3.17-2.94,1.86,1.86,0,0,1-.45-1.28,1.11,1.11,0,0,1,.49-.8,3.73,3.73,0,0,1,2.73-.14c5.58,1.41,11.23,2.92,16.8,4.49,1.31.37,2.75.44,3-.26.15-.39-.13-.94-.83-1.65a23.94,23.94,0,0,0-9.81-5.83A51.3,51.3,0,0,0,329,80.26a43.48,43.48,0,0,1-8.8-2,4,4,0,0,1-2.36-1.77,1.75,1.75,0,0,1,0-1.45,4.19,4.19,0,0,1,2.51-1.88c2.78-.88,5.86-.78,8.85-.69a45.43,45.43,0,0,0,5.6-.05,2,2,0,0,0,1.37-.62.17.17,0,0,0,0-.2,1.4,1.4,0,0,0-.55-.38,23.56,23.56,0,0,0-13.95-2.12c-1.47.25-3.32.82-3.75,2.13a.4.4,0,0,1-.5.25.4.4,0,0,1-.25-.5c.44-1.34,1.95-2.26,4.37-2.66a24.36,24.36,0,0,1,14.43,2.19,1.94,1.94,0,0,1,.86.66,1,1,0,0,1,.08,1,2.59,2.59,0,0,1-2,1.07,45.16,45.16,0,0,1-5.7.06c-3-.1-5.93-.19-8.58.64a3.36,3.36,0,0,0-2,1.46.93.93,0,0,0,0,.8,3.36,3.36,0,0,0,1.91,1.35,43.33,43.33,0,0,0,8.64,1.92,51.2,51.2,0,0,1,7.47,1.5,24.91,24.91,0,0,1,10.14,6c.35.36,1.42,1.43,1,2.5s-1.89,1.32-4,.74c-5.56-1.57-11.2-3.08-16.77-4.5a3.08,3.08,0,0,0-2.09,0,.3.3,0,0,0-.16.24,1.19,1.19,0,0,0,.31.71,12.57,12.57,0,0,0,3,2.76,60.58,60.58,0,0,0,11,5.66c2.38,1,4.85,2.1,7.17,3.34a13.44,13.44,0,0,1,5,3.92c1.13,1.68,1.39,4.13-.32,5.55-2,1.64-5.54.87-7.7-.79a18.47,18.47,0,0,1-3.63-4.1c-.41-.57-.82-1.14-1.26-1.69a24.47,24.47,0,0,0-9.49-7.12,10.41,10.41,0,0,0-6.18-1,2.05,2.05,0,0,0-1.13.54c-.49.56-.1,1.51.31,2.21,2.09,3.59,5.83,6.24,8.85,8.15,1.12.71,2.25,1.38,3.39,2.06a66.59,66.59,0,0,1,7.31,4.79c2,1.56,2.8,3,2.44,4.34a3.37,3.37,0,0,1-3.4,2.08h0a8.74,8.74,0,0,1-4.07-1.2c-3.63-1.9-6.42-4.78-9.12-7.56-.51-.53-1-1-1.54-1.57-2.89-2.9-6.82-6.36-11.66-7.35a1.4,1.4,0,0,0-.81,0c-.1.06-.16.19-.18.41-.18,1.42.8,2.88,1.88,4.24a59.26,59.26,0,0,0,8.89,9l.65.53a21.06,21.06,0,0,1,3.66,3.45c1.39,1.82,1.78,3.81,1.07,5.34a1.75,1.75,0,0,1-1,1A1.63,1.63,0,0,1,327.57,123.71Z" style="fill:#fff;opacity:0.30000000000000004"></path></g><path d="M236.42,127.22c24.08,9.28,26.42,15.85,28.81,13.27s1.17-13.7-4.43-18.25-8-2.27-6.48-.47c-8.08-5.88-17.21-6.68-20-5.77s.44,2.9.44,2.9S217.92,120.08,236.42,127.22Z" style="fill:#263238"></path><path d="M310.73,141.68c-32.93-.53-38.29,4.71-40.22,1.38s3.78-13.25,12.34-15.36,10.67,1,8.09,2c12.09-2.41,23.55.33,26.66,2.26s-1.64,2.55-1.64,2.55S336,142.08,310.73,141.68Z" style="fill:#263238"></path><path d="M244.81,92.43c-1.32-3-1.21-9.6,2.24-9.61s-.21,9.9-.21,9.9C247.49,93.79,246.26,95.77,244.81,92.43Z" style="fill:#407BFF"></path><path d="M195.08,157.58c-2.91-1.61-6.82-6.87-4.09-9s5.87,8,5.87,8C198,157,198.26,159.34,195.08,157.58Z" style="fill:#407BFF"></path><path d="M245.28,94.48c-3-1.34-7.41-6.23-4.88-8.58S247,93.31,247,93.31C248.17,93.65,248.61,96,245.28,94.48Z" style="fill:#407BFF"></path><path d="M307.54,107.52c4.47-1.22,11.52-7.08,8.51-10.84s-10.6,8.88-10.6,8.88C303.72,105.78,302.63,108.86,307.54,107.52Z" style="fill:#407BFF"></path><path d="M259.54,208c49.49,7.38,66.27,30.67,62.89,49.88-2.71,15.48-9.33,4.25-9.33,4.25s2,36.55-19.17,55c-6.74,5.85-5.46-2.78-5.46-2.78S267.63,350.45,242.2,347c-37.18-5.08-38.72-43.73-38.72-43.73s-7.2,10.89-13.1-4.76c-6.51-17.24-6.65-40,1.7-57C180.35,248.64,177.93,195.79,259.54,208Z" style="fill:#fff;opacity:0.1"></path><path d="M261.38,211.57c-38.27-7.06-56.43,3.91-58.74,17.06-1.86,10.59,5.81,5,5.81,5s-10.61,23,.41,39.89c3.5,5.36,4.71-.49,4.71-.49s6.32,28.23,25.9,32.06c28.64,5.61,36.72-19.63,36.72-19.63s4.73,11.3,12.67,1,13.71-27.8,13.81-37.18C309.54,256.61,324.49,223.22,261.38,211.57Z" style="fill:#fff;opacity:0.1"></path><path d="M193.58,198.62c-.23.47-.43.55-.63.55l-.59,0-1.17-.06h-2.37l-2.37.11c-.8.06-1.59.16-2.39.22a61.61,61.61,0,0,0-9.63,1.9,82.63,82.63,0,0,0-19.26,8.41A114.74,114.74,0,0,0,137,223.12a140.12,140.12,0,0,0-16.11,16.82l4.88-14.05.74,5.67.94,5.49c.4,1.76.76,3.54,1.19,5.25s.86,3.38,1.41,4.91a44.5,44.5,0,0,0,3.46,8.28,16.26,16.26,0,0,0,.93,1.53,4,4,0,0,0,.45.62,5,5,0,0,0,.42.52l.37.37c.11.09.19.08.26.14s0-.05,0-.08a.22.22,0,0,0,0-.15,3,3,0,0,0-.21-.4l-.34-.48-.2-.25-.4-.45-.46-.48A27.75,27.75,0,1,1,95,295.51l-.59-.63-.54-.6-.46-.57c-.29-.35-.64-.79-.87-1.1-.41-.56-.81-1.13-1.2-1.7s-.63-1-.94-1.5-.63-1-.85-1.43c-.47-.89-1-1.81-1.4-2.67L87,282.79c-.38-.84-.66-1.6-1-2.41s-.64-1.61-.88-2.36c-.5-1.52-1.07-3.1-1.44-4.56a98.59,98.59,0,0,1-3.2-16.93c-.35-2.74-.44-5.34-.62-8s-.13-5.17-.17-7.74.11-5.05.21-7.54l.49-7.38.23-3.48a17,17,0,0,1,4.65-10.58A186.36,186.36,0,0,1,106.19,189a161.22,161.22,0,0,1,25.29-19,124.65,124.65,0,0,1,30.32-13.35,108,108,0,0,1,17-3.4c1.46-.14,2.91-.31,4.37-.41s2.93-.18,4.4-.23,3,0,4.43,0l2.21.08,1.11,0a1.26,1.26,0,0,1,1.07.61,33.76,33.76,0,0,1,2.67,34.2Z" style="fill:#407BFF"></path><path d="M336.94,178.18a1,1,0,0,1,1-.31l.9.23,1.81.47,3.62,1c2.4.72,4.8,1.48,7.18,2.32a145.24,145.24,0,0,1,14.11,5.79,128,128,0,0,1,26.28,16.53,120.3,120.3,0,0,1,21.61,23A118.72,118.72,0,0,1,428.2,255l.29.79a21,21,0,0,1,.4,13.29h0a63.42,63.42,0,0,1-2.69,9.16,73.83,73.83,0,0,1-3.67,8.07c-1.34,2.51-2.77,4.87-4.26,7.12s-3,4.35-4.63,6.37a117,117,0,0,1-10,11.12c-1.75,1.71-3.53,3.37-5.39,5-.93.81-1.87,1.61-2.87,2.41l-1.53,1.23-.84.63c-.35.26-.5.39-1.15.82a27.75,27.75,0,0,1-28.75-47.48c-.24.18,0,.06.07.05s.23-.08.37-.15l.85-.38c.6-.27,1.22-.59,1.84-.91,1.24-.65,2.49-1.35,3.7-2.08a73.32,73.32,0,0,0,6.8-4.61c1-.8,2-1.59,2.87-2.37s1.67-1.55,2.39-2.28,1.3-1.42,1.84-2l1.28-1.64h0l.69,14.08a76,76,0,0,0-9.51-17.07,74.59,74.59,0,0,0-13.52-14,85.51,85.51,0,0,0-17-10.51,95.47,95.47,0,0,0-9.56-3.87c-1.64-.58-3.31-1.1-5-1.59l-2.54-.71-1.29-.33-.64-.16c-.22-.06-.41-.2-.51-.71l-1.38-6.76A40,40,0,0,1,336.94,178.18Z" style="fill:#407BFF"></path><path d="M191.83,160.84c-.43,2-3.55,3.13-7,2.41s-5.84-3-5.41-5,3.55-3.13,7-2.41S192.26,158.79,191.83,160.84Z" style="opacity:0.1"></path><path d="M178.17,164.85c0,1.36-1.94,2.38-4.21,2.29s-4.06-1.28-4-2.64,1.94-2.38,4.21-2.29S178.23,163.49,178.17,164.85Z" style="opacity:0.1"></path><path d="M294.18,94.79c-.7.74-2.6.07-4.25-1.49s-2.43-3.42-1.73-4.16,2.6-.06,4.25,1.5S294.87,94.06,294.18,94.79Z" style="opacity:0.1"></path><path d="M267.4,121.57c-1.52.49-3.4-1.18-4.21-3.72s-.22-5,1.3-5.46,3.4,1.18,4.21,3.72S268.92,121.09,267.4,121.57Z" style="opacity:0.1"></path><path d="M259.63,114c-.93.47-2.31-.39-3.1-1.93s-.68-3.17.25-3.64,2.3.39,3.09,1.93S260.55,113.55,259.63,114Z" style="opacity:0.1"></path><path d="M130.4,257.38c-1.19.66-3.05-.41-4.15-2.39s-1-4.13.15-4.79,3,.4,4.15,2.39S131.59,256.72,130.4,257.38Z" style="opacity:0.1"></path><path d="M121.5,250c-.86.14-1.74-.91-2-2.34s.28-2.69,1.14-2.83,1.73.91,2,2.33S122.35,249.85,121.5,250Z" style="opacity:0.1"></path><path d="M108.05,192.05c-.67-.55-.46-1.9.46-3s2.22-1.57,2.88-1,.46,1.91-.46,3S108.72,192.6,108.05,192.05Z" style="opacity:0.1"></path><path d="M98,288.88c-2.25,1.07-5.52-1.1-7.3-4.85s-1.42-7.65.83-8.72,5.51,1.1,7.3,4.85S100.27,287.81,98,288.88Z" style="opacity:0.1"></path><path d="M183.17,220.79c-1.28-1.53-.72-4.12,1.26-5.78s4.62-1.77,5.91-.24.72,4.12-1.25,5.78S184.46,222.32,183.17,220.79Z" style="opacity:0.1"></path><path d="M184,210c-.19-1.19.9-2.36,2.44-2.6s2.94.52,3.13,1.71-.9,2.36-2.44,2.6S184.16,211.22,184,210Z" style="opacity:0.1"></path><path d="M197.3,212.43a1.71,1.71,0,1,1,1.9-.9A1.5,1.5,0,0,1,197.3,212.43Z" style="opacity:0.1"></path><path d="M418.09,278.09c.91,1.17.45,3.75-1,5.76s-3.37,2.7-4.28,1.53-.45-3.74,1-5.75S417.19,276.93,418.09,278.09Z" style="opacity:0.1"></path><path d="M417.31,286.2c.67.62.59,2.33-.18,3.83s-1.95,2.21-2.62,1.59-.6-2.33.18-3.83S416.64,285.58,417.31,286.2Z" style="opacity:0.1"></path><path d="M148.14,351.61c.72,1.29-.1,3.77-1.84,5.55s-3.74,2.18-4.46.89.1-3.77,1.84-5.55S147.41,350.33,148.14,351.61Z" style="opacity:0.1"></path><path d="M146.18,359.52c.58.71.25,2.39-.74,3.76s-2.25,1.9-2.83,1.19-.24-2.39.74-3.76S145.61,358.81,146.18,359.52Z" style="opacity:0.1"></path><path d="M391,316.63c0,1.42-2,3.17-4.48,3.91s-4.56.19-4.59-1.22,2-3.17,4.48-3.91S390.92,315.22,391,316.63Z" style="opacity:0.1"></path><path d="M294.7,383.6c1.37.37,2.49,2.78,2.5,5.39s-1.09,4.44-2.46,4.07-2.49-2.78-2.5-5.39S293.33,383.23,294.7,383.6Z" style="opacity:0.1"></path><path d="M319.27,225.39c1.61-.66,4,.6,5.43,2.82s1.22,4.56-.38,5.22-4.05-.6-5.44-2.82S317.66,226.05,319.27,225.39Z" style="opacity:0.1"></path><path d="M287.77,394.78c2.42-.08,5.69,2.72,7.3,6.26s1,6.47-1.45,6.56-5.69-2.72-7.31-6.26S285.35,394.87,287.77,394.78Z" style="opacity:0.1"></path><path d="M425.31,271c.4.47.17,2-.49,3.38s-1.52,2.12-1.92,1.65-.16-2,.5-3.38S424.92,270.56,425.31,271Z" style="opacity:0.1"></path><path d="M367.33,193.56c1.13-1,4-.5,6.43,1.05s3.46,3.61,2.33,4.58-4,.51-6.43-1.05S366.2,194.54,367.33,193.56Z" style="opacity:0.1"></path><path d="M157,332.27l-47.7,1.67-.8-21.79,45.59-59.07,22.82-.8L179,310.9l11.83-.41.75,20.56-11.82.41.65,17.83-22.82.8Zm-.76-20.56-1.1-30L131,312.59Z" style="fill:#fff"></path><path d="M157.63,350.59a.5.5,0,0,1-.5-.48l-.64-17.33-47.19,1.66a.51.51,0,0,1-.52-.48l-.8-21.79a.48.48,0,0,1,.1-.33l45.6-59.06a.51.51,0,0,1,.37-.2l22.82-.8a.51.51,0,0,1,.52.48l2.13,58.13,11.33-.4a.5.5,0,0,1,.52.48l.75,20.56a.49.49,0,0,1-.13.36.52.52,0,0,1-.35.16l-11.33.4.64,17.32a.47.47,0,0,1-.14.36.46.46,0,0,1-.34.16l-22.82.8ZM157,331.77a.5.5,0,0,1,.49.48l.64,17.33,21.82-.77-.63-17.33a.49.49,0,0,1,.13-.36.54.54,0,0,1,.35-.16l11.32-.39L190.39,311l-11.33.39a.5.5,0,0,1-.52-.48l-2.13-58.12-22.09.78L109,312.31l.78,21.12,47.2-1.66Zm-26-18.68a.5.5,0,0,1-.45-.27.49.49,0,0,1,.05-.53l24.11-30.91a.5.5,0,0,1,.89.29l1.1,30a.49.49,0,0,1-.13.36.57.57,0,0,1-.35.16l-25.2.88Zm23.65-30-22.6,29,23.63-.84Z" style="fill:#407BFF"></path><path d="M198.68,300.51q-1-27.19,8.28-38.4T236,250.2q9.51-.33,15.71,1.83a28.92,28.92,0,0,1,10.19,5.83,33.26,33.26,0,0,1,6.37,7.79,41.94,41.94,0,0,1,3.92,9.67,92.33,92.33,0,0,1,3.45,22.16q1,25.95-7.28,38.3t-29.45,13.09q-11.9.4-19.36-3.17a31.25,31.25,0,0,1-12.43-10.83q-3.61-5.14-5.84-14.23A100.29,100.29,0,0,1,198.68,300.51Zm26-.85q.67,18.22,4.09,24.78t9.47,6.34a9.6,9.6,0,0,0,6.81-3.07q2.82-2.94,4-9.1t.68-19.11q-.69-19-4.12-25.43t-9.78-6.2q-6.5.22-9.14,7T224.65,299.66Z" style="fill:#fff"></path><path d="M236.53,349.41c-6.89,0-12.65-1.09-17.17-3.26a32,32,0,0,1-12.63-11c-2.42-3.46-4.41-8.3-5.9-14.39a100.55,100.55,0,0,1-2.65-20.23c-.67-18.18,2.16-31.21,8.39-38.74S222.73,250.17,236,249.7c6.37-.21,11.72.41,15.89,1.86a29.44,29.44,0,0,1,10.37,5.93,33.78,33.78,0,0,1,6.46,7.91,43.36,43.36,0,0,1,4,9.78,93.91,93.91,0,0,1,3.46,22.28c.64,17.33-1.84,30.32-7.36,38.6S253.2,348.86,239,349.37C238.14,349.39,237.33,349.41,236.53,349.41Zm-37.35-48.92a99.91,99.91,0,0,0,2.62,20c1.47,6,3.4,10.7,5.76,14.06a30.9,30.9,0,0,0,12.24,10.67c4.88,2.34,11.32,3.4,19.12,3.12,13.91-.49,23.68-4.82,29.05-12.87s7.83-20.89,7.21-38a92.83,92.83,0,0,0-3.44-22,41.43,41.43,0,0,0-3.88-9.56,32.23,32.23,0,0,0-6.27-7.67,28.5,28.5,0,0,0-10-5.73c-4-1.41-9.27-2-15.52-1.8-13,.46-22.65,4.4-28.71,11.72S198.52,282.57,199.18,300.49Zm38.66,30.8c-4,0-7.24-2.23-9.54-6.62s-3.7-12.83-4.15-25h0c-.44-12,.24-20.44,2-25s5.06-7.16,9.59-7.32,7.88,2,10.24,6.47,3.72,13,4.18,25.64c.32,8.63.08,15.1-.69,19.22s-2.17,7.34-4.11,9.35a10.1,10.1,0,0,1-7.15,3.23Zm-12.69-31.65c.44,12,1.8,20.28,4,24.56s5.14,6.22,9,6.08a9.15,9.15,0,0,0,6.46-2.92c1.8-1.87,3.1-4.85,3.85-8.84s1-10.45.67-19c-.46-12.53-1.82-21-4.06-25.21s-5.25-6.08-9.32-5.94-7,2.33-8.7,6.68-2.39,12.7-1.95,24.59Z" style="fill:#407BFF"></path><path d="M215.86,254.66l6.5,4.52h0a.25.25,0,0,1,.1.21l-.42,4.33,0-.09,2.51,2-2.64-1.86a.11.11,0,0,1,0-.09h0l.14-4.35.11.22-6.78-4.1Z" style="fill:#407BFF"></path><path d="M220.32,258.13l-1.93,2.7,0,0-.05,0-2.73,1.43,2.51-1.79-.07.07,1.43-3Z" style="fill:#407BFF"></path><path d="M245.12,327.72l3.79,4.55,0,.05a.47.47,0,0,1,.05.1l1.25,5.37-2-5.14.1.15L243.85,329Z" style="fill:#407BFF"></path><polygon points="246.89 330.3 254.42 328.83 247.15 331.27 246.89 330.3" style="fill:#407BFF"></polygon><path d="M332.59,326.1l-47.69,1.67L284.1,306l45.59-59.07,22.82-.8,2.15,58.62,11.83-.41.75,20.56-11.83.41.66,17.83-22.82.8Zm-.75-20.56-1.1-30-24.1,30.9Z" style="fill:#fff"></path><path d="M333.25,344.42a.5.5,0,0,1-.5-.48l-.64-17.33-47.2,1.66a.49.49,0,0,1-.51-.48l-.8-21.8a.47.47,0,0,1,.1-.32l45.59-59.06a.51.51,0,0,1,.38-.2l22.82-.8a.5.5,0,0,1,.52.48l2.13,58.13,11.33-.4a.5.5,0,0,1,.52.48l.75,20.56a.49.49,0,0,1-.13.36.52.52,0,0,1-.35.16l-11.33.4.64,17.32a.52.52,0,0,1-.14.36.46.46,0,0,1-.35.16l-22.82.8Zm-.66-18.82a.5.5,0,0,1,.5.48l.64,17.33,21.82-.77-.64-17.33a.51.51,0,0,1,.49-.52l11.32-.39L366,304.83l-11.33.4a.5.5,0,0,1-.52-.48L352,246.63l-22.09.77L284.6,306.14l.78,21.11,47.2-1.65Zm-25.95-18.68a.5.5,0,0,1-.4-.81l24.1-30.9a.5.5,0,0,1,.55-.17.52.52,0,0,1,.35.46l1.1,30a.53.53,0,0,1-.13.36.6.6,0,0,1-.35.16l-25.21.88Zm23.65-30-22.6,29,23.63-.83Z" style="fill:#407BFF"></path><path d="M330.57,276.84l6-2.92-.18.36-.86-5.74h0a.23.23,0,0,1,.09-.23l7.48-5.22-.05.09-.39-7.34.6,7.33a.13.13,0,0,1,0,.09l-7.31,5.45.1-.24,1.05,5.71a.33.33,0,0,1-.17.34h0L331,277.73Z" style="fill:#407BFF"></path><path d="M108.89,333.62l7.69-9h0a.32.32,0,0,1,.3-.11l5.54.87-.15,0,6.1-3.4,0,.06,2.69-9.47-2.49,9.53a.1.1,0,0,1-.05.06l-6,3.64a.2.2,0,0,1-.15,0h0l-5.56-.68.3-.12-7.43,9.21Z" style="fill:#407BFF"></path><path d="M131.5,312.72l-2.84,9.44,0-.2,3,4.59-.09,0,7.5.1-7.5.11a.12.12,0,0,1-.09-.05l-3.19-4.44a.22.22,0,0,1,0-.19h0l2.35-9.57Z" style="fill:#407BFF"></path><path d="M337.55,267.28l-1.88-2.8.05,0-2.7-.89,2.76.69,0,0h0l2.47,2.27Z" style="fill:#407BFF"></path><path d="M131.29,263.63c2-4.56,15.93-.31,19,5.34-4.77.47-11.51,4.63-16.83,3.28C130.39,271.48,129.92,266.84,131.29,263.63Z" style="fill:#263238"></path><path d="M128.09,270.75c3.56-4.28,17.24,4.78,18.72,11.84-5.28-1-13.87,1.3-19.17-1.87C124.6,278.91,125.58,273.77,128.09,270.75Z" style="fill:#263238"></path><path d="M123.46,281.44c4.93-2.6,14.11,11,12.77,18.1-4.49-3-13.31-4.11-17-9.06C117.11,287.64,120,283.27,123.46,281.44Z" style="fill:#263238"></path><path d="M114,290.6c4.39.31,4.89,13.26,1,17.47-1.78-3.86-7.23-8.3-7.64-13.16C107.12,292.12,110.87,290.38,114,290.6Z" style="fill:#263238"></path><path d="M367,279.46c0-5-14.52-6.54-19.56-2.54,4.2,2.31,8.77,8.77,14.2,9.62C364.78,287,367,283,367,279.46Z" style="fill:#263238"></path><path d="M366,290.94c-2.15-5.14-17.88-.5-21.37,5.82,5.35.57,12.87,5.31,18.87,3.84C366.93,299.76,367.5,294.56,366,290.94Z" style="fill:#263238"></path><path d="M367.24,298.91c-3.51-4.32-17.29,4.59-18.85,11.64,5.3-1,13.86,1.44,19.19-1.67C370.65,307.1,369.72,302,367.24,298.91Z" style="fill:#263238"></path><path d="M373.29,315.24c-2.51-3.61-12.77,1.43-15.52,6.76,4.23-.45,10.83,3.25,15.21,1.12C375.5,321.89,375.07,317.78,373.29,315.24Z" style="fill:#263238"></path></g></svg>



<!--<svg id="Group_5" data-name="Group 5" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="994.188" height="856.374" viewBox="0 0 994.188 856.374">-->
<!--  <defs>-->
<!--    <clipPath id="clip-path">-->
<!--      <rect id="Rectangle_4" data-name="Rectangle 4" width="994.188" height="856.374" fill="none"/>-->
<!--    </clipPath>-->
<!--  </defs>-->
<!--  <g id="Group_2" data-name="Group 2">-->
<!--    <g id="Group_1" data-name="Group 1" clip-path="url(#clip-path)">-->
<!--      <path id="Path_1" data-name="Path 1" d="M74.391,272.608a166.311,166.311,0,0,1-26.6-20.425A2.759,2.759,0,0,0,44,256.2a171.936,171.936,0,0,0,27.485,21.1,2.759,2.759,0,0,0,2.9-4.693" transform="translate(10.037 58.501)" fill="#ac5e9f"/>-->
<!--      <path id="Path_2" data-name="Path 2" d="M142.493,289.7a167.621,167.621,0,0,1-33.251-4.438,2.759,2.759,0,0,0-1.273,5.368,173.34,173.34,0,0,0,34.348,4.584c.083,0,.165,0,.247,0a2.716,2.716,0,0,0,.958-.232,2.758,2.758,0,0,0-1.028-5.279" transform="translate(24.627 66.354)" fill="#ac5e9f"/>-->
<!--      <path id="Path_3" data-name="Path 3" d="M107.781,24.755h0a2.758,2.758,0,0,0,.182-4.952,2.736,2.736,0,0,0-2.1-.211,173.2,173.2,0,0,0-20.128,7.47c-3.927,1.743-7.85,3.66-11.654,5.7l-.217.116a2.759,2.759,0,0,0,2.611,4.86l.213-.115c3.681-1.972,7.479-3.829,11.284-5.517a167.607,167.607,0,0,1,19.486-7.231,2.931,2.931,0,0,0,.322-.12" transform="translate(16.848 4.531)" fill="#ac5e9f"/>-->
<!--      <path id="Path_4" data-name="Path 4" d="M46.052,61.561a2.759,2.759,0,0,0-4.016-3.784,171.923,171.923,0,0,0-21.09,27.5,2.759,2.759,0,1,0,4.694,2.9A166.324,166.324,0,0,1,46.052,61.561" transform="translate(4.778 13.242)" fill="#ac5e9f"/>-->
<!--      <path id="Path_5" data-name="Path 5" d="M5.516,156.281a167.683,167.683,0,0,1,4.42-33.253,2.759,2.759,0,0,0-2.049-3.321,2.764,2.764,0,0,0-3.32,2.049A173.271,173.271,0,0,0,0,156.107a2.758,2.758,0,0,0,2.669,2.844c.084,0,.166,0,.249,0a2.758,2.758,0,0,0,2.6-2.668" transform="translate(0 27.835)" fill="#ac5e9f"/>-->
<!--      <path id="Path_6" data-name="Path 6" d="M23.9,223.633c-1.972-3.682-3.829-7.479-5.517-11.285a167.6,167.6,0,0,1-7.254-19.56,2.758,2.758,0,1,0-5.282,1.59,173.041,173.041,0,0,0,7.493,20.207c1.743,3.929,3.66,7.85,5.7,11.656l.076.143a2.772,2.772,0,0,0,2.595,1.447,2.7,2.7,0,0,0,.953-.229c.064-.028.127-.06.189-.092a2.762,2.762,0,0,0,1.124-3.736Z" transform="translate(1.334 44.399)" fill="#ac5e9f"/>-->
<!--      <path id="Path_7" data-name="Path 7" d="M178.445,23.713a2.759,2.759,0,0,0,.476-5.44,173.184,173.184,0,0,0-34.353-4.551,2.759,2.759,0,1,0-.17,5.515,167.632,167.632,0,0,1,33.255,4.406,2.787,2.787,0,0,0,.793.07" transform="translate(32.975 3.192)" fill="#ac5e9f"/>-->
<!--      <path id="Path_8" data-name="Path 8" d="M209.924,272.909c-3.686,1.975-7.484,3.831-11.285,5.517A167.33,167.33,0,0,1,179,285.706a2.773,2.773,0,0,0-1.842,3.434,2.756,2.756,0,0,0,3.437,1.848,173.082,173.082,0,0,0,20.286-7.518c3.927-1.742,7.842-3.656,11.632-5.686l.1-.051a2.71,2.71,0,0,0,1.088-3.715,2.8,2.8,0,0,0-3.77-1.108" transform="translate(41.192 63.421)" fill="#ac5e9f"/>-->
<!--      <path id="Path_9" data-name="Path 9" d="M278.821,155.647a2.761,2.761,0,0,0-2.846,2.668,167.78,167.78,0,0,1-4.454,33.249,2.763,2.763,0,0,0,2.046,3.322,2.8,2.8,0,0,0,.8.071,2.772,2.772,0,0,0,.96-.234,2.737,2.737,0,0,0,1.565-1.884,173.151,173.151,0,0,0,4.6-34.345,2.762,2.762,0,0,0-2.667-2.847" transform="translate(63.157 36.214)" fill="#ac5e9f"/>-->
<!--      <path id="Path_10" data-name="Path 10" d="M273.367,123.061a2.768,2.768,0,0,0,2.427.092h0a2.762,2.762,0,0,0,1.521-3.313,172.649,172.649,0,0,0-7.541-20.366c-1.741-3.926-3.657-7.846-5.695-11.651l-.158-.293a2.758,2.758,0,0,0-4.86,2.61l.153.287c1.975,3.687,3.831,7.484,5.517,11.285a167.634,167.634,0,0,1,7.3,19.713,2.74,2.74,0,0,0,1.334,1.637" transform="translate(60.2 20.028)" fill="#ac5e9f"/>-->
<!--      <path id="Path_11" data-name="Path 11" d="M237.864,59.728a2.759,2.759,0,1,0,3.782-4.017A171.848,171.848,0,0,0,214.14,34.635a2.758,2.758,0,1,0-2.9,4.694,166.422,166.422,0,0,1,26.621,20.4" transform="translate(48.845 7.962)" fill="#ac5e9f"/>-->
<!--      <path id="Path_12" data-name="Path 12" d="M262.65,224.237a2.764,2.764,0,0,0-3.8.894,166.283,166.283,0,0,1-20.438,26.591,2.759,2.759,0,0,0,4.012,3.788,171.877,171.877,0,0,0,21.117-27.475,2.763,2.763,0,0,0-.894-3.8" transform="translate(55.297 52.077)" fill="#ac5e9f"/>-->
<!--      <path id="Path_13" data-name="Path 13" d="M717.329,567.736a13.1,13.1,0,1,1,13.1-13.1,13.118,13.118,0,0,1-13.1,13.1m0-20.833a7.729,7.729,0,1,0,7.73,7.73,7.739,7.739,0,0,0-7.73-7.73" transform="translate(163.852 125.997)" fill="#e03c75"/>-->
<!--      <path id="Path_14" data-name="Path 14" d="M111.563,599.963h-8.048V588.615a2.283,2.283,0,1,0-4.566,0v11.348H90.9a2.283,2.283,0,0,0,0,4.567h8.048v11.348a2.283,2.283,0,0,0,4.566,0V604.53h8.048a2.283,2.283,0,0,0,0-4.567" transform="translate(20.619 136.422)" fill="#e03c75"/>-->
<!--      <path id="Path_15" data-name="Path 15" d="M707.282,82.1H695.2V65.064a3.427,3.427,0,1,0-6.854,0V82.1H676.27a3.427,3.427,0,1,0,0,6.855h12.079v17.032a3.427,3.427,0,1,0,6.854,0V88.951h12.079a3.427,3.427,0,0,0,0-6.855" transform="translate(156.55 14.341)" fill="#e03c75"/>-->
<!--      <path id="Path_16" data-name="Path 16" d="M442.537,241.38a299.75,299.75,0,0,0-26.24,17.579,2.564,2.564,0,0,0,1.637,4.614q.128-.006.255-.022a2.576,2.576,0,0,0,1.183-.488,294.524,294.524,0,0,1,25.789-17.277,2.564,2.564,0,1,0-2.624-4.406" transform="translate(96.621 56.078)" fill="#ac5e9f"/>-->
<!--      <path id="Path_17" data-name="Path 17" d="M435.2,654.206a297.806,297.806,0,0,1-24.926-18.5A2.565,2.565,0,0,0,407,639.663a303.281,303.281,0,0,0,25.358,18.817,2.559,2.559,0,0,0,1.514.425q.128-.006.255-.022a2.565,2.565,0,0,0,1.066-4.677" transform="translate(94.481 147.774)" fill="#ac5e9f"/>-->
<!--      <path id="Path_18" data-name="Path 18" d="M506.476,215.655a2.541,2.541,0,0,0-1.941-.245c-3.423.932-6.887,1.939-10.3,3-6.633,2.057-13.263,4.365-19.71,6.857a2.565,2.565,0,0,0,1.018,4.955c.086,0,.173-.011.259-.023h0a2.564,2.564,0,0,0,.573-.149c6.338-2.452,12.858-4.72,19.378-6.741,3.347-1.037,6.753-2.028,10.124-2.945a2.565,2.565,0,0,0,.594-4.7" transform="translate(110.028 50.098)" fill="#ac5e9f"/>-->
<!--      <path id="Path_19" data-name="Path 19" d="M494.659,681.175a298.129,298.129,0,0,1-28.987-11.133,2.564,2.564,0,0,0-2.086,4.685A302.96,302.96,0,0,0,493.07,686.05a2.5,2.5,0,0,0,.89.126,2.244,2.244,0,0,0,.256-.022,2.58,2.58,0,0,0,2.087-1.748,2.565,2.565,0,0,0-1.644-3.231" transform="translate(107.509 155.847)" fill="#ac5e9f"/>-->
<!--      <path id="Path_20" data-name="Path 20" d="M391.279,282.17a2.521,2.521,0,0,0-1.792.8A302.357,302.357,0,0,0,369,306.991a2.565,2.565,0,0,0,2.129,4.122c.086,0,.173-.011.26-.022a2.553,2.553,0,0,0,1.683-.981A296.982,296.982,0,0,1,393.21,286.5a2.565,2.565,0,0,0-1.93-4.328" transform="translate(85.732 65.652)" fill="#ac5e9f"/>-->
<!--      <path id="Path_21" data-name="Path 21" d="M352.8,335.983a2.564,2.564,0,0,0-3.465,1.067A303.446,303.446,0,0,0,336.046,365.7a2.563,2.563,0,0,0,2.478,3.518,2.181,2.181,0,0,0,.253-.022,2.569,2.569,0,0,0,2.028-1.584,298.427,298.427,0,0,1,13.064-28.165,2.565,2.565,0,0,0-1.069-3.466" transform="translate(78.145 78.104)" fill="#ac5e9f"/>-->
<!--      <path id="Path_22" data-name="Path 22" d="M326.239,488.313c-1.04-7.492-1.8-15.115-2.258-22.658a2.566,2.566,0,0,0-2.7-2.4,2.5,2.5,0,0,0-1.765.835,2.6,2.6,0,0,0-.656,1.881c.466,7.672,1.239,15.427,2.3,23.052.385,2.77.815,5.572,1.276,8.328a2.551,2.551,0,0,0,2.619,2.139c.086,0,.174-.011.263-.023h0c.026,0,.052-.007.068-.011a2.568,2.568,0,0,0,2.105-2.952c-.455-2.712-.878-5.466-1.255-8.186" transform="translate(74.189 107.784)" fill="#ac5e9f"/>-->
<!--      <path id="Path_23" data-name="Path 23" d="M329.6,398.737a2.565,2.565,0,0,0-4.663.841,301.124,301.124,0,0,0-5.082,31.183,2.563,2.563,0,0,0,2.272,2.826,2.63,2.63,0,0,0,.381.014,2.114,2.114,0,0,0,.25-.022,2.561,2.561,0,0,0,2.2-2.262,295.762,295.762,0,0,1,4.995-30.648,2.545,2.545,0,0,0-.348-1.932" transform="translate(74.417 92.5)" fill="#ac5e9f"/>-->
<!--      <path id="Path_24" data-name="Path 24" d="M559.187,691.124a295.106,295.106,0,0,1-30.92-2.893,2.564,2.564,0,0,0-.746,5.074,300.052,300.052,0,0,0,31.458,2.944q.1,0,.2,0,.128-.006.255-.022a2.564,2.564,0,0,0-.25-5.1" transform="translate(122.229 160.124)" fill="#ac5e9f"/>-->
<!--      <path id="Path_25" data-name="Path 25" d="M366.181,587.724a2.564,2.564,0,1,0-4.216,2.92A299.51,299.51,0,0,0,381.3,615.62a2.553,2.553,0,0,0,2.038.889c.085,0,.171-.011.258-.023a2.565,2.565,0,0,0,1.588-4.215,294.381,294.381,0,0,1-19-24.547" transform="translate(84.112 136.489)" fill="#ac5e9f"/>-->
<!--      <path id="Path_26" data-name="Path 26" d="M336.753,529.58a2.564,2.564,0,1,0-4.848,1.673,304.009,304.009,0,0,0,11.842,29.273,2.55,2.55,0,0,0,2.416,1.479c.086,0,.173-.011.26-.023a2.565,2.565,0,0,0,1.971-3.625,298.708,298.708,0,0,1-11.641-28.777" transform="translate(77.191 122.815)" fill="#ac5e9f"/>-->
<!--      <path id="Path_27" data-name="Path 27" d="M735.552,621.727a295.385,295.385,0,0,1-23.18,20.621,2.564,2.564,0,0,0,1.7,4.565q.126-.005.251-.022a2.579,2.579,0,0,0,1.247-.536,300.7,300.7,0,0,0,23.585-20.981,2.564,2.564,0,0,0-3.607-3.646" transform="translate(165.524 144.486)" fill="#ac5e9f"/>-->
<!--      <path id="Path_28" data-name="Path 28" d="M783.882,345.685a300.246,300.246,0,0,0-15.744-27.378,2.564,2.564,0,1,0-4.3,2.787A295.205,295.205,0,0,1,779.308,348a2.549,2.549,0,0,0,2.639,1.381h0a2.517,2.517,0,0,0,.806-.253,2.568,2.568,0,0,0,1.128-3.447" transform="translate(177.625 73.789)" fill="#ac5e9f"/>-->
<!--      <path id="Path_29" data-name="Path 29" d="M800.013,507.693a2.568,2.568,0,0,0-3.111,1.861,297.482,297.482,0,0,1-9.148,29.673,2.564,2.564,0,0,0,2.509,3.445c.084,0,.168-.011.25-.022h0a2.566,2.566,0,0,0,2.056-1.658,302.71,302.71,0,0,0,9.3-30.187,2.565,2.565,0,0,0-1.861-3.112" transform="translate(183.251 118.107)" fill="#ac5e9f"/>-->
<!--      <path id="Path_30" data-name="Path 30" d="M804.911,442.124H804.9a2.575,2.575,0,0,0-2.473,2.635,294.191,294.191,0,0,1-.772,31.046,2.562,2.562,0,0,0,2.358,2.756,2.681,2.681,0,0,0,.3.006c.085,0,.17-.011.254-.022a2.578,2.578,0,0,0,2.2-2.341,299.462,299.462,0,0,0,.786-31.59,2.569,2.569,0,0,0-2.64-2.491" transform="translate(186.519 102.869)" fill="#ac5e9f"/>-->
<!--      <path id="Path_31" data-name="Path 31" d="M570.859,206.821a299.7,299.7,0,0,0-31.558,1.423,2.521,2.521,0,0,0-1.733.907,2.569,2.569,0,0,0,2.07,4.208c.051,0,.1-.006.154-.011a294.819,294.819,0,0,1,31.028-1.4,2.525,2.525,0,0,0,.373-.023,2.565,2.565,0,0,0-.334-5.1" transform="translate(124.939 48.119)" fill="#ac5e9f"/>-->
<!--      <path id="Path_32" data-name="Path 32" d="M802.111,412.678h0c.051-.007.1-.016.148-.025a2.568,2.568,0,0,0,2.012-3.016,305.669,305.669,0,0,0-7.782-30.616l-.133-.427a2.564,2.564,0,1,0-4.9,1.524l.132.425a300.415,300.415,0,0,1,7.65,30.1,2.567,2.567,0,0,0,2.611,2.061,2.251,2.251,0,0,0,.255-.022" transform="translate(184.123 87.669)" fill="#ac5e9f"/>-->
<!--      <path id="Path_33" data-name="Path 33" d="M777.508,569.068a2.569,2.569,0,0,0-3.5.957,298.09,298.09,0,0,1-16.772,26.121,2.565,2.565,0,0,0,2.177,4.059c.085,0,.171-.011.258-.023a2.569,2.569,0,0,0,1.729-1.043,303.626,303.626,0,0,0,17.061-26.572,2.565,2.565,0,0,0-.957-3.5" transform="translate(176.075 132.327)" fill="#ac5e9f"/>-->
<!--      <path id="Path_34" data-name="Path 34" d="M724.647,267.931a2.564,2.564,0,1,0-3.387,3.851,295.748,295.748,0,0,1,22.187,21.693,2.55,2.55,0,0,0,2.241.8A2.564,2.564,0,0,0,747.22,290a301.522,301.522,0,0,0-22.573-22.07" transform="translate(167.614 62.191)" fill="#ac5e9f"/>-->
<!--      <path id="Path_35" data-name="Path 35" d="M623.99,684.255a297.412,297.412,0,0,1-30.564,5.606,2.564,2.564,0,0,0,.45,5.1,2.144,2.144,0,0,0,.232-.02q1.355-.176,2.712-.364c9.474-1.315,19.015-3.111,28.359-5.336a2.564,2.564,0,0,0-1.188-4.989" transform="translate(137.558 159.19)" fill="#ac5e9f"/>-->
<!--      <path id="Path_36" data-name="Path 36" d="M636.591,217.8a301.411,301.411,0,0,0-30.761-7.191,2.564,2.564,0,1,0-.9,5.048,296.437,296.437,0,0,1,30.236,7.069,2.587,2.587,0,0,0,.811.1q.128-.006.255-.022a2.565,2.565,0,0,0,.362-5" transform="translate(140.257 48.994)" fill="#ac5e9f"/>-->
<!--      <path id="Path_37" data-name="Path 37" d="M696.851,246.339a303.57,303.57,0,0,0-27.687-15.19,2.564,2.564,0,1,0-2.229,4.618A298.469,298.469,0,0,1,694.152,250.7a2.58,2.58,0,0,0,1.448.382,2.507,2.507,0,0,0,.253-.023,2.564,2.564,0,0,0,1-4.72" transform="translate(154.838 53.722)" fill="#ac5e9f"/>-->
<!--      <path id="Path_38" data-name="Path 38" d="M684.291,660.76a295.885,295.885,0,0,1-27.921,13.563,2.563,2.563,0,0,0,1.35,4.9h0a2.57,2.57,0,0,0,.645-.177,301.8,301.8,0,0,0,28.407-13.8,2.565,2.565,0,0,0-2.481-4.489" transform="translate(152.353 153.665)" fill="#ac5e9f"/>-->
<!--      <rect id="Rectangle_1" data-name="Rectangle 1" width="34.803" height="350.263" transform="translate(477.397 401.247)" fill="#e03c75"/>-->
<!--      <rect id="Rectangle_2" data-name="Rectangle 2" width="34.941" height="12.11" transform="translate(477.259 401.378)" fill="#551948"/>-->
<!--      <path id="Path_39" data-name="Path 39" d="M642.752,118.013V284.908L524.74,402.92H357.845L239.833,284.908V118.013L357.845,0H524.74Z" transform="translate(55.802 0)" fill="#e03c75"/>-->
<!--      <path id="Path_40" data-name="Path 40" d="M635.381,107.823V274.718L517.369,392.73H350.474l-78.961-78.961C568.275,357.5,571.353,43.794,571.353,43.794Z" transform="translate(63.173 10.19)" fill="#551948"/>-->
<!--      <path id="Path_41" data-name="Path 41" d="M620.477,163.434S563.743,374.092,335.57,364.893H502.465L620.477,246.882Z" transform="translate(78.077 38.026)" fill="#551948"/>-->
<!--      <path id="Path_42" data-name="Path 42" d="M619.475,122.427V273.1L512.933,379.643H362.26L255.718,273.1V122.427L362.26,15.885H512.933Z" transform="translate(59.498 3.696)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="12"/>-->
<!--      <rect id="Rectangle_3" data-name="Rectangle 3" width="13.974" height="350.263" transform="translate(498.227 401.247)" fill="#551948"/>-->
<!--    </g>-->
<!--  </g>-->
<!--  <text id="OPPS" transform="translate(365.836 248.036)" fill="#fff" font-size="112" font-family="PoetsenOne-Regular, Poetsen One"><tspan x="0" y="0">OPPS</tspan></text>-->
<!--  <g id="Group_4" data-name="Group 4">-->
<!--    <g id="Group_3" data-name="Group 3" clip-path="url(#clip-path)">-->
<!--      <path id="Path_43" data-name="Path 43" d="M710.93,621.715H191.237a5.849,5.849,0,0,1-5.849-5.849v-1.234a5.849,5.849,0,0,1,5.849-5.849H710.93a5.848,5.848,0,0,1,5.849,5.849v1.234a5.849,5.849,0,0,1-5.849,5.849" transform="translate(43.134 141.646)" fill="#3f1239"/>-->
<!--      <path id="Path_44" data-name="Path 44" d="M215.5,621.715H131.7a6.466,6.466,0,1,1,0-12.932h83.8a6.466,6.466,0,1,1,0,12.932" transform="translate(29.138 141.646)" fill="#3f1239"/>-->
<!--      <path id="Path_45" data-name="Path 45" d="M117.543,621.715H28.256a5.849,5.849,0,0,1-5.85-5.849v-1.234a5.849,5.849,0,0,1,5.85-5.849h89.287a5.848,5.848,0,0,1,5.849,5.849v1.234a5.849,5.849,0,0,1-5.849,5.849" transform="translate(5.213 141.646)" fill="#3f1239"/>-->
<!--      <path id="Path_46" data-name="Path 46" d="M717.542,621.715H644.059a5.85,5.85,0,0,1-5.849-5.849v-1.234a5.849,5.849,0,0,1,5.849-5.849h73.483a5.848,5.848,0,0,1,5.849,5.849v1.234a5.849,5.849,0,0,1-5.849,5.849" transform="translate(148.492 141.646)" fill="#3f1239"/>-->
<!--      <path id="Path_47" data-name="Path 47" d="M790.465,621.715H735.557a6.466,6.466,0,1,1,0-12.932h54.908a6.466,6.466,0,1,1,0,12.932" transform="translate(169.638 141.646)" fill="#3f1239"/>-->
<!--      <path id="Path_48" data-name="Path 48" d="M380.337,641.043H197.247l3.331-12.06,6.506-23.55,3.94-14.26,5.808-21.023,11.2-40.526,5.808-21.023,40.831-147.8h28.247l14.965,54.166L343.746,508.6l5.808,21.023,11.2,40.526,5.808,21.023,10.446,37.81Z" transform="translate(45.894 83.948)" fill="#e03c75"/>-->
<!--      <path id="Path_49" data-name="Path 49" d="M380.337,631.516H197.247l3.331-12.06,6.506-23.55c127.585-7.537,110.8-190.464,110.8-190.464l59.126,214.014Z" transform="translate(45.893 94.334)" fill="#7b2167"/>-->
<!--      <rect id="Rectangle_5" data-name="Rectangle 5" width="258.013" height="24.531" transform="translate(205.678 725.85)" fill="#e03c75"/>-->
<!--      <path id="Path_50" data-name="Path 50" d="M380.337,591.121H197.247l3.331-12.06H377.006Z" transform="translate(45.894 134.73)" fill="#3f1239"/>-->
<!--      <path id="Path_51" data-name="Path 51" d="M339.078,498.455s-3.836,80.917-96.8,99.426l124.254-.067Z" transform="translate(56.37 115.976)" fill="#551948"/>-->
<!--      <path id="Path_52" data-name="Path 52" d="M343.745,502.423H222.218l5.808-21.023h109.91Z" transform="translate(51.704 112.007)" fill="#fff"/>-->
<!--      <path id="Path_53" data-name="Path 53" d="M363.96,552.356H208.423l5.808-21.023h143.92Z" transform="translate(48.494 123.625)" fill="#fff"/>-->
<!--      <path id="Path_54" data-name="Path 54" d="M358.119,588.844s-34.2,25.819-160.872,24.531H417.8V588.844Z" transform="translate(45.893 137.006)" fill="#551948"/>-->
<!--    </g>-->
<!--  </g>-->
<!--</svg>-->
