import 'dart:developer';
import 'package:flutter/foundation.dart';

/// فئة لإدارة الإعدادات الآمنة للتطبيق
/// تحتوي على المفاتيح الحساسة مثل API Keys
class SecureConfig {
  // منع إنشاء كائنات من هذه الفئة
  SecureConfig._();

  /// معرف تطبيق Agora
  /// يجب نقل هذه القيمة إلى متغيرات البيئة في الإنتاج
  static const String _agoraAppId = String.fromEnvironment(
    'AGORA_APP_ID',
    defaultValue: '', // لا نضع قيمة افتراضية لأسباب أمنية
  );

  /// الحصول على معرف تطبيق Agora بشكل آمن
  static String get agoraAppId {
    // إذا كان متغير البيئة محدد، استخدمه
    if (_agoraAppId.isNotEmpty) {
      return _agoraAppId;
    }

    // في وضع التطوير، نستخدم القيمة المؤقتة مع تحذير
    if (kDebugMode) {
      log("⚠️ تحذير: يتم استخدام Agora App ID مؤقت في وضع التطوير");
      log("📝 يجب تعيين AGORA_APP_ID في متغيرات البيئة للإنتاج");
    }

    // استخدام القيمة الحقيقية في جميع الأوضاع
    // تأكد من أن هذا هو App ID الصحيح من لوحة تحكم Agora
    return 'b8c3ddbb4d2c45b29962d9592e6e4acf';
  }

  /// التحقق من صحة الإعدادات
  static bool validateConfig() {
    try {
      final appId = agoraAppId;

      // التحقق من طول App ID (يجب أن يكون 32 حرف)
      if (appId.isEmpty || appId.length != 32) {
        log("❌ Agora App ID غير صحيح: الطول يجب أن يكون 32 حرف");
        return false;
      }

      // التحقق من أن App ID يحتوي على أحرف وأرقام فقط
      final validPattern = RegExp(r'^[a-f0-9]{32}$');
      if (!validPattern.hasMatch(appId)) {
        log("❌ Agora App ID غير صحيح: يجب أن يحتوي على أحرف وأرقام فقط");
        return false;
      }

      log("✅ Agora App ID صحيح: ${appId.substring(0, 8)}...");
      return true;
    } catch (e) {
      log("❌ خطأ في التحقق من الإعدادات: $e");
      return false;
    }
  }

  /// طباعة حالة الإعدادات (للتطوير فقط)
  static void printConfigStatus() {
    if (kDebugMode) {
      log("🔧 حالة الإعدادات الآمنة:");

      final currentAppId = agoraAppId;
      final isFromEnv = _agoraAppId.isNotEmpty;

      log("   - Agora App ID: ${currentAppId.substring(0, 8)}...${currentAppId.substring(24)}");
      log("   - المصدر: ${isFromEnv ? '✅ متغير البيئة' : '⚠️ قيمة افتراضية'}");
      log("   - الطول: ${currentAppId.length} حرف");
      log("   - صحيح: ${validateConfig() ? '✅' : '❌'}");

      if (!isFromEnv) {
        log("💡 لتعيين متغيرات البيئة:");
        log("   flutter run --dart-define=AGORA_APP_ID=$currentAppId");
        log("   أو أضف إلى ملف .env:");
        log("   AGORA_APP_ID=$currentAppId");
      }
    }
  }

  /// إعدادات نظام تصفية الضوضاء
  static const double defaultNoiseThreshold = 0.8;
  static const int maxNoiseWarnings = 3;
  static const Duration noiseDetectionCooldown = Duration(seconds: 2);
  static const Duration noiseDataCleanupInterval = Duration(minutes: 5);
  static const Duration noiseDataExpiration = Duration(minutes: 10);

  /// إعدادات الأداء
  static const int audioVolumeIndicationInterval = 200; // milliseconds
  static const int audioVolumeSmoothing = 3;
  static const bool enableVoiceActivityDetection = true;

  /// إعدادات إعادة الاتصال
  static const int maxInitRetries = 3;
  static const Duration reconnectDelay = Duration(seconds: 3);
  static const Duration speakerEnableDelay = Duration(milliseconds: 1000);

  /// إعدادات جودة الصوت
  static const Map<String, dynamic> audioQualitySettings = {
    'noise_suppression': true,
    'auto_gain_control': true,
    'echo_cancellation': true,
  };

  /// إعدادات خاصة بنظام iOS
  static const Map<String, dynamic> iosSpecificSettings = {
    'use_apple_recording': true,
    'force_ios_speaker': true,
  };

  /// RevenueCat API Keys
  static const String _revenueCatAndroidKey = String.fromEnvironment(
    'REVENUECAT_API_KEY_ANDROID',
    defaultValue: 'goog_OwZumvfKAuWwZwPchvOdXsLrmfs',
  );

  static const String _revenueCatIOSKey = String.fromEnvironment(
    'REVENUECAT_API_KEY_IOS',
    defaultValue: 'appl_TidwdMbpnBOBXHXVgbFaOCmmIAr',
  );

  static const String _revenueCatEntitlementId = String.fromEnvironment(
    'REVENUECAT_ENTITLEMENT_ID',
    defaultValue: 'premium_access',
  );

  /// الحصول على مفاتيح RevenueCat
  static String get revenueCatAndroidKey => _revenueCatAndroidKey;
  static String get revenueCatIOSKey => _revenueCatIOSKey;
  static String get revenueCatEntitlementId => _revenueCatEntitlementId;
}
