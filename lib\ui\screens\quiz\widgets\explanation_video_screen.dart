import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:google_fonts/google_fonts.dart';

class ExplanationVideoScreen extends StatefulWidget {
  final Question question;

  const ExplanationVideoScreen({
    super.key,
    required this.question,
  });

  @override
  State<ExplanationVideoScreen> createState() => _ExplanationVideoScreenState();
}

class _ExplanationVideoScreenState extends State<ExplanationVideoScreen> {
  late YoutubePlayerController _controller;
  bool _isFullScreen = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    if (!widget.question.hasVideo) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    try {
      _controller = YoutubePlayerController(
        initialVideoId: widget.question.videoId!,
        flags: YoutubePlayerFlags(
          autoPlay: true,
          mute: false,
          disableDragSeek: false,
          loop: false,
          isLive: false,
          forceHD: false,
          enableCaption: false,
          showLiveFullscreenButton: true,
          startAt: widget.question.videoStartTime ?? 0,
        ),
      );

      _controller.addListener(_onVideoStateChange);

      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onVideoStateChange() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onVideoStateChange);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.question.hasVideo) {
      return Scaffold(
        appBar: AppBar(
          title: Text('خطأ في الفيديو'),
        ),
        body: const Center(
          child: Text('لا يوجد فيديو متاح'),
        ),
      );
    }

    if (_isLoading) {
      return Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            'جاري تحميل الفيديو...',
            style: GoogleFonts.ibmPlexSansArabic(
              color: Colors.white,
            ),
          ),
        ),
        body: const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      );
    }

    return YoutubePlayerBuilder(
      onEnterFullScreen: () => setState(() => _isFullScreen = true),
      onExitFullScreen: () => setState(() => _isFullScreen = false),
      player: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Theme.of(context).primaryColor,
        progressColors: ProgressBarColors(
          playedColor: Theme.of(context).primaryColor,
          handleColor: Theme.of(context).primaryColor,
          bufferedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
          backgroundColor: Colors.grey.shade300,
        ),
        bottomActions: [
          const CurrentPosition(),
          const SizedBox(width: 10),
          Expanded(
            child: ProgressBar(
              colors: ProgressBarColors(
                playedColor: Theme.of(context).primaryColor,
                handleColor: Colors.white,
                bufferedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                backgroundColor: Colors.grey.shade300,
              ),
            ),
          ),
          const SizedBox(width: 10),
          const RemainingDuration(),
          const SizedBox(width: 10),
          PlaybackSpeedButton(
            icon: Icon(
              Icons.speed,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 10),
          const FullScreenButton(),
        ],
      ),
      builder: (context, player) => Scaffold(
        backgroundColor: Colors.black,
        appBar: _isFullScreen ? null : _buildAppBar(),
        body: Column(
          children: [
            // Video Player
            Container(
              decoration: const BoxDecoration(
                color: Colors.black,
              ),
              child: player,
            ),

            // Content below video (only when not in fullscreen)
            if (!_isFullScreen)
              Expanded(
                child: Container(
                  color: Colors.grey[50],
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Video Info Card
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 10,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Icon(
                                        Icons.play_circle_outline,
                                        color: Theme.of(context).primaryColor,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        'شرح بالفيديو',
                                        style: GoogleFonts.ibmPlexSansArabic(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'شاهد الشرح التفصيلي لهذا السؤال',
                                  style: GoogleFonts.ibmPlexSansArabic(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                    height: 1.5,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 20),

                          // Text explanation if available
                          if (widget.question.note != null && widget.question.note!.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(15),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 10,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.orange.withValues(alpha: 0.1),
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                        child: const Icon(
                                          Icons.text_snippet_outlined,
                                          color: Colors.orange,
                                          size: 24,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        'الشرح النصي',
                                        style: GoogleFonts.ibmPlexSansArabic(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.orange,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    widget.question.note!,
                                    style: GoogleFonts.ibmPlexSansArabic(
                                      fontSize: 16,
                                      height: 1.6,
                                      color: Colors.grey[800],
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.black,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        'شرح السؤال',
        style: GoogleFonts.ibmPlexSansArabic(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
