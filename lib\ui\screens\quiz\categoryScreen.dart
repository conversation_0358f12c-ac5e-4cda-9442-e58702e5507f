import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/ads/interstitial_ad_cubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/models/category.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/bannerAdContainer.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/constants/app_constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';

class CategoryScreen extends StatefulWidget {
  // final String categoryName;

  const CategoryScreen({
    required this.quizType,
    this.fromNav = false,
    super.key,
  });

  final QuizTypes quizType;
  final bool fromNav;

  @override
  State<CategoryScreen> createState() => _CategoryScreen();

  static Route<dynamic> route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments! as Map;
    return CupertinoPageRoute(
      builder: (_) => CategoryScreen(
        quizType: arguments['quizType'] as QuizTypes,
        // categoryName: arguments['categoryName'],
      ),
    );
  }
}

class _CategoryScreen extends State<CategoryScreen> {
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      context.read<InterstitialAdCubit>().showAd(context);
    });

    context.read<QuizCategoryCubit>().getQuizCategoryWithUserId(
          languageId: UiUtils.getCurrentQuizLanguageId(context),
          type: UiUtils.getCategoryTypeNumberFromQuizType(widget.quizType),
        );
  }

  String getCategoryTitle(QuizTypes quizType) => context.tr(
        switch (quizType) {
          // تم إزالة MathMania لأن اختبارات الرياضيات لم تعد مدعومة
          // QuizTypes.mathMania => 'mathMania',
          // تم إزالة QuizTypes.audioQuestions لأن الأسئلة الصوتية لم تعد مدعومة
          // تم إزالة QuizTypes.guessTheWord لأن اختبارات تخمين الكلمة لم تعد مدعومة
          QuizTypes.funAndLearn => 'funAndLearn',
          _ => 'quizZone',
        },
      )!;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(25),
          ),
        ),
        centerTitle: true,
        title: Text(
          AppConstants.coursesAndTrainingTitle,
          style: GoogleFonts.ibmPlexSansArabic(
            textStyle: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontSize: 22,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
                Theme.of(context).primaryColor.withOpacity(0.6),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
        ),
      ),
      body: Stack(
        children: [
          showCategory(),
          const Align(
            alignment: Alignment.bottomCenter,
            child: BannerAdContainer(),
          ),
        ],
      ),
    );
  }

  Future<void> _handleOnTapCategory(
      BuildContext context, Category category) async {
    /// Unlock the Premium Category

    // if (category.isPremium) {
    //   // التحقق من حالة الاشتراك
    //   final isSubscribed =
    //       await SubscriptionManager.instance.checkSubscriptionStatus();

    //   if (!isSubscribed) {
    //     // عرض نافذة المحتوى المميز
    //     if (!context.mounted) return;
    //     showDialog(
    //       context: context,
    //       builder: (context) => const PremiumContentDialog(
    //         title: "محتوى تعليمي مميز",
    //         message: "هذا المحتوى متاح فقط للمشتركين في الباقة المميزة",
    //       ),
    //     );
    //     return;
    //   }
    // }

    /// noOf is number of subcategories
    if (category.noOf == '0') {
      if (widget.quizType == QuizTypes.quizZone) {
        /// if category doesn't have any subCategory, check for levels.
        if (category.maxLevel == '0') {
          //direct move to quiz screen pass level as 0
          Navigator.of(context).pushNamed(
            Routes.quiz,
            arguments: {
              'numberOfPlayer': 1,
              'quizType': QuizTypes.quizZone,
              'categoryId': category.id,
              'subcategoryId': '',
              'level': '0',
              'subcategoryMaxLevel': '0',
              'unlockedLevel': 0,
              'contestId': '',
              'comprehensionId': '',
              'quizName': 'Quiz Zone',
              'showRetryButton': category.noOfQues! != '0',
              'isPremiumCategory': category.isPremium,
            },
          );
        } else {
          //navigate to level screen
          Navigator.of(context)
              .pushNamed(Routes.levels, arguments: {'Category': category});
        }
      } else if (widget.quizType == QuizTypes.audioQuestions) {
        Navigator.of(context).pushNamed(
          Routes.quiz,
          arguments: {
            'numberOfPlayer': 1,
            'quizType': QuizTypes.audioQuestions,
            'categoryId': category.id,
            'isPlayed': category.isPlayed,
            'isPremiumCategory': category.isPremium,
          },
        );
      } else if (widget.quizType == QuizTypes.funAndLearn) {
        Navigator.of(context).pushNamed(
          Routes.funAndLearnTitle,
          arguments: {
            'type': 'category',
            'typeId': category.id,
            'title': category.categoryName,
            'isPremiumCategory': category.isPremium,
          },
        );
      }
    } else {
      if (widget.quizType == QuizTypes.quizZone) {
        Navigator.of(context).pushNamed(
          Routes.subcategoryAndLevel,
          arguments: {
            'category_id': category.id,
            'category_name': category.categoryName,
            'isPremiumCategory': category.isPremium,
          },
        );
      } else {
        Navigator.of(context).pushNamed(
          Routes.subCategory,
          arguments: {
            'categoryId': category.id,
            'quizType': widget.quizType,
            'category_name': category.categoryName,
            'isPremiumCategory': category.isPremium,
          },
        );
      }
    }
  }

  Widget showCategory() {
    return BlocConsumer<QuizCategoryCubit, QuizCategoryState>(
      bloc: context.read<QuizCategoryCubit>(),
      listener: (context, state) {
        if (state is QuizCategoryFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      builder: (context, state) {
        if (state is QuizCategoryProgress || state is QuizCategoryInitial) {
          return const Center(child: CircularProgressContainer());
        }
        if (state is QuizCategoryFailure) {
          return ErrorContainer(
            showBackButton: false,
            errorMessageColor: Theme.of(context).primaryColor,
            showErrorImage: true,
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
            onTapRetry: () {
              context.read<QuizCategoryCubit>().getQuizCategoryWithUserId(
                    languageId: UiUtils.getCurrentQuizLanguageId(context),
                    type: UiUtils.getCategoryTypeNumberFromQuizType(
                        widget.quizType),
                  );
            },
          );
        }
        final categoryList = (state as QuizCategorySuccess).categories;
        final size = MediaQuery.of(context).size;
        final crossAxisCount = size.width > 600 ? 3 : 2;
        final aspectRatio = size.width > 600 ? 0.8 : 0.75;

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).scaffoldBackgroundColor,
                Theme.of(context).primaryColor.withOpacity(0.05),
              ],
            ),
          ),
          child: GridView.builder(
            padding: EdgeInsets.symmetric(
              horizontal: size.width * 0.04,
              vertical: 20,
            ),
            controller: scrollController,
            itemCount: categoryList.length,
            physics: const BouncingScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: size.width * 0.04,
              mainAxisSpacing: 20,
              childAspectRatio: aspectRatio,
            ),
            itemBuilder: (context, index) {
              return _buildEnhancedCategoryCard(
                  context, categoryList[index], index);
            },
          ),
        );
      },
    );
  }

  Widget _buildEnhancedCategoryCard(
      BuildContext context, Category category, int index) {
    final size = MediaQuery.of(context).size;
    final primaryColor = Theme.of(context).primaryColor;

    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withOpacity(0.15),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color:
                        Theme.of(context).colorScheme.shadow.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(25),
                child: InkWell(
                  onTap: () => _handleOnTapCategory(context, category),
                  borderRadius: BorderRadius.circular(25),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(25),
                    child: Stack(
                      children: [
                        // صورة الخلفية
                        Positioned.fill(
                          child: Image.network(
                            category.image ?? '',
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      primaryColor.withOpacity(0.8),
                                      primaryColor.withOpacity(0.6),
                                    ],
                                  ),
                                ),
                                child: Icon(
                                  Icons.category_rounded,
                                  size: size.width * 0.15,
                                  color:
                                      Theme.of(context).colorScheme.onPrimary,
                                ),
                              );
                            },
                          ),
                        ),

                        // تدرج لوني محسن
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  primaryColor.withOpacity(0.1),
                                  Theme.of(context)
                                      .colorScheme
                                      .shadow
                                      .withOpacity(0.7),
                                ],
                                stops: const [0.0, 0.6, 1.0],
                              ),
                            ),
                          ),
                        ),

                        // نمط هندسي متحرك
                        Positioned.fill(
                          child: CustomPaint(
                            painter: _CategoryCardPainter(
                              primaryColor: primaryColor,
                              animationValue: value,
                            ),
                          ),
                        ),

                        // المحتوى
                        Positioned(
                          bottom: 15,
                          left: 15,
                          right: 15,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // اسم الفئة مع خلفية
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .shadow
                                      .withOpacity(0.6),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onPrimary
                                        .withOpacity(0.2),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  category.categoryName ?? '',
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.right,
                                  textDirection: TextDirection.rtl,
                                  style: GoogleFonts.ibmPlexSansArabic(
                                    fontSize: size.width * 0.035,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(context).colorScheme.onPrimary,
                                    shadows: [
                                      Shadow(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .shadow
                                            .withOpacity(0.5),
                                        offset: const Offset(0, 1),
                                        blurRadius: 3,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(height: 10),

                              // معلومات الأسئلة
                              Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: size.width * 0.025,
                                        vertical: size.height * 0.008,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            primaryColor.withOpacity(0.9),
                                            primaryColor.withOpacity(0.7),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                primaryColor.withOpacity(0.3),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            widget.quizType ==
                                                    QuizTypes.funAndLearn
                                                ? Icons.menu_book_rounded
                                                : Icons.quiz_rounded,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onPrimary,
                                            size: 14,
                                          ),
                                          const SizedBox(width: 4),
                                          Flexible(
                                            child: Text(
                                              category.noOf == '0'
                                                  ? '${category.noOfQues} سؤال'
                                                  : '${category.noOf} قسم',
                                              style: TextStyle(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onPrimary,
                                                fontSize: size.width * 0.028,
                                                fontWeight: FontWeight.w600,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),

                                  // زر البدء
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onPrimary
                                          .withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onPrimary
                                            .withOpacity(0.4),
                                        width: 1,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.play_arrow_rounded,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onPrimary,
                                      size: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// تحسين تصميم نتائج البحث
// رسام النمط الهندسي للبطاقات
class _CategoryCardPainter extends CustomPainter {
  final Color primaryColor;
  final double animationValue;

  _CategoryCardPainter({
    required this.primaryColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1 * animationValue)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // رسم خطوط متقاطعة متحركة
    final spacing = 30.0 + (animationValue * 5);

    // خطوط قطرية
    for (double i = -size.width; i < size.width * 2; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }

    // دوائر متحركة
    paint.style = PaintingStyle.fill;
    paint.color = primaryColor.withOpacity(0.1 * animationValue);

    final centerX = size.width * 0.8;
    final centerY = size.height * 0.3;
    final radius = 12 + (animationValue * 8);

    canvas.drawCircle(
      Offset(centerX, centerY),
      radius,
      paint,
    );

    // مثلثات صغيرة
    paint.color = Colors.white.withOpacity(0.15 * animationValue);
    final trianglePath = Path();
    final triangleSize = 6 + (animationValue * 3);

    trianglePath.moveTo(size.width * 0.2, size.height * 0.7);
    trianglePath.lineTo(size.width * 0.2 + triangleSize, size.height * 0.7);
    trianglePath.lineTo(
        size.width * 0.2 + triangleSize / 2, size.height * 0.7 - triangleSize);
    trianglePath.close();

    canvas.drawPath(trianglePath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// ignore: unused_element
class _CategorySearchDelegate extends SearchDelegate<String> {
  final List<Category> categories;

  _CategorySearchDelegate(this.categories);

  ThemeData appBarTheme(BuildContext context) {
    return Theme.of(context).copyWith(
      appBarTheme: AppBarTheme(
        backgroundColor: Theme.of(context).primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: GoogleFonts.ibmPlexSansArabic(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        hintStyle: GoogleFonts.ibmPlexSansArabic(
          color: Colors.white70,
          fontSize: 18,
        ),
      ),
    );
  }

  @override
  String get searchFieldLabel => 'ابحث عن فئة...';

  @override
  TextStyle? get searchFieldStyle => const TextStyle(
        color: Colors.white,
        fontSize: 18,
      );

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    final results = categories
        .where((category) =>
            category.categoryName
                ?.toLowerCase()
                .contains(query.toLowerCase()) ??
            false)
        .toList();

    if (query.isEmpty) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search_rounded,
              size: 80,
              color: Colors.grey.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'ابحث عن الفئة التي تريدها',
              style: TextStyle(
                color: Colors.grey.withOpacity(0.5),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    if (results.isEmpty) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search_off_rounded,
              size: 80,
              color: Colors.grey.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'لم يتم العثور على نتائج',
              style: TextStyle(
                color: Colors.grey.withOpacity(0.5),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: results.length,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 20,
        mainAxisSpacing: 20,
        childAspectRatio: 0.85,
      ),
      itemBuilder: (context, index) {
        final category = results[index];
        return _buildSearchResultCard(context, category);
      },
    );
  }

  Widget _buildSearchResultCard(BuildContext context, Category category) {
    return InkWell(
      onTap: () {
        close(context, category.id ?? '');
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                  child: Image.network(
                    category.image ?? '',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        child: Icon(
                          Icons.category_rounded,
                          size: 40,
                          color: Theme.of(context).primaryColor,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(12),
              child: Text(
                category.categoryName ?? '',
                maxLines: 2,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
