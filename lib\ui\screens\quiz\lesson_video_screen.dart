import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:flutterquiz/features/quiz/models/lesson_model.dart';
import 'package:shimmer/shimmer.dart';

class LessonVideoScreen extends StatefulWidget {
  final LessonModel lesson;

  const LessonVideoScreen({
    required this.lesson,
    super.key,
  });

  @override
  State<LessonVideoScreen> createState() => _LessonVideoScreenState();
}

class _LessonVideoScreenState extends State<LessonVideoScreen> {
  late YoutubePlayerController _controller;
  bool _isFullScreen = false;
  String _duration = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = YoutubePlayerController(
      initialVideoId: widget.lesson.videoId,
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
        enableCaption: false, // تعطيل الترجمة التلقائية
        showLiveFullscreenButton: true,
      ),
    );

    _controller.addListener(_onVideoStateChange);

    // إضافة تأخير لمحاكاة التحميل
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _onVideoStateChange() {
    if (_controller.metadata.duration.inSeconds > 0) {
      final duration = _controller.metadata.duration;
      setState(() {
        _duration =
            '${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
      });
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onVideoStateChange);
    _controller.dispose();
    super.dispose();
  }


  Widget _buildShimmerLoading() {
    final size = MediaQuery.of(context).size;
    return Column(
      children: [
        // محاكاة مشغل الفيديو
        Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            height: size.width * 9 / 16, // نسبة أبعاد الفيديو
            color: Colors.white,
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(size.width * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // محاكاة العنوان
                  Container(
                    height: 20,
                    width: size.width * 0.3,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  SizedBox(height: size.height * 0.03),
                  // محاكاة الوصف
                  ...List.generate(
                      3,
                      (index) => Padding(
                            padding: const EdgeInsets.only(bottom: 10),
                            child: Container(
                              height: 15,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          )),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: _buildAppBar(),
        body: _buildShimmerLoading(),
      );
    }

    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final titleSize = size.width * (isTablet ? 0.03 : 0.045);
    final bodySize = titleSize * 0.8;

    return YoutubePlayerBuilder(
      onEnterFullScreen: () => setState(() => _isFullScreen = true),
      onExitFullScreen: () => setState(() => _isFullScreen = false),
      player: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Theme.of(context).primaryColor,
        progressColors: ProgressBarColors(
          playedColor: Theme.of(context).primaryColor,
          handleColor: Theme.of(context).primaryColor,
          bufferedColor: Theme.of(context).primaryColor.withOpacity(0.2),
          backgroundColor: Colors.grey.shade300,
        ),
        bottomActions: [
          const CurrentPosition(),
          const SizedBox(width: 10),
          Expanded(
            child: ProgressBar(
              colors: ProgressBarColors(
                playedColor: Theme.of(context).primaryColor,
                handleColor: Colors.white,
                bufferedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                backgroundColor: Colors.grey.shade300,
              ),
            ),
          ),
          const SizedBox(width: 10),
          const RemainingDuration(),
          const SizedBox(width: 10),
          PlaybackSpeedButton(
            icon: Icon(
              Icons.speed,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 10),
          const FullScreenButton(),
        ],
      ),
      builder: (context, player) => Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: _isFullScreen ? null : _buildAppBar(),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: Theme.of(context).brightness == Brightness.dark
                  ? [
                      Theme.of(context).colorScheme.surface,
                      Theme.of(context).colorScheme.surface.withOpacity(0.8),
                      Theme.of(context).colorScheme.surface,
                    ]
                  : [
                      Colors.grey[50]!,
                      Colors.grey[100]!,
                      Colors.grey[50]!,
                    ],
            ),
          ),
          child: Column(
            children: [
              // Video Player with enhanced shadow
              Container(
                decoration: BoxDecoration(
                  color: Colors.black,
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).shadowColor.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                      spreadRadius: -5,
                    ),
                  ],
                ),
                child: player,
              ),

              if (!_isFullScreen)
                Expanded(
                  child: CustomScrollView(
                    physics: const BouncingScrollPhysics(),
                    slivers: [
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.all(size.width * 0.04),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Enhanced Video Info Card
                              AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.surface,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Theme.of(context).shadowColor.withOpacity(0.05),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                      spreadRadius: -5,
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Enhanced Title Section
                                    Container(
                                      padding: EdgeInsets.all(size.width * 0.04),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Theme.of(context).primaryColor,
                                            Theme.of(context).primaryColor.withOpacity(0.8),
                                            Theme.of(context).primaryColor.withOpacity(0.9),
                                          ],
                                        ),
                                        borderRadius: const BorderRadius.vertical(
                                          top: Radius.circular(20),
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            widget.lesson.title,
                                            style: GoogleFonts.ibmPlexSansArabic(
                                              fontSize: titleSize,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                          if (_duration.isNotEmpty) ...[
                                            SizedBox(height: size.height * 0.01),
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.timer_outlined,
                                                  size: bodySize,
                                                  color: Colors.white70,
                                                ),
                                                SizedBox(
                                                    width: size.width * 0.01),
                                                Text(
                                                  _duration,
                                                  style: GoogleFonts
                                                      .ibmPlexSansArabic(
                                                    fontSize: bodySize,
                                                    color: Colors.white70,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),

                                    // Enhanced Description Section
                                    if (widget.lesson.description.isNotEmpty)
                                      Container(
                                        padding: EdgeInsets.all(size.width * 0.04),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Container(
                                                  padding:
                                                      const EdgeInsets.all(8),
                                                  decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .primaryColor
                                                        .withOpacity(0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(10),
                                                  ),
                                                  child: Icon(
                                                    Icons.description_outlined,
                                                    color: Theme.of(context)
                                                        .primaryColor,
                                                    size: titleSize,
                                                  ),
                                                ),
                                                SizedBox(
                                                    width: size.width * 0.03),
                                                Text(
                                                  'الوصف',
                                                  style: GoogleFonts
                                                      .ibmPlexSansArabic(
                                                    fontSize: titleSize,
                                                    fontWeight: FontWeight.bold,
                                                    color: Theme.of(context).colorScheme.onSurface,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: size.height * 0.02),
                                            Text(
                                              widget.lesson.description,
                                              style:
                                                  GoogleFonts.ibmPlexSansArabic(
                                                fontSize: bodySize,
                                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                                height: 1.8,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
        floatingActionButton: _isFullScreen
            ? null
            : Hero(
                tag: 'playButton',
                child: _buildFloatingActionButton(bodySize, titleSize, size),
              ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(25),
        ),
      ),
      centerTitle: true,
      elevation: 0,
      title: Text(
        widget.lesson.title,
        style: GoogleFonts.ibmPlexSansArabic(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withOpacity(0.8),
              Theme.of(context).primaryColor.withOpacity(0.6),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(
      double bodySize, double titleSize, Size size) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
            spreadRadius: -2,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (_controller.value.isPlaying) {
              _controller.pause();
            } else {
              _controller.play();
            }
            setState(() {});
          },
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: size.width * 0.04,
              vertical: size.height * 0.015,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: titleSize * 1.2,
                ),
                SizedBox(width: size.width * 0.02),
                Text(
                  _controller.value.isPlaying ? 'إيقاف مؤقت' : 'تشغيل',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: bodySize,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
