import 'dart:io';
import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/battleRoom/models/battleRoom.dart';
import 'package:flutterquiz/features/exam/models/exam.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateUserDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/cubits/comprehensionCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/contestCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/setCategoryPlayedCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/setContestLeaderboardCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/unlockedLevelCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/updateLevelCubit.dart';
import 'package:flutterquiz/features/quiz/models/comprehension.dart';
// import 'package:flutterquiz/features/quiz/models/guessTheWordQuestion.dart'; // تم إزالته لأن اختبارات تخمين الكلمة لم تعد مدعومة
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/statistic/cubits/updateStatisticCubit.dart';
import 'package:flutterquiz/features/statistic/statisticRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/radialResultContainer.dart';
import 'package:flutterquiz/utils/rating_service.dart';
import 'package:flutterquiz/ui/screens/subscription/subscription_manager.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/ui/widgets/premium_content_dialog.dart';
import 'package:flutterquiz/ui/widgets/pre_result_ad_dialog.dart';
import 'package:flutterquiz/ui/screens/subscription/paywall.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/constants/app_constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

// رسام دائرة التقدم المحسن
class CircleProgressPainter extends CustomPainter {
  final double progress;
  final Color progressColor;
  final double strokeWidth;
  final bool showGradient;

  CircleProgressPainter({
    required this.progress,
    required this.progressColor,
    required this.strokeWidth,
    this.showGradient = true,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;
    final rect = Rect.fromCircle(center: center, radius: radius);

    // رسم الدائرة الخلفية
    final backgroundPaint = Paint()
      ..color = progressColor.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius, backgroundPaint);

    // رسم دائرة التقدم
    const startAngle = -pi / 2;
    final sweepAngle = 2 * pi * progress;

    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    if (showGradient) {
      paint.shader = LinearGradient(
        colors: [
          progressColor,
          progressColor.withOpacity(0.6),
          progressColor.withOpacity(0.8),
        ],
        stops: const [0.0, 0.5, 1.0],
      ).createShader(rect);
    } else {
      paint.color = progressColor;
    }

    canvas.drawArc(rect, startAngle, sweepAngle, false, paint);

    // إضافة نقطة في نهاية القوس
    if (progress > 0) {
      final endAngle = startAngle + sweepAngle;
      final endPoint = Offset(
        center.dx + radius * cos(endAngle),
        center.dy + radius * sin(endAngle),
      );

      final dotPaint = Paint()
        ..color = progressColor
        ..style = PaintingStyle.fill;

      canvas.drawCircle(endPoint, strokeWidth / 3, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class ResultScreen extends StatefulWidget {
  const ResultScreen({
    required this.isPlayed,
    required this.comprehension,
    required this.playWithBot,
    required this.isPremiumCategory,
    super.key,
    this.exam,
    this.correctExamAnswers,
    this.incorrectExamAnswers,
    this.obtainedMarks,
    this.examCompletedInMinutes,
    this.timeTakenToCompleteQuiz,
    this.hasUsedAnyLifeline,
    this.numberOfPlayer,
    this.myPoints,
    this.battleRoom,
    this.questions,
    this.unlockedLevel,
    this.quizType,
    this.subcategoryMaxLevel,
    this.contestId,
    // تم إزالة guessTheWordQuestions لأن اختبارات تخمين الكلمة لم تعد مدعومة
    this.entryFee,
    this.categoryId,
    this.subcategoryId,
  });

  final QuizTypes?
      quizType; //to show different kind of result data for different quiz type
  final int?
      numberOfPlayer; //to show different kind of result data for number of player
  final int?
      myPoints; // will be in use when quiz is not type of battle and live battle
  final List<Question>? questions; //to see review answers
  final BattleRoom? battleRoom; //will be in use for battle
  final bool
      playWithBot; // used for random battle with robot, users doesn't get any coins or score for playing with bot.
  final String? contestId;
  final Comprehension comprehension; //
  // تم إزالة guessTheWordQuestions لأن اختبارات تخمين الكلمة لم تعد مدعومة
  final int? entryFee;

  //if quizType is quizZone then it will be in use
  //to determine to show next level button
  //it will be in use if quizType is quizZone
  final String? subcategoryMaxLevel;

  //to determine if we need to update level or not
  //it will be in use if quizType is quizZone
  final int? unlockedLevel;

  //Time taken to complete the quiz in seconds
  final double? timeTakenToCompleteQuiz;

  //has used any lifeline - it will be in use to check badge earned or not for
  //quizZone quiz type
  final bool? hasUsedAnyLifeline;

  //Exam module details
  final Exam? exam; //to get the details related exam
  final int? obtainedMarks;
  final int? examCompletedInMinutes;
  final int? correctExamAnswers;
  final int? incorrectExamAnswers;
  final String? categoryId;
  final String? subcategoryId;

  //This will be in use if quizType is audio questions
  // and guess the word
  final bool isPlayed; //

  final bool isPremiumCategory;

  static Route<dynamic> route(RouteSettings routeSettings) {
    final args = routeSettings.arguments! as Map;
    //keys of map are numberOfPlayer,quizType,questions (required)
    //if quizType is not battle and liveBattle need to pass following args
    //myPoints
    //if quizType is quizZone then need to pass following arguments
    //subcategoryMaxLevel, unlockedLevel
    //if quizType is battle and liveBattle then need to pass following arguments
    //battleRoom
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          //to update unlocked level for given subcategory
          BlocProvider<UpdateLevelCubit>(
            create: (_) => UpdateLevelCubit(QuizRepository()),
          ),
          //to update user score and coins
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
          //to update statistic
          BlocProvider<UpdateStatisticCubit>(
            create: (_) => UpdateStatisticCubit(StatisticRepository()),
          ),
          //set ContestLeaderBoard
          BlocProvider<SetContestLeaderboardCubit>(
            create: (_) => SetContestLeaderboardCubit(QuizRepository()),
          ),
          //set quiz category played
          BlocProvider<SetCategoryPlayed>(
            create: (_) => SetCategoryPlayed(QuizRepository()),
          ),
          BlocProvider<UpdateUserDetailCubit>(
            create: (_) => UpdateUserDetailCubit(ProfileManagementRepository()),
          ),
        ],
        child: ResultScreen(
          battleRoom: args['battleRoom'] as BattleRoom?,
          categoryId: args['categoryId'] as String? ?? '',
          comprehension:
              args['comprehension'] as Comprehension? ?? Comprehension.empty(),
          contestId: args['contestId'] as String?,
          correctExamAnswers: args['correctExamAnswers'] as int?,
          entryFee: args['entryFee'] as int?,
          exam: args['exam'] as Exam?,
          examCompletedInMinutes: args['examCompletedInMinutes'] as int?,
          // تم إزالة guessTheWordQuestions لأن اختبارات تخمين الكلمة لم تعد مدعومة
          hasUsedAnyLifeline: args['hasUsedAnyLifeline'] as bool?,
          incorrectExamAnswers: args['incorrectExamAnswers'] as int?,
          isPlayed: args['isPlayed'] as bool? ?? true,
          myPoints: args['myPoints'] as int?,
          numberOfPlayer: args['numberOfPlayer'] as int?,
          obtainedMarks: args['obtainedMarks'] as int?,
          playWithBot: args['play_with_bot'] as bool? ?? false,
          questions: args['questions'] as List<Question>?,
          quizType: args['quizType'] as QuizTypes?,
          subcategoryId: args['subcategoryId'] as String? ?? '',
          subcategoryMaxLevel: args['subcategoryMaxLevel'] as String?,
          timeTakenToCompleteQuiz: args['timeTakenToCompleteQuiz'] as double?,
          unlockedLevel: args['unlockedLevel'] as int?,
          isPremiumCategory: args['isPremiumCategory'] as bool? ?? false,
        ),
      ),
    );
  }

  @override
  State<ResultScreen> createState() => _ResultScreenState();
}

class _ResultScreenState extends State<ResultScreen> {
  final ScreenshotController screenshotController = ScreenshotController();
  List<Map<String, dynamic>> usersWithRank = [];
  late final String userName;
  bool _isWinner = false; // Inicialización por defecto
  int _earnedCoins = 0;
  String? _winnerId;

  bool _displayedAlreadyLoggedInDialog = false;

  late final didSkipQue = widget.quizType == QuizTypes.quizZone &&
      widget.questions!.map((e) => e.submittedAnswerId).contains('0');

  // إنشاء مثيل من خدمة التقييم
  final RatingService _ratingService = RatingService();

  @override
  void initState() {
    super.initState();

    // ✅ عرض PreResultAdDialog بعد تحميل الصفحة بثانية واحدة
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && !widget.isPremiumCategory) {
        _showPreResultAdDialog();
      }
    });

    if (widget.quizType == QuizTypes.oneVsOneBattle) {
      battleConfiguration();
      userName = '';
    } else {
      //decide winner
      if (winPercentage() >=
          context.read<SystemConfigCubit>().quizWinningPercentage) {
        _isWinner = true;
      } else {
        _isWinner = false;
      }
      //earn coins based on percentage
      earnCoinsBasedOnWinPercentage();
      setContestLeaderboard();
      userName = context.read<UserDetailsCubit>().getUserName();

      // عرض نافذة التقييم إذا حقق المستخدم نتيجة جيدة
      _checkAndShowRatingPrompt();
    }

    //check for badges
    //update score,coins and statistic related details

    Future.delayed(Duration.zero, () {
      //earnBadge will check the condition for unlocking badges and
      //will return true or false
      //we need to return bool value so we can pass this to
      //updateScoreAndCoinsCubit since dashing_debut badge will unlock
      //from set_user_coin_score api
      _earnBadges();
      _updateScoreAndCoinsDetails();
      _updateStatistics();
      fetchUpdateUserDetails();
    });
  }

  Future<void> fetchUpdateUserDetails() async {
    if (widget.quizType == QuizTypes.quizZone ||
        widget.quizType == QuizTypes.funAndLearn) {
      await context.read<UserDetailsCubit>().fetchUserDetails();
    }
  }

  /// ✅ عرض نافذة الإعلان قبل النتائج
  void _showPreResultAdDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PreResultAdDialog(
        onContinue: () {
          // لا نحتاج لفعل شيء، المستخدم يرى النتائج بالفعل
          Navigator.of(context).pop();
        },
        onSubscribe: () async {
          // الانتقال لصفحة الاشتراك
          Navigator.of(context).pop();
          try {
            final offerings = await Purchases.getOfferings();
            if (!mounted) return;
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => MultiBlocProvider(
                  providers: [
                    BlocProvider<UpdateUserDetailCubit>(
                      create: (_) =>
                          UpdateUserDetailCubit(ProfileManagementRepository()),
                    ),
                    BlocProvider<UserDetailsCubit>(
                      create: (_) =>
                          UserDetailsCubit(ProfileManagementRepository()),
                    ),
                  ],
                  child: Paywall(
                    offering: offerings.current,
                  ),
                ),
              ),
            );
          } catch (e) {
            if (!mounted) return;
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => MultiBlocProvider(
                  providers: [
                    BlocProvider<UpdateUserDetailCubit>(
                      create: (_) =>
                          UpdateUserDetailCubit(ProfileManagementRepository()),
                    ),
                    BlocProvider<UserDetailsCubit>(
                      create: (_) =>
                          UserDetailsCubit(ProfileManagementRepository()),
                    ),
                  ],
                  child: const Paywall(
                    offering: null,
                  ),
                ),
              ),
            );
          }
        },
      ),
    );
  }

  void _updateStatistics() {
    if (widget.quizType != QuizTypes.selfChallenge &&
        widget.quizType != QuizTypes.exam) {
      context.read<UpdateStatisticCubit>().updateStatistic(
            answeredQuestion: attemptedQuestion(),
            categoryId: getCategoryIdOfQuestion(),
            correctAnswers: correctAnswer(),
            winPercentage: winPercentage(),
          );
    }
  }

  //update stats related to battle, score of user and coins given to winner
  Future<void> battleConfiguration() async {
    var winnerId = '';

    if (widget.battleRoom!.user1!.points == widget.battleRoom!.user2!.points) {
      _isWinner = true;
      _winnerId = winnerId;

      /// No Coins & Score when playing with Robot.
      if (!widget.playWithBot) {
        _updateCoinsAndScoreAndStatisticForBattle(widget.battleRoom!.entryFee!);
      }
    } else {
      if (widget.battleRoom!.user1!.points > widget.battleRoom!.user2!.points) {
        winnerId = widget.battleRoom!.user1!.uid;
      } else {
        winnerId = widget.battleRoom!.user2!.uid;
      }
      await Future<void>.delayed(Duration.zero);
      _isWinner = context.read<UserDetailsCubit>().userId() == winnerId;
      _winnerId = winnerId;

      if (!widget.playWithBot) {
        _updateCoinsAndScoreAndStatisticForBattle(
          widget.battleRoom!.entryFee! * 2,
        );
      }
      //update winner id and _isWinner in ui
      setState(() {});
    }
  }

  void _updateCoinsAndScoreAndStatisticForBattle(int earnedCoins) {
    Future.delayed(
      Duration.zero,
      () {
        //
        final currentUserId = context.read<UserDetailsCubit>().userId();
        final currentUser = widget.battleRoom!.user1!.uid == currentUserId
            ? widget.battleRoom!.user1!
            : widget.battleRoom!.user2!;
        if (_isWinner) {
          //update score and coins for user
          context.read<UpdateScoreAndCoinsCubit>().updateCoinsAndScore(
                currentUser.points,
                earnedCoins,
                wonBattleKey,
              );
          //update score locally and database
          context.read<UserDetailsCubit>().updateCoins(
                addCoin: true,
                coins: earnedCoins,
              );
          context.read<UserDetailsCubit>().updateScore(currentUser.points);

          //update battle stats

          context.read<UpdateStatisticCubit>().updateBattleStatistic(
                userId1: currentUserId == widget.battleRoom!.user1!.uid
                    ? widget.battleRoom!.user1!.uid
                    : widget.battleRoom!.user2!.uid,
                userId2: widget.battleRoom!.user1!.uid != currentUserId
                    ? widget.battleRoom!.user1!.uid
                    : widget.battleRoom!.user2!.uid,
                winnerId: _winnerId!,
              );
        } else {
          //if user is not winner then update only score
          context.read<UpdateScoreAndCoinsCubit>().updateScore(
                currentUser.points,
              );
          context.read<UserDetailsCubit>().updateScore(currentUser.points);
        }
      },
    );
  }

  void _earnBadges() {
    final userId = context.read<UserDetailsCubit>().userId();
    final badgesCubit = context.read<BadgesCubit>();
    final config = context.read<SystemConfigCubit>();
    final quickestCorrectAnswerExtraScore =
        config.oneVsOneBattleQuickestCorrectAnswerExtraScore;
    final correctAnswerScore =
        config.quizCorrectAnswerCreditScore(QuizTypes.oneVsOneBattle);
    final languageId = UiUtils.getCurrentQuizLanguageId(context);

    if (widget.quizType == QuizTypes.oneVsOneBattle) {
      //if badges is locked
      if (badgesCubit.isBadgeLocked('ultimate_player')) {
        final badgeEarnPoints =
            (correctAnswerScore + quickestCorrectAnswerExtraScore) *
                totalQuestions();

        //if user's points is same as highest points
        final currentUser = widget.battleRoom!.user1!.uid == userId
            ? widget.battleRoom!.user1!
            : widget.battleRoom!.user2!;
        if (currentUser.points == badgeEarnPoints) {
          badgesCubit.setBadge(
            badgeType: 'ultimate_player',
            languageId: languageId,
          );
        }
      }
    } else if (widget.quizType == QuizTypes.funAndLearn) {
      //
      //if totalQuestion is less than minimum question then do not check for badges
      if (totalQuestions() < minimumQuestionsForBadges) {
        return;
      }

      //funAndLearn is related to flashback
      if (badgesCubit.isBadgeLocked('flashback')) {
        final funNLearnQuestionMinimumTimeForBadge =
            badgesCubit.getBadgeCounterByType('flashback');
        //if badges not loaded some how
        if (funNLearnQuestionMinimumTimeForBadge == -1) {
          return;
        }
        final badgeEarnTimeInSeconds =
            totalQuestions() * funNLearnQuestionMinimumTimeForBadge;
        if (correctAnswer() == totalQuestions() &&
            widget.timeTakenToCompleteQuiz! <=
                badgeEarnTimeInSeconds.toDouble()) {
          badgesCubit.setBadge(
            badgeType: 'flashback',
            languageId: languageId,
          );
        }
      }
    } else if (widget.quizType == QuizTypes.quizZone) {
      if (badgesCubit.isBadgeLocked('dashing_debut')) {
        badgesCubit.setBadge(
          badgeType: 'dashing_debut',
          languageId: languageId,
        );
      }
      //
      //if totalQuestion is less than minimum question then do not check for badges

      if (totalQuestions() < minimumQuestionsForBadges) {
        return;
      }

      if (badgesCubit.isBadgeLocked('brainiac')) {
        if (correctAnswer() == totalQuestions() &&
            !widget.hasUsedAnyLifeline!) {
          badgesCubit.setBadge(
            badgeType: 'brainiac',
            languageId: languageId,
          );
        }
      }
      // تم إزالة الشرط المتعلق بـ GuessTheWord لأن اختبارات تخمين الكلمة لم تعد مدعومة
    } else if (widget.quizType == QuizTypes.dailyQuiz) {
      if (badgesCubit.isBadgeLocked('thirsty')) {
        //
        badgesCubit.setBadge(
          badgeType: 'thirsty',
          languageId: languageId,
        );
      }
    }
  }

  Future<void> setContestLeaderboard() async {
    await Future<void>.delayed(Duration.zero);
    if (widget.quizType == QuizTypes.contest) {
      await context.read<SetContestLeaderboardCubit>().setContestLeaderboard(
            questionAttended: attemptedQuestion(),
            correctAns: correctAnswer(),
            contestId: widget.contestId,
            score: widget.myPoints,
          );
    }
  }

  String _getCoinUpdateTypeBasedOnQuizZone() {
    return switch (widget.quizType) {
      QuizTypes.quizZone => wonQuizZoneKey,
      QuizTypes.dailyQuiz => wonDailyQuizKey,
      QuizTypes.funAndLearn => wonFunNLearnKey,
      _ => '-',
    };
  }

  void _updateCoinsAndScore() {
    var points = widget.myPoints;
    if (widget.isPremiumCategory) {
      _earnedCoins = _earnedCoins * 2;
      points = widget.myPoints! * 2;
    }

    //update score and coins for user
    context.read<UpdateScoreAndCoinsCubit>().updateCoinsAndScore(
          widget.myPoints,
          _earnedCoins,
          _getCoinUpdateTypeBasedOnQuizZone(),
        );
    //update score locally and database
    context.read<UserDetailsCubit>().updateCoins(
          addCoin: true,
          coins: _earnedCoins,
        );

    context.read<UserDetailsCubit>().updateScore(points);
  }

  //
  void _updateScoreAndCoinsDetails() {
    //if percentage is more than 30 then update score and coins
    if (_isWinner) {
      //
      //if quizType is quizZone we need to update unlocked level,coins and score
      //only one time
      //
      if (widget.quizType == QuizTypes.quizZone) {
        //if given level is same as unlocked level then update level
        if (int.parse(widget.questions!.first.level!) == widget.unlockedLevel) {
          final updatedLevel = int.parse(widget.questions!.first.level!) + 1;
          //update level

          context.read<UpdateLevelCubit>().updateLevel(
                widget.categoryId!,
                widget.subcategoryId ?? '',
                updatedLevel.toString(),
              );

          _updateCoinsAndScore();
        } else {}
        if (widget.subcategoryId == '0') {
          context.read<UnlockedLevelCubit>().fetchUnlockLevel(
                widget.categoryId!,
                '0',
              );
        } else {
          context.read<SubCategoryCubit>().fetchSubCategory(widget.categoryId!);
        }
      }
      //
      else if (widget.quizType == QuizTypes.funAndLearn &&
          !widget.comprehension.isPlayed) {
        _updateCoinsAndScore();
        context.read<SetCategoryPlayed>().setCategoryPlayed(
              quizType: QuizTypes.funAndLearn,
              categoryId: widget.questions!.first.categoryId!,
              subcategoryId: widget.questions!.first.subcategoryId! == '0'
                  ? ''
                  : widget.questions!.first.subcategoryId!,
              typeId: widget.comprehension.id,
            );
      }
    }
    // fetchUpdateUserDetails();
  }

  void earnCoinsBasedOnWinPercentage() {
    if (_isWinner) {
      final percentage = winPercentage();
      _earnedCoins = UiUtils.coinsBasedOnWinPercentage(
        // تم إزالة guessTheWordMaxWinningCoins لأن اختبارات تخمين الكلمة لم تعد مدعومة
        percentage: percentage,
        quizType: widget.quizType!,
        maxCoinsWinningPercentage:
            context.read<SystemConfigCubit>().maxCoinsWinningPercentage,
        maxWinningCoins: context.read<SystemConfigCubit>().maxWinningCoins,
      );
    }
  }

  // التحقق من إمكانية عرض نافذة التقييم
  void _checkAndShowRatingPrompt() {
    // عرض نافذة التقييم فقط إذا كانت النتيجة أكبر من 70%
    if (winPercentage() >= 70) {
      // تأخير قصير للسماح بعرض النتائج أولاً
      Future.delayed(const Duration(seconds: 2), () async {
        if (!mounted) return;

        final shouldShow = await _ratingService.shouldShowRatingPrompt();

        if (shouldShow && mounted) {
          await _ratingService.showRatingPrompt();
        }
      });
    }
  }

  //This will execute once user press back button or go back from result screen
  //so respective data of category,sub category and fun n learn can be updated
  void onPageBackCalls() {
    if (widget.quizType == QuizTypes.funAndLearn &&
        _isWinner &&
        !widget.comprehension.isPlayed) {
      context.read<ComprehensionCubit>().getComprehension(
            languageId: UiUtils.getCurrentQuizLanguageId(context),
            type: widget.questions!.first.subcategoryId! == '0'
                ? 'category'
                : 'subcategory',
            typeId: widget.questions!.first.subcategoryId! == '0'
                ? widget.questions!.first.categoryId!
                : widget.questions!.first.subcategoryId!,
          );
    } else if (widget.questions != null && widget.questions!.isNotEmpty) {
      //
      if (widget.questions!.first.subcategoryId == '0') {
        //update category
        context.read<QuizCategoryCubit>().getQuizCategoryWithUserId(
              languageId: UiUtils.getCurrentQuizLanguageId(context),
              type: UiUtils.getCategoryTypeNumberFromQuizType(
                widget.quizType ??
                    QuizTypes
                        .quizZone, // استخدام نوع الاختبار الحالي مع التحقق من القيمة null
              ),
            );
      } else {
        //update subcategory
        context.read<SubCategoryCubit>().fetchSubCategory(
              widget.questions!.first.categoryId!,
            );
      }
    } else if (widget.quizType == QuizTypes.quizZone) {
      if (widget.subcategoryId == '') {
        context.read<UnlockedLevelCubit>().fetchUnlockLevel(
              widget.categoryId!,
              '0',
            );
      } else {
        context.read<SubCategoryCubit>().fetchSubCategory(widget.categoryId!);
      }
    }
    fetchUpdateUserDetails();
  }

  String getCategoryIdOfQuestion() {
    if (widget.quizType == QuizTypes.oneVsOneBattle) {
      return widget.battleRoom!.categoryId!.isEmpty
          ? '0'
          : widget.battleRoom!.categoryId!;
    }
    // تم إزالة الشرط المتعلق بـ GuessTheWord لأن اختبارات تخمين الكلمة لم تعد مدعومة
    return widget.questions!.first.categoryId!.isEmpty
        ? '-'
        : widget.questions!.first.categoryId!;
  }

  int correctAnswer() {
    if (widget.quizType == QuizTypes.exam) {
      return widget.correctExamAnswers!;
    }
    var correctAnswer = 0;
    for (final question in widget.questions!) {
      if (AnswerEncryption.decryptCorrectAnswer(
            rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
            correctAnswer: question.correctAnswer!,
          ) ==
          question.submittedAnswerId) {
        correctAnswer++;
      }
    }
    return correctAnswer;
  }

  int attemptedQuestion() {
    var attemptedQuestion = 0;
    if (widget.quizType == QuizTypes.exam) {
      return 0;
    }

    for (final question in widget.questions!) {
      if (question.attempted) {
        attemptedQuestion++;
      }
    }
    return attemptedQuestion;
  }

  double winPercentage() {
    if (widget.quizType == QuizTypes.oneVsOneBattle) return 0;

    if (widget.quizType == QuizTypes.exam) {
      return (widget.obtainedMarks! * 100.0) /
          int.parse(widget.exam!.totalMarks);
    }

    return (correctAnswer() * 100.0) / totalQuestions();
  }

  bool showCoinsAndScore() {
    if (widget.quizType == QuizTypes.selfChallenge ||
        widget.quizType == QuizTypes.contest ||
        widget.quizType == QuizTypes.exam ||
        widget.quizType == QuizTypes.dailyQuiz) {
      return false;
    }

    if (widget.quizType == QuizTypes.quizZone) {
      return _isWinner &&
          (int.parse(widget.questions!.first.level!) == widget.unlockedLevel);
    }
    if (widget.quizType == QuizTypes.funAndLearn) {
      //if user completed more than 30% and has not played this paragraph yet
      return _isWinner && !widget.comprehension.isPlayed;
    }

    return _isWinner;
  }

  int totalQuestions() {
    if (widget.quizType == QuizTypes.exam) {
      return widget.correctExamAnswers! + widget.incorrectExamAnswers!;
    }

    if (didSkipQue) {
      return widget.questions!.length - 1;
    }

    return widget.questions!.length;
  }

  Widget _buildGreetingMessage() {
    final String title;
    final String message;

    if (widget.quizType == QuizTypes.oneVsOneBattle) {
      if (_winnerId!.isEmpty) {
        title = 'matchDrawLbl';
        message = 'congratulationsLbl';
      } else if (_isWinner) {
        title = 'victoryLbl';
        message = 'congratulationsLbl';
      } else {
        title = 'defeatLbl';
        message = 'betterNextLbl';
      }
    } else if (widget.quizType == QuizTypes.exam) {
      title = widget.exam!.title;
      message = examResultKey;
    } else {
      final scorePct = winPercentage();

      if (scorePct <= 30) {
        title = goodEffort;
        message = keepLearning;
      } else if (scorePct <= 50) {
        title = wellDone;
        message = makingProgress;
      } else if (scorePct <= 70) {
        title = greatJob;
        message = closerToMastery;
      } else if (scorePct <= 90) {
        title = excellentWork;
        message = keepGoing;
      } else {
        title = fantasticJob;
        message = achievedMastery;
      }
    }

    final titleStyle = TextStyle(
      fontSize: 30,
      color: _isWinner
          ? Theme.of(context).primaryColor
          : Theme.of(context).colorScheme.onSurface,
      fontWeight: FontWeights.bold,
      letterSpacing: 0.5,
      shadows: [
        Shadow(
          color: _isWinner
              ? Theme.of(context).primaryColor.withOpacity(0.3)
              : Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
          offset: const Offset(1, 1),
          blurRadius: 2,
        ),
      ],
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 25),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          alignment: Alignment.center,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isWinner)
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.emoji_events,
                    color: Theme.of(context).primaryColor,
                    size: 30,
                  ),
                ),
              const SizedBox(width: 10),
              Flexible(
                child: Text(
                  widget.quizType == QuizTypes.exam
                      ? title
                      : context.tr(title)!,
                  textAlign: TextAlign.center,
                  style: titleStyle,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (widget.quizType != QuizTypes.exam &&
                  widget.quizType != QuizTypes.oneVsOneBattle) ...[
                Flexible(
                  child: Text(
                    " ${userName.split(' ').first}",
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    style: TextStyle(
                      fontSize: 30,
                      color: Theme.of(context).primaryColor,
                      overflow: TextOverflow.ellipsis,
                      fontWeight: FontWeights.bold,
                      letterSpacing: 0.5,
                      shadows: [
                        Shadow(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.3),
                          offset: const Offset(1, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 15),
        Container(
          alignment: Alignment.center,
          width: MediaQuery.of(context).size.shortestSide * .85,
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: _isWinner
                  ? [
                      Theme.of(context).primaryColor.withOpacity(0.15),
                      Theme.of(context).primaryColor.withOpacity(0.05),
                    ]
                  : [
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.08),
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.03),
                    ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: _isWinner
                  ? Theme.of(context).primaryColor.withOpacity(0.2)
                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: _isWinner
                    ? Theme.of(context).primaryColor.withOpacity(0.1)
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.05),
                blurRadius: 10,
                spreadRadius: 1,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Text(
            context.tr(message)!,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18,
              color: Theme.of(context).colorScheme.onSurface,
              height: 1.3,
              letterSpacing: 0.3,
            ),
          ),
        ),
      ],
    );
  }

  // دالة لعرض بيانات النتيجة بشكل مبسط

  Widget _buildIndividualResultContainer(String userProfileUrl) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF4CAF50),
            const Color(0xFF2196F3),
            const Color(0xFF00BCD4),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(35),
        boxShadow: [
          BoxShadow(
            color: (Theme.of(context).brightness == Brightness.dark
                    ? Colors.green.shade600
                    : const Color(0xFF4CAF50))
                .withOpacity(0.4),
            blurRadius: 30,
            spreadRadius: 0,
            offset: const Offset(0, 15),
          ),
          BoxShadow(
            color: Theme.of(context).colorScheme.surface.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: -5,
            offset: const Offset(-10, -10),
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(35),
          border: Border.all(
            color: Theme.of(context).colorScheme.surface.withOpacity(0.2),
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // قسم الصورة العلوي مع تصميم جميل
            Container(
              height: 200,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // خلفية دائرية متدرجة
                  Container(
                    width: 180,
                    height: 180,
                    decoration: BoxDecoration(
                      gradient: RadialGradient(
                        colors: [
                          Theme.of(context).colorScheme.surface.withOpacity(
                              Theme.of(context).brightness == Brightness.dark
                                  ? 0.3
                                  : 0.6),
                          Theme.of(context).colorScheme.surface.withOpacity(
                              Theme.of(context).brightness == Brightness.dark
                                  ? 0.1
                                  : 0.3),
                          Colors.transparent,
                        ],
                        stops: const [0.0, 0.7, 1.0],
                      ),
                      shape: BoxShape.circle,
                    ),
                  ),

                  // دوائر خلفية متحركة
                  Positioned(
                    top: 20,
                    right: 30,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.surface.withOpacity(
                                Theme.of(context).brightness == Brightness.dark
                                    ? 0.3
                                    : 0.5),
                            Theme.of(context).colorScheme.surface.withOpacity(
                                Theme.of(context).brightness == Brightness.dark
                                    ? 0.1
                                    : 0.2),
                          ],
                        ),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),

                  Positioned(
                    bottom: 30,
                    left: 20,
                    child: Container(
                      width: 25,
                      height: 25,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.surface.withOpacity(
                                Theme.of(context).brightness == Brightness.dark
                                    ? 0.4
                                    : 0.6),
                            Theme.of(context).colorScheme.surface.withOpacity(
                                Theme.of(context).brightness == Brightness.dark
                                    ? 0.2
                                    : 0.3),
                          ],
                        ),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),

                  // الصورة الرئيسية أو نتيجة الامتحان
                  if (widget.quizType == QuizTypes.exam)
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context)
                                .colorScheme
                                .surface
                                .withOpacity(0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: RadialPercentageResultContainer(
                        percentage: winPercentage(),
                        timeTakenToCompleteQuizInSeconds:
                            widget.examCompletedInMinutes,
                        size: const Size(140, 140),
                      ),
                    )
                  else
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.surface.withOpacity(
                                Theme.of(context).brightness == Brightness.dark
                                    ? 0.3
                                    : 0.5),
                            Theme.of(context).colorScheme.surface.withOpacity(
                                Theme.of(context).brightness == Brightness.dark
                                    ? 0.1
                                    : 0.2),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context)
                                .colorScheme
                                .surface
                                .withOpacity(Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? 0.4
                                    : 0.6),
                            blurRadius: 25,
                            spreadRadius: 3,
                          ),
                          BoxShadow(
                            color: Theme.of(context)
                                .colorScheme
                                .shadow
                                .withOpacity(0.1),
                            blurRadius: 15,
                            spreadRadius: -5,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.all(8),
                      child: QImage.circular(
                        imageUrl: userProfileUrl,
                        width: 120,
                        height: 120,
                      ),
                    ),

                  // أيقونة النتيجة في الزاوية
                  Positioned(
                    bottom: 10,
                    right: 10,
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: _isWinner
                              ? [
                                  const Color(0xFF4CAF50),
                                  const Color(0xFF45a049)
                                ]
                              : [
                                  const Color(0xFFFF9800),
                                  const Color(0xFFf57c00)
                                ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: (_isWinner
                                    ? const Color(0xFF4CAF50)
                                    : const Color(0xFFFF9800))
                                .withOpacity(0.4),
                            blurRadius: 15,
                            spreadRadius: 2,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Icon(
                        _isWinner
                            ? Icons.emoji_events
                            : Icons.sentiment_satisfied_alt,
                        color: Theme.of(context).colorScheme.onPrimary,
                        size: 28,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // رسالة التهنئة أو التشجيع
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.25
                            : 0.4),
                    Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.15
                            : 0.25),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        Theme.of(context).colorScheme.surface.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: -2,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Text(
                _isWinner
                    ? "🎉 مبروك! أداء رائع"
                    : "💪 حاول مرة أخرى، أنت قريب من النجاح",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                  height: 1.3,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // قسم النتائج والإحصائيات
            if (widget.quizType != QuizTypes.exam)
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.surface.withOpacity(
                          Theme.of(context).brightness == Brightness.dark
                              ? 0.2
                              : 0.4),
                      Theme.of(context).colorScheme.surface.withOpacity(
                          Theme.of(context).brightness == Brightness.dark
                              ? 0.1
                              : 0.2),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.3
                            : 0.5),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 15,
                      spreadRadius: -3,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // النسبة المئوية
                    Expanded(
                      child: Column(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Theme.of(context)
                                            .colorScheme
                                            .surface
                                            .withOpacity(
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? 0.9
                                                    : 0.8),
                                        Theme.of(context)
                                            .colorScheme
                                            .surface
                                            .withOpacity(
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? 0.7
                                                    : 0.6),
                                      ],
                                    ),
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .surface
                                            .withOpacity(
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? 0.5
                                                    : 0.7),
                                        blurRadius: 10,
                                        spreadRadius: 2,
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 80,
                                  height: 80,
                                  child: CircularProgressIndicator(
                                    value: winPercentage() / 100,
                                    strokeWidth: 4,
                                    backgroundColor: Theme.of(context)
                                        .colorScheme
                                        .surface
                                        .withOpacity(
                                            Theme.of(context).brightness ==
                                                    Brightness.dark
                                                ? 0.3
                                                : 0.5),
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? Colors.green.shade600
                                          : const Color(0xFF4CAF50),
                                    ),
                                  ),
                                ),
                                Text(
                                  "${winPercentage().toStringAsFixed(0)}%",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.green.shade600
                                        : const Color(0xFF4CAF50),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "النسبة",
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // الوقت المستغرق
                    if (widget.timeTakenToCompleteQuiz != null)
                      Expanded(
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Theme.of(context)
                                        .colorScheme
                                        .surface
                                        .withOpacity(
                                            Theme.of(context).brightness ==
                                                    Brightness.dark
                                                ? 0.9
                                                : 0.8),
                                    Theme.of(context)
                                        .colorScheme
                                        .surface
                                        .withOpacity(
                                            Theme.of(context).brightness ==
                                                    Brightness.dark
                                                ? 0.7
                                                : 0.6),
                                  ],
                                ),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .surface
                                        .withOpacity(
                                            Theme.of(context).brightness ==
                                                    Brightness.dark
                                                ? 0.5
                                                : 0.7),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.timer_rounded,
                                color: const Color(0xFF2196F3),
                                size: 32,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _formatTime(
                                  widget.timeTakenToCompleteQuiz!.toInt()),
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).colorScheme.onSurface,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              "الوقت",
                              style: TextStyle(
                                fontSize: 10,
                                color: Theme.of(context).colorScheme.onSurface,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),

            // معلومات الامتحان
            if (widget.quizType == QuizTypes.exam)
              Container(
                margin: const EdgeInsets.only(bottom: 20),
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.surface.withOpacity(0.25),
                      Theme.of(context).colorScheme.surface.withOpacity(0.15),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color:
                        Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${widget.obtainedMarks}/${widget.exam!.totalMarks} ${context.tr(markKey)!}',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

            const SizedBox(height: 20),

            // معلومات الإجابات والنقاط بتصميم جديد
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.2
                            : 0.4),
                    Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.1
                            : 0.2),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 15,
                    spreadRadius: -3,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // الإجابات الصحيحة
                  _buildEnhancedResultData(
                    '${correctAnswer()}',
                    Icons.check_circle_rounded,
                    const Color(0xFF4CAF50),
                    "صحيحة",
                  ),

                  // خط فاصل جميل
                  Container(
                    height: 60,
                    width: 2,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context)
                              .colorScheme
                              .outline
                              .withOpacity(0.1),
                          Theme.of(context)
                              .colorScheme
                              .outline
                              .withOpacity(0.4),
                          Theme.of(context)
                              .colorScheme
                              .outline
                              .withOpacity(0.1),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),

                  // الإجابات الخاطئة
                  _buildEnhancedResultData(
                    widget.quizType == QuizTypes.exam
                        ? '${widget.incorrectExamAnswers}'
                        : '${totalQuestions() - correctAnswer()}',
                    Icons.cancel_rounded,
                    const Color(0xFFF44336),
                    "خاطئة",
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دالة مساعدة لبناء عناصر النتائج المحسنة
  Widget _buildEnhancedResultData(
      String value, IconData icon, Color color, String label) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                color.withOpacity(0.8),
                color.withOpacity(0.6),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.3),
                blurRadius: 10,
                spreadRadius: 1,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.onPrimary,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // دالة لتنسيق الوقت بشكل أفضل
  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return "$minutes:${remainingSeconds.toString().padLeft(2, '0')} دقيقة";
  }

  Widget _buildBattleResultDetails() {
    final winnerDetails = widget.battleRoom!.user1!.uid == _winnerId
        ? widget.battleRoom!.user1
        : widget.battleRoom!.user2;
    final looserDetails = widget.battleRoom!.user1!.uid != _winnerId
        ? widget.battleRoom!.user1
        : widget.battleRoom!.user2;

    return _winnerId == null
        ? const SizedBox()
        : LayoutBuilder(
            builder: (context, constraints) {
              var verticalSpacePercentage = 0.0;
              if (constraints.maxHeight <
                  UiUtils.profileHeightBreakPointResultScreen) {
                verticalSpacePercentage = _winnerId!.isEmpty ? 0.035 : 0.03;
              } else {
                verticalSpacePercentage = _winnerId!.isEmpty ? 0.075 : 0.05;
              }
              return Column(
                children: [
                  _buildGreetingMessage(),
                  if (widget.entryFee! > 0)
                    context.read<UserDetailsCubit>().userId() == _winnerId
                        ? Padding(
                            padding: const EdgeInsets.only(top: 20, bottom: 20),
                            child: Container(
                              padding: const EdgeInsets.only(
                                top: 10,
                                bottom: 10,
                                right: 30,
                                left: 30,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.2),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                "${context.tr("youWin")!} ${widget.entryFee! * 2} ${context.tr("coinsLbl")!}",
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                          )
                        : _winnerId!.isEmpty
                            ? const SizedBox()
                            : Padding(
                                padding: const EdgeInsets.only(
                                  top: 20,
                                  bottom: 20,
                                ),
                                child: Container(
                                  padding: const EdgeInsets.only(
                                    top: 10,
                                    bottom: 10,
                                    right: 30,
                                    left: 30,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onTertiary
                                        .withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    "${context.tr("youLossLbl")!} ${widget.entryFee} ${context.tr("coinsLbl")!}",
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onTertiary,
                                    ),
                                  ),
                                ),
                              )
                  else
                    const SizedBox(height: 50),
                  SizedBox(
                    height:
                        constraints.maxHeight * verticalSpacePercentage - 10.2,
                  ),
                  if (_winnerId!.isEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    QImage.circular(
                                      width: 80,
                                      height: 80,
                                      imageUrl:
                                          widget.battleRoom!.user1!.profileUrl,
                                    ),
                                    Center(
                                      child: SvgPicture.asset(
                                        Assets.hexagonFrame,
                                        height: 90,
                                        width: 90,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  widget.battleRoom!.user1!.name,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight: FontWeights.bold,
                                    fontSize: 16,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onTertiary,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 18,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .scaffoldBackgroundColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${context.tr(scoreLbl)} ${widget.battleRoom!.user1!.points}',
                                    style: TextStyle(
                                      fontWeight: FontWeights.bold,
                                      fontSize: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onTertiary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 5),
                          Column(
                            children: [
                              SvgPicture.asset(
                                Assets.versus,
                                width: MediaQuery.of(context).size.width * 0.12,
                                height:
                                    MediaQuery.of(context).size.height * 0.12,
                              ),
                              const SizedBox(
                                height: 80,
                              ),
                              const SizedBox(),
                            ],
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    QImage.circular(
                                      width: 80,
                                      height: 80,
                                      imageUrl:
                                          widget.battleRoom!.user2!.profileUrl,
                                    ),
                                    Center(
                                      child: SvgPicture.asset(
                                        Assets.hexagonFrame,
                                        width: 90,
                                        height: 90,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  widget.battleRoom!.user2!.name,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight: FontWeights.bold,
                                    fontSize: 16,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onTertiary,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 18,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .scaffoldBackgroundColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${context.tr(scoreLbl)} ${widget.battleRoom!.user2!.points}',
                                    style: TextStyle(
                                      fontWeight: FontWeights.bold,
                                      fontSize: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onTertiary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    QImage.circular(
                                      width: 80,
                                      height: 80,
                                      imageUrl: winnerDetails!.profileUrl,
                                    ),
                                    Center(
                                      child: SvgPicture.asset(
                                        Assets.hexagonFrame,
                                        width: 90,
                                        height: 90,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  winnerDetails.name,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight: FontWeights.bold,
                                    fontSize: 16,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onTertiary,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 18,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .scaffoldBackgroundColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${context.tr(scoreLbl)} ${winnerDetails.points}',
                                    style: TextStyle(
                                      fontWeight: FontWeights.bold,
                                      fontSize: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onTertiary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Column(
                            children: [
                              SvgPicture.asset(
                                Assets.versus,
                                width: MediaQuery.of(context).size.width * 0.12,
                                height:
                                    MediaQuery.of(context).size.height * 0.12,
                              ),
                              const SizedBox(
                                height: 80,
                              ),
                              const SizedBox(),
                            ],
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    QImage.circular(
                                      width: 80,
                                      height: 80,
                                      imageUrl: looserDetails!.profileUrl,
                                    ),
                                    Center(
                                      child: SvgPicture.asset(
                                        Assets.hexagonFrame,
                                        colorFilter: ColorFilter.mode(
                                          Theme.of(context)
                                              .colorScheme
                                              .onTertiary,
                                          BlendMode.srcIn,
                                        ),
                                        width: 90,
                                        height: 90,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  looserDetails.name,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight: FontWeights.bold,
                                    fontSize: 16,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onTertiary,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 18,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .scaffoldBackgroundColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${context.tr(scoreLbl)} ${looserDetails.points}',
                                    style: TextStyle(
                                      fontWeight: FontWeights.bold,
                                      fontSize: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onTertiary,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              );
            },
          );
  }

  Widget _buildResultDetails(BuildContext context) {
    final userProfileUrl =
        context.read<UserDetailsCubit>().getUserProfile().profileUrl ?? '';

    //build results for 1 user
    if (widget.numberOfPlayer == 1) {
      return _buildIndividualResultContainer(userProfileUrl);
    }
    if (widget.numberOfPlayer == 2) {
      return _buildBattleResultDetails();
    }
    return const SizedBox();
  }

  // دالة مشاركة النتائج
  Future<void> _shareResult(BuildContext context) async {
    try {
      // حفظ سياق التطبيق قبل العمليات غير المتزامنة
      final appName = "مجتهد";
      final currentContext = context;

      // التقاط لقطة شاشة للنتائج
      final image = await screenshotController.capture();
      if (image == null) return;
      if (!mounted) return;

      // حفظ الصورة مؤقتًا
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/quiz_result.png');
      await file.writeAsBytes(image.buffer.asUint8List());
      if (!mounted) return;

      // نص المشاركة
      final scoreText = '$appName\n'
          'نتيجتي في الاختبار: ${correctAnswer()}/${totalQuestions()}\n'
          'انضم إلينا وشارك في التحدي!';

      // مشاركة النتائج
      if (!mounted) return;
      await UiUtils.share(
        scoreText,
        files: [XFile(file.path)],
        context: currentContext,
      );
    } catch (e) {
      if (!mounted) return;
      UiUtils.showSnackBar('حدث خطأ أثناء المشاركة', context);
    }
  }

  Widget _buildResultContainer(BuildContext context) {
    return Screenshot(
      controller: screenshotController,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.surface.withOpacity(
                  Theme.of(context).brightness == Brightness.dark
                      ? 0.25
                      : 0.45),
              Theme.of(context).colorScheme.surface.withOpacity(
                  Theme.of(context).brightness == Brightness.dark
                      ? 0.15
                      : 0.35),
              Theme.of(context).colorScheme.surface.withOpacity(
                  Theme.of(context).brightness == Brightness.dark
                      ? 0.05
                      : 0.25),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.2),
              blurRadius: 20,
              spreadRadius: 2,
              offset: const Offset(0, 8),
            ),
          ],
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: 2,
          ),
        ),
        padding: const EdgeInsets.all(25),
        child: Column(
          children: [
            // أيقونة النتيجة المحسنة
            Container(
              width: 90,
              height: 90,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: _isWinner
                      ? [
                          const Color(0xFFFFD700),
                          const Color(0xFFFFA500),
                          const Color(0xFFFF8C00),
                        ]
                      : [
                          Theme.of(context).primaryColor,
                          Theme.of(context).primaryColor.withOpacity(0.7),
                        ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: _isWinner
                        ? const Color(0xFFFFD700).withOpacity(0.4)
                        : Theme.of(context).primaryColor.withOpacity(0.4),
                    blurRadius: 15,
                    spreadRadius: 3,
                    offset: const Offset(0, 5),
                  ),
                  BoxShadow(
                    color: Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.8
                            : 0.6),
                    blurRadius: 8,
                    spreadRadius: -3,
                    offset: const Offset(-3, -3),
                  ),
                ],
              ),
              child: Icon(
                _isWinner ? Icons.emoji_events : Icons.sentiment_satisfied_alt,
                color: Theme.of(context).colorScheme.onPrimary,
                size: 45,
              ),
            ),
            const SizedBox(height: 15),

            // رسالة النتيجة المحسنة
            Container(
              margin: const EdgeInsets.only(bottom: 25),
              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.2
                            : 0.4),
                    Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.1
                            : 0.2),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Theme.of(context).colorScheme.surface.withOpacity(
                      Theme.of(context).brightness == Brightness.dark
                          ? 0.3
                          : 0.5),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Text(
                    _getMainMessage(),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimary,
                      height: 1.3,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getSubMessage(),
                    style: TextStyle(
                      fontSize: 15,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.9),
                      height: 1.2,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // تفاصيل النتائج
            _buildResultDetails(context),
          ],
        ),
      ),
    );
  }

  Widget _buildButton(
    String buttonTitle,
    Function onTap,
    BuildContext context,
  ) {
    // تحديد الأيقونة والألوان المناسبة للزر
    IconData buttonIcon;
    Color baseColor;

    if (buttonTitle.contains("إعادة") || buttonTitle.contains("Play")) {
      buttonIcon = Icons.replay_rounded;
      baseColor = Colors.green;
    } else if (buttonTitle.contains("الرئيسية") ||
        buttonTitle.contains("Home")) {
      buttonIcon = Icons.home_rounded;
      baseColor = Colors.blue;
    } else if (buttonTitle.contains("التالي") || buttonTitle.contains("Next")) {
      buttonIcon = Icons.arrow_forward_rounded;
      baseColor = Colors.orange;
    } else if (buttonTitle.contains("مراجعة") ||
        buttonTitle.contains("Review")) {
      buttonIcon = Icons.quiz_rounded;
      baseColor = Colors.purple;
    } else if (buttonTitle.contains("مشاركة") ||
        buttonTitle.contains("Share")) {
      buttonIcon = Icons.share_rounded;
      baseColor = Colors.deepOrange;
    } else {
      buttonIcon = Icons.check_circle_rounded;
      baseColor = Theme.of(context).colorScheme.primary;
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.1),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap as VoidCallback,
          borderRadius: BorderRadius.circular(20),
          splashColor: baseColor.withOpacity(0.2),
          highlightColor: baseColor.withOpacity(0.1),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: baseColor.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            child: Container(
              height: 60,
              width: MediaQuery.of(context).size.width * 0.85,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // أيقونة الزر
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: baseColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      buttonIcon,
                      color: baseColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 15),

                  // نص الزر
                  Text(
                    buttonTitle,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  //play again button will be build different for every quizType
  Widget _buildPlayAgainButton() {
    if (widget.quizType == QuizTypes.selfChallenge) {
      return const SizedBox();
    } else if (widget.quizType == QuizTypes.funAndLearn) {
      return Container();
    } else if (widget.quizType == QuizTypes.quizZone) {
      //if user is winner
      if (_isWinner) {
        //we need to check if currentLevel is last level or not
        final maxLevel = int.parse(widget.subcategoryMaxLevel!);
        final currentLevel = int.parse(widget.questions!.first.level!);
        if (maxLevel == currentLevel) {
          return const SizedBox.shrink();
        }
        return _buildButton(
          context.tr('nextLevelBtn')!,
          () async {
            if (widget.isPremiumCategory) {
              if (widget.isPremiumCategory && currentLevel >= maxMiumlever) {
                // التحقق من حالة الاشتراك
                final isSubscribed = await SubscriptionManager.instance
                    .checkSubscriptionStatus();

                if (!isSubscribed) {
                  // عرض نافذة المحتوى المميز
                  if (!context.mounted) return;
                  showDialog(
                    // ignore: use_build_context_synchronously
                    context: context,
                    builder: (context) => const PremiumContentDialog(
                      title: AppConstants.premiumContentTitle,
                      message: AppConstants.premiumContentMessage,
                    ),
                  );
                  return;
                }
              }
            }
            //if given level is same as unlocked level then we need to update level
            //else do not update level
            final unlockedLevel = int.parse(widget.questions!.first.level!) ==
                    widget.unlockedLevel
                ? (widget.unlockedLevel! + 1)
                : widget.unlockedLevel;
            //play quiz for next level
            Navigator.of(context).pushReplacementNamed(
              Routes.quiz,
              arguments: {
                'numberOfPlayer': widget.numberOfPlayer,
                'quizType': widget.quizType,
                //if subcategory id is empty for question means we need to fetch question by it's category
                'categoryId': widget.categoryId,
                'subcategoryId': widget.subcategoryId,
                'level': (currentLevel + 1).toString(),
                //increase level
                'subcategoryMaxLevel': widget.subcategoryMaxLevel,
                'unlockedLevel': unlockedLevel,
              },
            );
          },
          context,
        );
      }
      //if user failed to complete this level
      return _buildButton(
        context.tr('playAgainBtn')!,
        () {
          fetchUpdateUserDetails();
          //to play this level again (for quizZone quizType)
          Navigator.of(context).pushReplacementNamed(
            Routes.quiz,
            arguments: {
              'numberOfPlayer': widget.numberOfPlayer,
              'quizType': widget.quizType,
              //if subcategory id is empty for question means we need to fetch questions by it's category
              'categoryId': widget.categoryId,
              'subcategoryId': widget.subcategoryId,
              'level': widget.questions!.first.level,
              'unlockedLevel': widget.unlockedLevel,
              'subcategoryMaxLevel': widget.subcategoryMaxLevel,
            },
          );
        },
        context,
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildShareYourScoreButton() {
    return Builder(
      builder: (context) {
        return _buildButton(
          context.tr('shareScoreBtn')!,
          () async {
            try {
              //capturing image
              final image = await screenshotController.capture();
              //root directory path
              final directory = (await getApplicationDocumentsDirectory()).path;

              final fileName = DateTime.now().microsecondsSinceEpoch.toString();
              //create file with given path
              final file = await File('$directory/$fileName.png').create();
              //write as bytes
              await file.writeAsBytes(image!.buffer.asUint8List());

              final appLink = context.read<SystemConfigCubit>().appUrl;

              // final referralCode =
              //     context.read<UserDetailsCubit>().getUserProfile().referCode ??
              //         '';

              final scoreText = '$appName'
                  "\n${context.tr('myScoreLbl')!}"
                  "\n${context.tr("appLink")!}"
                  '\n$appLink';

              await UiUtils.share(
                scoreText,
                files: [XFile(file.path)],
                context: context,
              ).onError(
                (e, s) => ShareResult('$e', ShareResultStatus.dismissed),
              );
            } catch (e) {
              if (!mounted) return;

              UiUtils.showSnackBar(
                context.tr(
                  convertErrorCodeToLanguageKey(errorCodeDefaultMessage),
                )!,
                context,
              );
            }
          },
          context,
        );
      },
    );
  }

  bool _unlockedReviewAnswersOnce = true;

  Widget _buildReviewAnswersButton() {
    void onTapYesReviewAnswers() {
      _unlockedReviewAnswersOnce = true;
      // Navigator.of(context).pop();

      // تم إزالة الشرط المتعلق بـ GuessTheWord لأن اختبارات تخمين الكلمة لم تعد مدعومة
      Navigator.of(context).pushNamed(
        Routes.reviewAnswers,
        arguments: {
          'quizType': widget.quizType,
          'questions': widget.questions,
          // تم إزالة guessTheWordQuestions لأن اختبارات تخمين الكلمة لم تعد مدعومة
          'guessTheWordQuestions': [],
        },
      );
    }

    return _buildButton(
      context.tr('reviewAnsBtn')!,
      () {
        if (_unlockedReviewAnswersOnce) {
          // تم إزالة الشرط المتعلق بـ GuessTheWord لأن اختبارات تخمين الكلمة لم تعد مدعومة
          Navigator.of(context).pushNamed(
            Routes.reviewAnswers,
            arguments: {
              'quizType': widget.quizType,
              'questions': widget.questions,
              // تم إزالة guessTheWordQuestions لأن اختبارات تخمين الكلمة لم تعد مدعومة
              'guessTheWordQuestions': [],
            },
          );
          return;
        }

        showDialog<void>(
          context: context,
          builder: (_) => AlertDialog(
            actions: [
              TextButton(
                onPressed: onTapYesReviewAnswers,
                child: Text(
                  context.tr(continueLbl)!,
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
              ),

              /// Cancel Button
              TextButton(
                onPressed: Navigator.of(context).pop,
                child: Text(
                  context.tr(cancelButtonKey)!,
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
              ),
            ],
            content: Text(
              '${context.read<SystemConfigCubit>().reviewAnswersDeductCoins} ${context.tr(coinsWillBeDeductedKey)!}',
              style: TextStyle(color: Theme.of(context).primaryColor),
            ),
          ),
        );
      },
      context,
    );
  }

  Widget _buildHomeButton() {
    void onTapHomeButton() {
      fetchUpdateUserDetails();
      Navigator.of(context).pushNamedAndRemoveUntil(
        Routes.home,
        (_) => false,
        arguments: false,
      );
    }

    return _buildButton(
      context.tr('homeBtn')!,
      onTapHomeButton,
      context,
    );
  }

  Widget _buildResultButtons(BuildContext context) {
    const buttonSpace = SizedBox(height: 15);

    return Column(
      children: [
        if (widget.quizType! != QuizTypes.exam &&
            widget.quizType != QuizTypes.oneVsOneBattle) ...[
          _buildPlayAgainButton(),
          buttonSpace,
        ],
        if (widget.quizType == QuizTypes.quizZone ||
            widget.quizType == QuizTypes.dailyQuiz ||
            widget.quizType == QuizTypes.selfChallenge ||
            widget.quizType == QuizTypes.funAndLearn ||
            widget.quizType == QuizTypes.bookmarkQuiz) ...[
          _buildReviewAnswersButton(),
          buttonSpace,
        ],
        _buildShareYourScoreButton(),
        buttonSpace,
        _buildHomeButton(),
        buttonSpace,
      ],
    );
  }

  String get _appbarTitle {
    final (title, emoji) = switch (widget.quizType) {
      QuizTypes.selfChallenge => ('selfChallengeResult', '🎯'),
      QuizTypes.exam => ('examResult', '📝'),
      QuizTypes.dailyQuiz => ('dailyQuizResult', '📅'),
      QuizTypes.oneVsOneBattle => ('randomBattleResult', '⚔️'),
      QuizTypes.funAndLearn => ('funAndLearnResult', '🎮'),
      QuizTypes.bookmarkQuiz => ('bookmarkQuizResult', '🔖'),
      _ => ('quizResultLbl', '🏆'),
    };

    return '$emoji ${context.tr(title)!}';
  }

  String _getMainMessage() {
    if (_isWinner) {
      final messages = [
        "🎉 أحسنت! لقد أكملت الاختبار بنجاح",
        "🌟 رائع! أداء متميز ومبهر",
        "🏆 ممتاز! لقد حققت نتيجة رائعة",
        "✨ مذهل! استمر في هذا التقدم",
      ];
      return messages[DateTime.now().millisecond % messages.length];
    } else {
      final messages = [
        "💪 حاول مرة أخرى، أنت قادر على تحقيق نتيجة أفضل",
        "🎯 لا بأس، كل محاولة خطوة نحو النجاح",
        "🚀 استمر في المحاولة، النجاح قريب",
        "⭐ لا تستسلم، أنت أقرب للهدف مما تتخيل",
      ];
      return messages[DateTime.now().millisecond % messages.length];
    }
  }

  String _getSubMessage() {
    if (_isWinner) {
      final messages = [
        "استمر في التقدم والتعلم! 📚",
        "أنت على الطريق الصحيح! 🎯",
        "مهاراتك تتطور باستمرار! 🌱",
        "إنجاز رائع يستحق التقدير! 👏",
      ];
      return messages[DateTime.now().millisecond % messages.length];
    } else {
      final messages = [
        "لا تستسلم، كل محاولة تقربك من النجاح 🌟",
        "التعلم رحلة، والأخطاء جزء منها 📖",
        "المثابرة هي مفتاح النجاح 🔑",
        "كل خطوة تجعلك أقوى وأكثر خبرة 💪",
      ];
      return messages[DateTime.now().millisecond % messages.length];
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop:
          context.read<UserDetailsCubit>().state is! UserDetailsFetchInProgress,
      // ignore: deprecated_member_use
      onPopInvoked: (didPop) {
        if (didPop) return;

        onPageBackCalls();
      },
      child: MultiBlocListener(
        listeners: [
          BlocListener<UpdateScoreAndCoinsCubit, UpdateScoreAndCoinsState>(
            listener: (context, state) {
              if (state is UpdateScoreAndCoinsFailure) {
                if (state.errorMessage == errorCodeUnauthorizedAccess) {
                  //already showed already logged in from other api error
                  if (!_displayedAlreadyLoggedInDialog) {
                    _displayedAlreadyLoggedInDialog = true;
                    showAlreadyLoggedInDialog(context);
                    return;
                  }
                }
              }
            },
          ),
          BlocListener<UpdateStatisticCubit, UpdateStatisticState>(
            listener: (context, state) {
              if (state is UpdateStatisticFailure) {
                //
                if (state.errorMessageCode == errorCodeUnauthorizedAccess) {
                  //already showed already logged in from other api error
                  if (!_displayedAlreadyLoggedInDialog) {
                    _displayedAlreadyLoggedInDialog = true;
                    showAlreadyLoggedInDialog(context);
                    return;
                  }
                }
              }
            },
          ),
          BlocListener<SetContestLeaderboardCubit, SetContestLeaderboardState>(
            listener: (context, state) {
              if (state is SetContestLeaderboardFailure) {
                //
                if (state.errorMessage == errorCodeUnauthorizedAccess) {
                  //already showed already logged in from other api error
                  if (!_displayedAlreadyLoggedInDialog) {
                    _displayedAlreadyLoggedInDialog = true;
                    showAlreadyLoggedInDialog(context);
                    return;
                  }
                }
              }
              if (state is SetContestLeaderboardSuccess) {
                context.read<ContestCubit>().getContest(
                      languageId: UiUtils.getCurrentQuizLanguageId(context),
                    );
              }
            },
          ),
        ],
        child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor.withOpacity(0.1),
                    Colors.transparent,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
            title: Text(
              _appbarTitle,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.bold,
                fontSize: 24,
                letterSpacing: 0.5,
                shadows: [
                  Shadow(
                    color: Theme.of(context).shadowColor.withOpacity(0.3),
                    offset: Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
            leading: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface.withOpacity(
                      Theme.of(context).brightness == Brightness.dark
                          ? 0.8
                          : 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).shadowColor.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.arrow_back_rounded,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 22,
                ),
              ),
              onPressed: () {
                onPageBackCalls();
                Navigator.pop(context);
              },
            ),
            actions: [
              // زر مشاركة النتائج محسن
              IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface.withOpacity(
                        Theme.of(context).brightness == Brightness.dark
                            ? 0.8
                            : 0.9),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).shadowColor.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.share_rounded,
                    color: Theme.of(context).colorScheme.onSurface,
                    size: 22,
                  ),
                ),
                onPressed: () => _shareResult(context),
              ),
              const SizedBox(width: 8),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.primary,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
            child: SafeArea(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // حاوية النتائج
                      _buildResultContainer(context),
                      const SizedBox(height: 25),
                      // أزرار التحكم
                      _buildResultButtons(context),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
