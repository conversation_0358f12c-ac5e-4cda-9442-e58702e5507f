import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:google_fonts/google_fonts.dart';

class VideoQuestionWidget extends StatefulWidget {
  final Question question;
  final double? width;
  final double? height;
  final bool autoPlay;
  final bool showControls;
  final VoidCallback? onFullScreenTap;

  const VideoQuestionWidget({
    super.key,
    required this.question,
    this.width,
    this.height,
    this.autoPlay = false,
    this.showControls = true,
    this.onFullScreenTap,
  });

  @override
  State<VideoQuestionWidget> createState() => _VideoQuestionWidgetState();
}

class _VideoQuestionWidgetState extends State<VideoQuestionWidget> {
  YoutubePlayerController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  // ignore: unused_field
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    print('🎥 [VIDEO LOG] بدء تهيئة مشغل الفيديو');
    print('🎥 [VIDEO LOG] Question ID: ${widget.question.id}');
    print('🎥 [VIDEO LOG] Video URL: ${widget.question.videoUrl}');
    print('🎥 [VIDEO LOG] Has Video: ${widget.question.hasVideo}');

    if (!widget.question.hasVideo) {
      print('❌ [VIDEO LOG] السؤال لا يحتوي على فيديو');
      setState(() {
        _hasError = true;
        _errorMessage = 'رابط الفيديو غير صحيح';
        _isLoading = false;
      });
      return;
    }

    print('🎥 [VIDEO LOG] Video ID: ${widget.question.videoId}');
    print('🎥 [VIDEO LOG] Start Time: ${widget.question.videoStartTime}');
    print('🎥 [VIDEO LOG] Auto Play: ${widget.autoPlay}');

    try {
      _controller = YoutubePlayerController(
        initialVideoId: widget.question.videoId!,
        flags: YoutubePlayerFlags(
          autoPlay: widget.autoPlay,
          mute: false,
          disableDragSeek: false,
          loop: false,
          isLive: false,
          forceHD: false,
          enableCaption: false,
          showLiveFullscreenButton: true,
          startAt: widget.question.videoStartTime ?? 0,
        ),
      );

      print('✅ [VIDEO LOG] تم إنشاء YoutubePlayerController بنجاح');
      _controller!.addListener(_onVideoStateChange);

      // تأخير قصير لمحاكاة التحميل
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          print('✅ [VIDEO LOG] تم تحميل الفيديو بنجاح');
          setState(() {
            _isLoading = false;
          });
        }
      });
    } catch (e) {
      print('❌ [VIDEO LOG] خطأ في تحميل الفيديو: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'حدث خطأ في تحميل الفيديو: $e';
        _isLoading = false;
      });
    }
  }

  void _onVideoStateChange() {
    if (_controller != null) {
      final state = _controller!.value.playerState;
      final position = _controller!.value.position;
      final duration = _controller!.value.metaData.duration;

      print('🎬 [VIDEO STATE] Player State: $state');
      print('🎬 [VIDEO STATE] Position: ${position.inSeconds}s');
      print('🎬 [VIDEO STATE] Duration: ${duration.inSeconds}s');
      print('🎬 [VIDEO STATE] Is Ready: ${_controller!.value.isReady}');
      print('🎬 [VIDEO STATE] Has Error: ${_controller!.value.hasError}');

      if (_controller!.value.hasError) {
        print('❌ [VIDEO STATE] Error: ${_controller!.value.errorCode}');
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_onVideoStateChange);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.question.hasVideo) {
      return const SizedBox.shrink();
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    if (_isLoading) {
      return _buildLoadingWidget();
    }

    return YoutubePlayerBuilder(
      onEnterFullScreen: () {
        // إذا كان هناك callback مخصص، استخدمه بدلاً من الشاشة الكاملة العادية
        if (widget.onFullScreenTap != null) {
          widget.onFullScreenTap!();
        }
      },
      onExitFullScreen: () {},
      player: YoutubePlayer(
        controller: _controller!,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Theme.of(context).primaryColor,
        progressColors: ProgressBarColors(
          playedColor: Theme.of(context).primaryColor,
          handleColor: Theme.of(context).primaryColor,
          bufferedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
          backgroundColor: Colors.grey.shade300,
        ),
        bottomActions: widget.showControls
            ? [
                const CurrentPosition(),
                const SizedBox(width: 10),
                Expanded(
                  child: ProgressBar(
                    colors: ProgressBarColors(
                      playedColor: Theme.of(context).primaryColor,
                      handleColor: Colors.white,
                      bufferedColor:
                          Theme.of(context).primaryColor.withValues(alpha: 0.2),
                      backgroundColor: Colors.grey.shade300,
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                const RemainingDuration(),
                const SizedBox(width: 10),
                PlaybackSpeedButton(
                  icon: Icon(
                    Icons.speed,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(width: 10),
                // زر شاشة كاملة مخصص أو عادي
                widget.onFullScreenTap != null
                    ? _buildCustomFullScreenButton()
                    : const FullScreenButton(),
              ]
            : [],
      ),
      builder: (context, player) => _buildVideoContainer(player),
    );
  }

  Widget _buildVideoContainer(Widget player) {
    return Container(
      width: widget.width ?? MediaQuery.of(context).size.width,
      height: widget.height ?? 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: player,
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.width ?? MediaQuery.of(context).size.width,
      height: widget.height ?? 200,
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            color: Colors.orange,
            size: 48,
          ),
          const SizedBox(height: 12),
          Text(
            "عذراً، لا يمكن تحميل الفيديو",
            style: GoogleFonts.ibmPlexSansArabic(
              color: Colors.orange.shade700,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            "يرجى التحقق من الاتصال بالإنترنت أو المحاولة لاحقاً",
            style: GoogleFonts.ibmPlexSansArabic(
              color: Colors.orange.shade600,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomFullScreenButton() {
    return GestureDetector(
      onTap: widget.onFullScreenTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          Icons.fullscreen,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      width: widget.width ?? MediaQuery.of(context).size.width,
      height: widget.height ?? 200,
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل الفيديو...',
            style: GoogleFonts.ibmPlexSansArabic(
              color: Theme.of(context).primaryColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// إيقاف الفيديو
  void pauseVideo() {
    _controller?.pause();
  }

  /// تشغيل الفيديو
  void playVideo() {
    _controller?.play();
  }

  /// التحقق من حالة التشغيل
  bool get isPlaying => _controller?.value.isPlaying ?? false;

  /// الحصول على الوقت الحالي
  Duration get currentPosition => _controller?.value.position ?? Duration.zero;

  /// الانتقال لوقت محدد
  void seekTo(Duration position) {
    _controller?.seekTo(position);
  }
}
