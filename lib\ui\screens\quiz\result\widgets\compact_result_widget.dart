import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';

/// ويدجت عرض النتائج المدمج والمتناسق
class CompactResultWidget extends StatelessWidget {
  final ResultData resultData;
  final ResultStateData resultState;
  final String userProfileUrl;

  const CompactResultWidget({
    super.key,
    required this.resultData,
    required this.resultState,
    required this.userProfileUrl,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutBack,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  Theme.of(context).colorScheme.surface.withOpacity(0.95),
                  Theme.of(context).colorScheme.surface.withOpacity(0.85),
                  Theme.of(context).colorScheme.surface.withOpacity(0.95),
                ]
              : [
                  Colors.white,
                  const Color(0xFFF8F9FA),
                  // Theme.of(context).primaryColor.withOpacity(0.08),
                  Colors.white,
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.6, 1.0],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Theme.of(context).primaryColor.withOpacity(0.2)
              : Theme.of(context).primaryColor.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).primaryColor.withOpacity(0.1)
                : Theme.of(context).primaryColor.withOpacity(0.15),
            blurRadius: 20,
            spreadRadius: 2,
            offset: const Offset(0, 8),
          ),
          if (Theme.of(context).brightness == Brightness.light)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 15,
              spreadRadius: 0,
              offset: const Offset(0, 4),
            ),
        ],
      ),
      child: Column(
        children: [
          // الرسالة الرئيسية
          _buildMainMessage(context),
          const SizedBox(height: 20),

          // التصميم الأفقي المدمج
          _buildHorizontalLayout(context),
          const SizedBox(height: 20),

          // تفاصيل النتائج المختصرة
          _buildCompactStats(context),
        ],
      ),
    );
  }

  /// بناء الرسالة الرئيسية
  Widget _buildMainMessage(BuildContext context) {
    return Column(
      children: [
        // أيقونة النتيجة
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: resultState.isWinner
                  ? [
                      const Color(0xFFFFD700),
                      const Color(0xFFFFA500),
                    ]
                  : [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withOpacity(0.7),
                    ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: resultState.isWinner
                    ? const Color(0xFFFFD700).withOpacity(0.4)
                    : Theme.of(context).primaryColor.withOpacity(0.4),
                blurRadius: 12,
                spreadRadius: 2,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            resultState.isWinner ? Icons.emoji_events : Icons.psychology,
            color: Colors.white,
            size: 30,
          ),
        ),
        const SizedBox(height: 12),

        // الرسالة الرئيسية
        Text(
          resultState.mainMessage,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeights.bold,
            color: Theme.of(context).colorScheme.onSurface,
            height: 1.3,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء التصميم الأفقي المدمج
  Widget _buildHorizontalLayout(BuildContext context) {
    return Row(
      children: [
        // صورة المستخدم
        _buildUserAvatar(context),
        const SizedBox(width: 20),

        // النسبة والوقت
        Expanded(
          child: _buildScoreAndTime(context),
        ),
      ],
    );
  }

  /// بناء صورة المستخدم المدمجة
  Widget _buildUserAvatar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  Theme.of(context).primaryColor.withOpacity(0.3),
                  Theme.of(context).primaryColor.withOpacity(0.1),
                ]
              : [
                  Theme.of(context).primaryColor.withOpacity(0.2),
                  Theme.of(context).primaryColor.withOpacity(0.05),
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Theme.of(context).primaryColor.withOpacity(0.4)
              : Theme.of(context).primaryColor.withOpacity(0.5),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).primaryColor.withOpacity(0.2)
                : Theme.of(context).primaryColor.withOpacity(0.25),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 4),
          ),
          if (Theme.of(context).brightness == Brightness.light)
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(3),
        child: QImage.circular(
          width: 70,
          height: 70,
          imageUrl: userProfileUrl,
        ),
      ),
    );
  }

  /// بناء النسبة والوقت
  Widget _buildScoreAndTime(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  Theme.of(context).primaryColor.withOpacity(0.1),
                  Theme.of(context).primaryColor.withOpacity(0.05),
                ]
              : [
                  const Color(0xFFF8F9FA),
                  Colors.white,
                ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Theme.of(context).primaryColor.withOpacity(0.2)
              : Theme.of(context).primaryColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: Theme.of(context).brightness == Brightness.light
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // النسبة المئوية
          Row(
            children: [
              Icon(
                Icons.percent,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'النسبة المئوية',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Theme.of(context).colorScheme.onSurface.withOpacity(0.7)
                      : const Color(0xFF6B7280),
                  fontWeight: FontWeights.medium,
                ),
              ),
              const Spacer(),
              Text(
                '${resultState.winPercentage.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeights.bold,
                  color: resultState.isWinner
                      ? Colors.green
                      : Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // الوقت المستغرق
          if (resultData.timeTakenToCompleteQuiz != null) ...[
            Row(
              children: [
                Icon(
                  Icons.timer,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'الوقت المستغرق',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.7),
                    fontWeight: FontWeights.medium,
                  ),
                ),
                const Spacer(),
                Text(
                  _formatTime(resultData.timeTakenToCompleteQuiz!),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeights.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// بناء الإحصائيات المختصرة
  Widget _buildCompactStats(BuildContext context) {
    return Row(
      children: [
        // الإجابات الصحيحة
        Expanded(
          child: _buildStatCard(
            context,
            icon: Icons.check_circle,
            label: 'صحيحة',
            value: '${resultState.correctAnswers}',
            color: Colors.green,
          ),
        ),
        const SizedBox(width: 12),

        // الإجابات الخاطئة
        Expanded(
          child: _buildStatCard(
            context,
            icon: Icons.cancel,
            label: 'خاطئة',
            value: '${resultState.totalQuestions - resultState.correctAnswers}',
            color: Colors.red,
          ),
        ),
        const SizedBox(width: 12),

        // إجمالي الأسئلة
        Expanded(
          child: _buildStatCard(
            context,
            icon: Icons.quiz,
            label: 'المجموع',
            value: '${resultState.totalQuestions}',
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: Theme.of(context).brightness == Brightness.dark
              ? [
                  color.withOpacity(0.1),
                  color.withOpacity(0.05),
                ]
              : [
                  color.withOpacity(0.08),
                  color.withOpacity(0.03),
                ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? color.withOpacity(0.3)
              : color.withOpacity(0.4),
          width: 1,
        ),
        boxShadow: Theme.of(context).brightness == Brightness.light
            ? [
                BoxShadow(
                  color: color.withOpacity(0.1),
                  blurRadius: 6,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeights.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).colorScheme.onSurface.withOpacity(0.7)
                  : const Color(0xFF6B7280),
              fontWeight: FontWeights.medium,
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق الوقت
  String _formatTime(double seconds) {
    final int minutes = (seconds / 60).floor();
    final int remainingSeconds = (seconds % 60).floor();

    if (minutes > 0) {
      return '${minutes}د ${remainingSeconds}ث';
    } else {
      return '${remainingSeconds}ث';
    }
  }
}
