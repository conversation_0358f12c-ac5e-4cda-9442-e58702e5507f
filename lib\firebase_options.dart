import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCxLD0k6mBB5_4K0aCBBIJZQxUHfCXJ9aI',
    appId: '1:695537688649:android:2ac1f938685bfccea89f98',
    messagingSenderId: '695537688649',
    projectId: 'quiz-49cd0',
    storageBucket: 'quiz-49cd0.firebasestorage.app',
    androidClientId: '695537688649-elghcgjm8rr363ivc6oc6ilklr7vt1om.apps.googleusercontent.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAw0tRR1KiJdep5usvn6BezxotXmbVxG6Q',
    appId: '1:695537688649:ios:4ffc255471cba40da89f98',
    messagingSenderId: '695537688649',
    projectId: 'quiz-49cd0',
    storageBucket: 'quiz-49cd0.firebasestorage.app',
    iosClientId: '695537688649-3aa5up0ukb3da1lc2bj4sjmigoc348lf.apps.googleusercontent.com',
    iosBundleId: 'com.mazen.flutterquiz',
  );
}