import 'dart:async';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/material.dart';
import 'package:flutterquiz/services/voice_chat_service.dart';
import 'package:iconsax/iconsax.dart';

/// ويدجت للتحكم في المحادثة الصوتية
class VoiceChatControls extends StatefulWidget {
  final String channelId;
  final int uid;
  final bool isEnabled;
  final bool showUserCount;
  final Function(bool)? onMicStatusChanged;

  const VoiceChatControls({
    super.key,
    required this.channelId,
    required this.uid,
    required this.isEnabled,
    this.showUserCount = true,
    this.onMicStatusChanged,
  });

  @override
  State<VoiceChatControls> createState() => _VoiceChatControlsState();
}

class _VoiceChatControlsState extends State<VoiceChatControls> with TickerProviderStateMixin {
  final VoiceChatService _voiceChatService = VoiceChatService();
  bool _isMuted = false;
  bool _isSpeakerOn = true;
  bool _isConnected = false;
  bool _isConnecting = false;
  final List<int> _connectedUsers = [];

  // للتحكم في حالة الاتصال
  late AnimationController _connectionAnimationController;
  late AnimationController _volumeAnimationController;
  StreamSubscription<int>? _userJoinedSubscription;
  StreamSubscription<int>? _userLeftSubscription;
  StreamSubscription<ConnectionStateType>? _connectionStateSubscription;

  // للتحكم في مستوى الصوت
  double _localVolume = 0.0;
  final Map<int, double> _remoteVolumes = {};
  StreamSubscription<List<AudioVolumeInfo>>? _volumeSubscription;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الرسوم المتحركة للاتصال
    _connectionAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    // إنشاء متحكم الرسوم المتحركة لمستوى الصوت
    _volumeAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    )..repeat(reverse: true);

    if (widget.isEnabled && widget.channelId.isNotEmpty) {
      _initVoiceChat();
    }
  }

  @override
  void dispose() {
    // إيقاف الرسوم المتحركة أولاً
    _connectionAnimationController.stop();
    _volumeAnimationController.stop();

    // التخلص من متحكمات الرسوم المتحركة
    _connectionAnimationController.dispose();
    _volumeAnimationController.dispose();

    // إلغاء جميع الاشتراكات بشكل آمن
    _userJoinedSubscription?.cancel();
    _userJoinedSubscription = null;

    _userLeftSubscription?.cancel();
    _userLeftSubscription = null;

    _connectionStateSubscription?.cancel();
    _connectionStateSubscription = null;

    _volumeSubscription?.cancel();
    _volumeSubscription = null;

    // مغادرة القناة إذا كان متصلاً
    if (_isConnected) {
      // استخدام Future.microtask لتجنب استدعاء async في dispose
      Future.microtask(() => _voiceChatService.leaveChannel());
    }

    // تنظيف البيانات
    _connectedUsers.clear();
    _remoteVolumes.clear();

    super.dispose();
  }

  // الاستماع إلى تغييرات مستوى الصوت
  void _listenToVolumeChanges() {
    _volumeSubscription = _voiceChatService.onAudioVolumeIndication.listen((speakers) {
      if (mounted) {
        setState(() {
          // تحديث مستوى الصوت المحلي
          final localSpeaker = speakers.firstWhere(
            (speaker) => speaker.uid == 0, // 0 يمثل المستخدم المحلي
            orElse: () => const AudioVolumeInfo(uid: 0, volume: 0, vad: 0),
          );

          _localVolume = localSpeaker.volume! / 255.0; // تحويل القيمة إلى نطاق 0-1

          // تحديث مستويات صوت المستخدمين البعيدين
          for (final speaker in speakers) {
            if (speaker.uid != 0) { // تجاهل المستخدم المحلي
              _remoteVolumes[speaker.uid!] = speaker.volume! / 255.0;
            }
          }
        });
      }
    });
  }

  Future<void> _initVoiceChat() async {
    setState(() {
      _isConnecting = true;
    });

    // إلغاء الاشتراكات السابقة إذا كانت موجودة
    _userJoinedSubscription?.cancel();
    _userLeftSubscription?.cancel();
    _connectionStateSubscription?.cancel();
    _volumeSubscription?.cancel();

    // طلب إذن الميكروفون أولاً مع عرض حوار للمستخدم
    final hasPermission = await _voiceChatService.showPermissionDialog(context);
    if (!hasPermission) {
      if (mounted) {
        setState(() {
          _isConnecting = false;
        });

        // عرض رسالة للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يمكن استخدام المحادثة الصوتية بدون إذن الميكروفون'),
            duration: Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    // تهيئة محرك Agora
    await _voiceChatService.initialize();

    // الاستماع إلى أحداث المستخدمين
    _userJoinedSubscription = _voiceChatService.onUserJoined.listen((uid) {
      if (mounted) {
        setState(() {
          if (!_connectedUsers.contains(uid)) {
            _connectedUsers.add(uid);
          }
        });
      }
    });

    _userLeftSubscription = _voiceChatService.onUserLeft.listen((uid) {
      if (mounted) {
        setState(() {
          _connectedUsers.remove(uid);
          _remoteVolumes.remove(uid); // إزالة مستوى صوت المستخدم المغادر
        });
      }
    });

    _connectionStateSubscription = _voiceChatService.onConnectionStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          _isConnected = _voiceChatService.isConnected;
          _isConnecting = false;

          // إعادة تعيين مستويات الصوت عند قطع الاتصال
          if (!_isConnected) {
            _localVolume = 0.0;
            _remoteVolumes.clear();
          }
        });
      }
    });

    // الاستماع إلى تغييرات مستوى الصوت
    _listenToVolumeChanges();

    // الانضمام إلى القناة
    final result = await _voiceChatService.joinChannel(
      widget.channelId,
      widget.uid,
    );

    if (mounted) {
      setState(() {
        _isConnected = result.success;
        _isConnecting = false;

        // إعادة تعيين مستويات الصوت إذا فشل الاتصال
        if (!result.success) {
          _localVolume = 0.0;
          _remoteVolumes.clear();

          // عرض رسالة خطأ إذا فشل الاتصال بسبب رفض الإذن
          if (result.errorCode == VoiceChatErrorCode.microphonePermissionDenied) {
            Future.delayed(Duration.zero, () {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(result.errorMessage ?? 'فشل الاتصال: تم رفض إذن الميكروفون'),
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            });
          }
        }
      });
    }
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
      _voiceChatService.muteLocalAudio(_isMuted);

      // استدعاء الدالة الخارجية لإعلام الأب بتغيير حالة الميكروفون
      widget.onMicStatusChanged?.call(_isMuted);
    });
  }

  void _toggleSpeaker() {
    setState(() {
      _isSpeakerOn = !_isSpeakerOn;
      _voiceChatService.setSpeakerphone(_isSpeakerOn);
    });
  }

  void _reconnect() {
    if (!_isConnecting) {
      _initVoiceChat();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isEnabled || widget.channelId.isEmpty) {
      return const SizedBox.shrink();
    }

    // تصميم جديد أكثر جمالًا للتحكم في المحادثة الصوتية
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.6),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مؤشر حالة الاتصال
          _buildCompactConnectionIndicator(),
          const SizedBox(width: 12),

          // عدد المستخدمين المحسن
          if (widget.showUserCount)
            Tooltip(
              message: 'عدد المستخدمين المتصلين',
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.purple.withOpacity(0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4.0,
                      spreadRadius: 1.0,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Icon(
                      Iconsax.people,
                      color: Colors.purple,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.purple.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${_connectedUsers.length + 1}',
                        style: const TextStyle(
                          color: Colors.purple,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          const SizedBox(width: 12),

          // مؤشر مستوى الصوت (يظهر فقط عند التحدث)
          if (!_isMuted && _isConnected && _localVolume > 0.1)
            _buildVoiceIndicator(),

          // زر كتم الصوت
          _buildCompactControlButton(
            icon: _isMuted ? Iconsax.microphone_slash : Iconsax.microphone,
            onTap: _toggleMute,
            isActive: !_isMuted,
            label: _isMuted ? 'إلغاء كتم' : 'كتم',
          ),
          const SizedBox(width: 12),

          // زر السماعة
          _buildCompactControlButton(
            icon: _isSpeakerOn ? Iconsax.volume_high : Iconsax.volume_slash,
            onTap: _toggleSpeaker,
            isActive: _isSpeakerOn,
            label: _isSpeakerOn ? 'السماعة' : 'كتم السماعة',
          ),

          // زر إعادة الاتصال
          if (!_isConnected && !_isConnecting) ...[
            const SizedBox(width: 12),
            _buildCompactControlButton(
              icon: Iconsax.refresh,
              onTap: _reconnect,
              isActive: true,
              label: 'إعادة الاتصال',
            ),
          ],
        ],
      ),
    );
  }

  // مؤشر حالة الاتصال المصغر المحسن
  Widget _buildCompactConnectionIndicator() {
    Color indicatorColor;
    IconData iconData;
    double size = 28;
    String statusText = '';

    if (_isConnecting) {
      // حالة جاري الاتصال
      indicatorColor = Colors.amber;
      iconData = Iconsax.wifi;
      statusText = 'جاري الاتصال';

      return AnimatedBuilder(
        animation: _connectionAnimationController,
        builder: (context, child) {
          return Tooltip(
            message: statusText,
            child: Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: indicatorColor.withOpacity(0.1),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: indicatorColor.withOpacity(0.2),
                    blurRadius: 4.0,
                    spreadRadius: 1.0,
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // دوائر متحركة
                  ...List.generate(2, (index) {
                    final scale = 0.7 + (index * 0.3);
                    final opacity = 0.7 - (index * 0.3) * _connectionAnimationController.value;

                    return Positioned.fill(
                      child: Opacity(
                        opacity: opacity,
                        child: Transform.scale(
                          scale: scale,
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: indicatorColor.withOpacity(0.5),
                                width: 1,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  }),

                  // أيقونة الحالة
                  Opacity(
                    opacity: 0.5 + (_connectionAnimationController.value * 0.5),
                    child: Icon(
                      iconData,
                      color: indicatorColor,
                      size: 14,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } else if (_isConnected) {
      // حالة متصل
      indicatorColor = Colors.green;
      iconData = Iconsax.microphone_2;
      statusText = 'متصل';
    } else {
      // حالة غير متصل
      indicatorColor = Colors.red;
      iconData = Iconsax.wifi_square;
      statusText = 'غير متصل';
    }

    return Tooltip(
      message: statusText,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: indicatorColor.withOpacity(0.1),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: indicatorColor.withOpacity(0.2),
              blurRadius: 4.0,
              spreadRadius: 1.0,
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // حلقة خارجية
            Container(
              width: size * 0.8,
              height: size * 0.8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: indicatorColor.withOpacity(0.5),
                  width: 1.5,
                ),
              ),
            ),

            // أيقونة الحالة
            Icon(
              iconData,
              color: indicatorColor,
              size: 14,
            ),
          ],
        ),
      ),
    );
  }

  // زر التحكم المصغر المحسن
  Widget _buildCompactControlButton({
    required IconData icon,
    required VoidCallback onTap,
    required bool isActive,
    String? label,
  }) {
    // تحديد لون الزر بناءً على حالته
    final Color buttonColor;
    if (icon == Iconsax.microphone || icon == Iconsax.microphone_slash) {
      buttonColor = isActive ? Colors.green : Colors.red;
    } else if (icon == Iconsax.volume_high || icon == Iconsax.volume_slash) {
      buttonColor = isActive ? Colors.blue : Colors.grey;
    } else {
      buttonColor = isActive ? Colors.green : Colors.red;
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: buttonColor.withOpacity(0.2),
          shape: BoxShape.circle,
          border: Border.all(
            color: buttonColor.withOpacity(0.5),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: buttonColor.withOpacity(0.1),
              blurRadius: 4.0,
              spreadRadius: 1.0,
            ),
          ],
        ),
        child: Icon(
          icon,
          color: buttonColor,
          size: 18,
        ),
      ),
    );
  }

  // مؤشر مستوى الصوت المتحرك المحسن
  Widget _buildVoiceIndicator() {
    if (_isMuted || !_isConnected) return const SizedBox.shrink();

    // تحديد لون المؤشر بناءً على مستوى الصوت
    Color indicatorColor;
    if (_localVolume > 0.7) {
      indicatorColor = Colors.red;
    } else if (_localVolume > 0.4) {
      indicatorColor = Colors.orange;
    } else {
      indicatorColor = Colors.green;
    }

    return AnimatedBuilder(
      animation: _volumeAnimationController,
      builder: (context, child) {
        return Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: indicatorColor.withOpacity(0.1),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: indicatorColor.withOpacity(0.2 * _localVolume),
                blurRadius: 4.0 * _localVolume,
                spreadRadius: 1.0 * _localVolume,
              ),
            ],
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // الدوائر المتحركة لمستوى الصوت
              ...List.generate(3, (index) {
                final scale = 0.6 + (index * 0.2);
                final opacity = _localVolume > (index * 0.3) ?
                    (1.0 - (index * 0.2)) * _volumeAnimationController.value :
                    0.0;

                return Positioned.fill(
                  child: Opacity(
                    opacity: opacity,
                    child: Transform.scale(
                      scale: scale,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: indicatorColor.withOpacity(0.5),
                            width: 1.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }),

              // أشرطة مستوى الصوت
              SizedBox(
                width: 16,
                height: 16,
                child: CustomPaint(
                  painter: VoiceLevelPainter(
                    level: _localVolume,
                    color: indicatorColor,
                    barCount: 4,
                    barWidth: 2,
                    spacing: 1,
                  ),
                ),
              ),

              // أيقونة الميكروفون
              Icon(
                Iconsax.microphone,
                color: indicatorColor,
                size: 14,
              ),
            ],
          ),
        );
      },
    );
  }
}

/// رسام مستوى الصوت المخصص
/// يرسم أشرطة مستوى الصوت
class VoiceLevelPainter extends CustomPainter {
  final double level;
  final Color color;
  final int barCount;
  final double barWidth;
  final double spacing;

  VoiceLevelPainter({
    required this.level,
    required this.color,
    this.barCount = 4,
    this.barWidth = 2,
    this.spacing = 1,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final totalWidth = (barWidth + spacing) * barCount - spacing;
    final startX = (size.width - totalWidth) / 2;
    final maxHeight = size.height * 0.8;

    for (int i = 0; i < barCount; i++) {
      final barHeight = maxHeight * ((i + 1) / barCount) * level;
      final barX = startX + i * (barWidth + spacing);
      final barY = (size.height - barHeight) / 2;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(barX, barY, barWidth, barHeight),
          const Radius.circular(1),
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(VoiceLevelPainter oldDelegate) {
    return oldDelegate.level != level || oldDelegate.color != color;
  }
}