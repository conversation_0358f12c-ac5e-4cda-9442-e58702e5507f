import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/styles/colors.dart';
import 'package:google_fonts/google_fonts.dart';

// ========================================
// 🎨 تعداد أنواع الثيمات
// ========================================
enum AppTheme { light, dark }

// ========================================
// 🎨 بيانات الثيمات المحسنة
// ========================================
final appThemeData = {
  // 🌞 الثيم الفاتح المحسن
  AppTheme.light: ThemeData(
    brightness: Brightness.light,
    useMaterial3: true,

    // الألوان الأساسية
    canvasColor: klCanvasColor,
    primaryColor: klPrimaryColor,
    scaffoldBackgroundColor: klPageBackgroundColor,
    cardColor: klCardColor,

    // الخطوط
    fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
    primaryTextTheme: GoogleFonts.ibmPlexSansArabicTextTheme(),
    textTheme: _lightTextTheme,

    // الثيمات الفرعية
    dialogTheme: _lightDialogTheme,
    dividerTheme: _lightDividerTheme,
    textButtonTheme: _lightTextButtonTheme,
    elevatedButtonTheme: _lightElevatedButtonTheme,
    outlinedButtonTheme: _lightOutlinedButtonTheme,
    tabBarTheme: _lightTabBarTheme,
    appBarTheme: _lightAppBarTheme,
    cardTheme: _lightCardTheme,
    
    // ألوان إضافية للـ UI
    dialogBackgroundColor: klSurfaceColor,
    textSelectionTheme: TextSelectionThemeData(
      selectionColor: klPrimaryColor,
      cursorColor: klPrimaryColor,
      selectionHandleColor: klPrimaryColor,
    ),

    // التأثيرات
    shadowColor: klPrimaryColor.withOpacity(0.15),
    highlightColor: Colors.transparent,
    splashColor: klPrimaryColor.withOpacity(0.1),
    focusColor: klPrimaryColor.withOpacity(0.12),
    hoverColor: klPrimaryColor.withOpacity(0.08),

    // الراديو والتحديد
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return klPrimaryColor;
        }
        return klSecondaryTextColor;
      }),
      overlayColor: WidgetStateProperty.all(klPrimaryColor.withOpacity(0.1)),
    ),
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.all(klPrimaryColor),
      overlayColor: WidgetStateProperty.all(klPrimaryColor.withOpacity(0.1)),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.all(klPrimaryColor),
      trackColor: WidgetStateProperty.all(klPrimaryColor.withOpacity(0.3)),
      overlayColor: WidgetStateProperty.all(klPrimaryColor.withOpacity(0.1)),
    ),

    // نظام الألوان الموحد
    colorScheme: ColorScheme.light(
      primary: klPrimaryColor,
      onPrimary: Colors.white,
      secondary: klSecondaryColor,
      onSecondary: Colors.white,
      tertiary: klAccentColor,
      onTertiary: Colors.white,
      surface: klSurfaceColor,
      onSurface: klPrimaryTextColor,
      background: klBackgroundColor,
      onBackground: klPrimaryTextColor,
      error: kErrorColor,
      onError: Colors.white,
      outline: klBorderColor,
      surfaceTint: Colors.transparent,
      surfaceVariant: klCardColor,
      onSurfaceVariant: klSecondaryTextColor,
    ),

    cupertinoOverrideTheme: _cupertinoOverrideTheme,
  ),

  // 🌙 الثيم الداكن المحسن
  AppTheme.dark: ThemeData(
    brightness: Brightness.dark,
    useMaterial3: true,

    // الألوان الأساسية
    canvasColor: kdCanvasColor,
    primaryColor: kdPrimaryColor,
    scaffoldBackgroundColor: kdPageBackgroundColor,
    cardColor: kdCardColor,

    // الخطوط
    fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
    primaryTextTheme: GoogleFonts.ibmPlexSansArabicTextTheme(),
    textTheme: _darkTextTheme,

    // الثيمات الفرعية
    dialogTheme: _darkDialogTheme,
    dividerTheme: _darkDividerTheme,
    textButtonTheme: _darkTextButtonTheme,
    elevatedButtonTheme: _darkElevatedButtonTheme,
    outlinedButtonTheme: _darkOutlinedButtonTheme,
    tabBarTheme: _darkTabBarTheme,
    appBarTheme: _darkAppBarTheme,
    cardTheme: _darkCardTheme,
    
    // ألوان إضافية للـ UI
    dialogBackgroundColor: kdSurfaceColor,
    textSelectionTheme: TextSelectionThemeData(
      selectionColor: kdPrimaryColor,
      cursorColor: kdPrimaryColor,
      selectionHandleColor: kdPrimaryColor,
    ),

    // التأثيرات
    shadowColor: kdPrimaryColor.withOpacity(0.3),
    highlightColor: Colors.transparent,
    splashColor: kdPrimaryColor.withOpacity(0.2),
    focusColor: kdPrimaryColor.withOpacity(0.15),
    hoverColor: kdPrimaryColor.withOpacity(0.1),

    // الراديو والتحديد
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return kdPrimaryColor;
        }
        return kdSecondaryTextColor;
      }),
      overlayColor: WidgetStateProperty.all(kdPrimaryColor.withOpacity(0.2)),
    ),
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.all(kdPrimaryColor),
      overlayColor: WidgetStateProperty.all(kdPrimaryColor.withOpacity(0.2)),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.all(kdPrimaryColor),
      trackColor: WidgetStateProperty.all(kdPrimaryColor.withOpacity(0.4)),
      overlayColor: WidgetStateProperty.all(kdPrimaryColor.withOpacity(0.2)),
    ),

    // نظام الألوان الموحد
    colorScheme: ColorScheme.dark(
      primary: kdPrimaryColor,
      onPrimary: Colors.white,
      secondary: kdSecondaryColor,
      onSecondary: Colors.white,
      tertiary: kdAccentColor,
      onTertiary: kdPrimaryTextColor,
      surface: kdSurfaceColor,
      onSurface: kdPrimaryTextColor,
      background: kdBackgroundColor,
      onBackground: kdPrimaryTextColor,
      error: kErrorColor,
      onError: Colors.white,
      outline: kdBorderColor,
      surfaceTint: Colors.transparent,
      surfaceVariant: kdCardColor,
      onSurfaceVariant: kdSecondaryTextColor,
    ),

    cupertinoOverrideTheme: _cupertinoOverrideTheme,
  ),
};

// ========================================
// 🎨 ثيمات النصوص
// ========================================

final _lightTextTheme = GoogleFonts.ibmPlexSansArabicTextTheme().copyWith(
  displayLarge: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: klPrimaryTextColor,
    ),
  ),
  headlineLarge: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: klPrimaryTextColor,
    ),
  ),
  bodyLarge: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 16,
      color: klPrimaryTextColor,
    ),
  ),
  bodyMedium: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 14,
      color: klSecondaryTextColor,
    ),
  ),
);

final _darkTextTheme = GoogleFonts.ibmPlexSansArabicTextTheme().copyWith(
  displayLarge: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: kdPrimaryTextColor,
    ),
  ),
  headlineLarge: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: kdPrimaryTextColor,
    ),
  ),
  bodyLarge: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 16,
      color: kdPrimaryTextColor,
    ),
  ),
  bodyMedium: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 14,
      color: kdSecondaryTextColor,
    ),
  ),
);

// ========================================
// 🎨 ثيمات الأزرار
// ========================================

final _lightTextButtonTheme = TextButtonThemeData(
  style: TextButton.styleFrom(
    foregroundColor: klPrimaryColor,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  ),
);

final _darkTextButtonTheme = TextButtonThemeData(
  style: TextButton.styleFrom(
    foregroundColor: kdPrimaryColor,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  ),
);

final _lightElevatedButtonTheme = ElevatedButtonThemeData(
  style: ElevatedButton.styleFrom(
    backgroundColor: klPrimaryColor,
    foregroundColor: Colors.white,
    elevation: 4,
    shadowColor: klPrimaryColor.withOpacity(0.3),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
  ),
);

final _darkElevatedButtonTheme = ElevatedButtonThemeData(
  style: ElevatedButton.styleFrom(
    backgroundColor: kdPrimaryColor,
    foregroundColor: Colors.white,
    elevation: 6,
    shadowColor: kdPrimaryColor.withOpacity(0.4),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
  ),
);

final _lightOutlinedButtonTheme = OutlinedButtonThemeData(
  style: OutlinedButton.styleFrom(
    foregroundColor: klPrimaryColor,
    side: BorderSide(color: klPrimaryColor, width: 1.5),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
  ),
);

final _darkOutlinedButtonTheme = OutlinedButtonThemeData(
  style: OutlinedButton.styleFrom(
    foregroundColor: kdPrimaryColor,
    side: BorderSide(color: kdPrimaryColor, width: 1.5),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
  ),
);

// ========================================
// 🎨 ثيمات أخرى
// ========================================

final _lightDividerTheme = DividerThemeData(
  color: klDividerColor,
  thickness: 1,
  space: 1,
);

final _darkDividerTheme = DividerThemeData(
  color: kdDividerColor,
  thickness: 1,
  space: 1,
);

final _lightDialogTheme = DialogThemeData(
  alignment: Alignment.center,
  backgroundColor: klSurfaceColor,
  surfaceTintColor: Colors.transparent,
  shadowColor: klPrimaryColor.withOpacity(0.1),
  elevation: 8,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(20),
  ),
  titleTextStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: klPrimaryTextColor,
    ),
  ),
  contentTextStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 16,
      color: klSecondaryTextColor,
    ),
  ),
);

final _darkDialogTheme = DialogThemeData(
  alignment: Alignment.center,
  backgroundColor: kdSurfaceColor,
  surfaceTintColor: Colors.transparent,
  shadowColor: kdPrimaryColor.withOpacity(0.2),
  elevation: 12,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(20),
  ),
  titleTextStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: kdPrimaryTextColor,
    ),
  ),
  contentTextStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 16,
      color: kdSecondaryTextColor,
    ),
  ),
);

final _lightTabBarTheme = TabBarThemeData(
  tabAlignment: TabAlignment.center,
  overlayColor: const WidgetStatePropertyAll(Colors.transparent),
  dividerHeight: 0,
  labelColor: Colors.white,
  unselectedLabelColor: klSecondaryTextColor.withOpacity(0.85),
  labelStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 14,
    ),
  ),
  unselectedLabelStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontWeight: FontWeight.normal,
      fontSize: 14,
    ),
  ),
  indicatorSize: TabBarIndicatorSize.tab,
  indicator: BoxDecoration(
    borderRadius: BorderRadius.circular(25),
    gradient: LinearGradient(
      colors: [klPrimaryColor, klSecondaryColor],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  ),
);

final _darkTabBarTheme = TabBarThemeData(
  tabAlignment: TabAlignment.center,
  overlayColor: const WidgetStatePropertyAll(Colors.transparent),
  dividerHeight: 0,
  labelColor: Colors.white,
  unselectedLabelColor: kdSecondaryTextColor.withOpacity(0.85),
  labelStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 14,
    ),
  ),
  unselectedLabelStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontWeight: FontWeight.normal,
      fontSize: 14,
    ),
  ),
  indicatorSize: TabBarIndicatorSize.tab,
  indicator: BoxDecoration(
    borderRadius: BorderRadius.circular(25),
    gradient: LinearGradient(
      colors: [kdPrimaryColor, kdSecondaryColor],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  ),
);

final _lightAppBarTheme = AppBarTheme(
  backgroundColor: klSurfaceColor,
  foregroundColor: klPrimaryTextColor,
  elevation: 0,
  shadowColor: Colors.transparent,
  surfaceTintColor: Colors.transparent,
  centerTitle: true,
  titleTextStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: klPrimaryTextColor,
    ),
  ),
  iconTheme: const IconThemeData(
    color: klPrimaryTextColor,
    size: 24,
  ),
);

final _darkAppBarTheme = AppBarTheme(
  backgroundColor: kdSurfaceColor,
  foregroundColor: kdPrimaryTextColor,
  elevation: 0,
  shadowColor: Colors.transparent,
  surfaceTintColor: Colors.transparent,
  centerTitle: true,
  titleTextStyle: GoogleFonts.ibmPlexSansArabic(
    textStyle: const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: kdPrimaryTextColor,
    ),
  ),
  iconTheme: const IconThemeData(
    color: kdPrimaryTextColor,
    size: 24,
  ),
);

final _lightCardTheme = CardThemeData(
  color: klCardColor,
  shadowColor: klPrimaryColor.withOpacity(0.1),
  elevation: 4,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(16),
  ),
  margin: const EdgeInsets.all(8),
);

final _darkCardTheme = CardThemeData(
  color: kdCardColor,
  shadowColor: kdPrimaryColor.withOpacity(0.2),
  elevation: 6,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(16),
  ),
  margin: const EdgeInsets.all(8),
);

final _cupertinoOverrideTheme = NoDefaultCupertinoThemeData(
  textTheme: CupertinoTextThemeData(
    textStyle: GoogleFonts.ibmPlexSansArabic(),
  ),
);
