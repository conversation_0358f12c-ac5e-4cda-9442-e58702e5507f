class SectionModel {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final String categoryId;
  final int order;

  SectionModel({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.categoryId,
    required this.order,
  });

  factory SectionModel.fromMap(Map<String, dynamic> map, String documentId) {
    return SectionModel(
      id: documentId,
      title: map['title'] as String? ?? '',
      description: map['description'] as String? ?? '',
      imageUrl: map['image_url'] as String? ?? '',
      categoryId: map['category_id'] as String? ?? '',
      order: (map['order'] as num?)?.toInt() ?? 0,
    );
  }
}
