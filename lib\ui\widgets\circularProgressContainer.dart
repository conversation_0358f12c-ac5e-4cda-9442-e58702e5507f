import 'package:flutter/material.dart';

class CircularProgressContainer extends StatelessWidget {
  const CircularProgressContainer({
    super.key,
    this.whiteLoader = false,
    this.size = 40,
  });

  final bool whiteLoader;
  final double size;

  @override
  Widget build(BuildContext context) {
    final color = whiteLoader ? Colors.white : Theme.of(context).primaryColor;

    return SizedBox(
      width: size,
      height: size,
      child: Padding(
        padding: const EdgeInsets.all(3),
        child: CircularProgressIndicator(
          strokeWidth: 4, // حجم الحد الخارجي
          valueColor: AlwaysStoppedAnimation<Color>(color), // لون الدوران
        ),
      ),
    );
  }
}
