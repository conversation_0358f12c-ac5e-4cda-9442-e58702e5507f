import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/constants/assets_constants.dart';

/// ويدجت عرض نتائج المعركة
class BattleResultWidget extends StatelessWidget {
  final ResultData resultData;
  final ResultStateData resultState;

  const BattleResultWidget({
    super.key,
    required this.resultData,
    required this.resultState,
  });

  @override
  Widget build(BuildContext context) {
    final currentUserId = context.read<UserDetailsCubit>().userId();
    final user1 = resultData.battleRoom!.user1!;
    final user2 = resultData.battleRoom!.user2!;

    final currentUser = user1.uid == currentUserId ? user1 : user2;
    final opponent = user1.uid == currentUserId ? user2 : user1;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.surface.withOpacity(0.1),
            Theme.of(context).colorScheme.surface.withOpacity(0.05),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // عنوان المعركة
          _buildBattleTitle(context),
          const SizedBox(height: 20),

          // نتائج المعركة
          _buildBattleResults(context, currentUser, opponent),
          const SizedBox(height: 20),

          // تفاصيل إضافية
          _buildBattleDetails(context),
        ],
      ),
    );
  }

  /// بناء عنوان المعركة
  Widget _buildBattleTitle(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.2),
            Theme.of(context).primaryColor.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.sports_esports,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            resultState.isWinner ? 'فوز!' : 'معركة منتهية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeights.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء نتائج المعركة
  Widget _buildBattleResults(
      BuildContext context, dynamic currentUser, dynamic opponent) {
    return Row(
      children: [
        // اللاعب الحالي
        Expanded(
          child: _buildPlayerCard(
            context,
            name: currentUser.name,
            profileUrl: currentUser.profileUrl,
            points: currentUser.points,
            isWinner: resultState.isWinner,
            isCurrentUser: true,
          ),
        ),

        // أيقونة VS
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              SvgPicture.asset(
                Assets.versus,
                width: 40,
                height: 40,
                colorFilter: ColorFilter.mode(
                  Theme.of(context).primaryColor,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'VS',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeights.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        ),

        // الخصم
        Expanded(
          child: _buildPlayerCard(
            context,
            name: opponent.name,
            profileUrl: opponent.profileUrl,
            points: opponent.points,
            isWinner: !resultState.isWinner,
            isCurrentUser: false,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة اللاعب
  Widget _buildPlayerCard(
    BuildContext context, {
    required String name,
    required String profileUrl,
    required int points,
    required bool isWinner,
    required bool isCurrentUser,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isWinner
            ? Colors.green.withOpacity(0.1)
            : Theme.of(context).colorScheme.surface.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: isWinner
              ? Colors.green.withOpacity(0.3)
              : Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: isCurrentUser ? 2 : 1,
        ),
      ),
      child: Column(
        children: [
          // صورة اللاعب مع تاج الفوز
          Stack(
            alignment: Alignment.center,
            children: [
              QImage.circular(
                width: 60,
                height: 60,
                imageUrl: profileUrl,
              ),
              if (isWinner)
                Positioned(
                  top: -5,
                  child: Icon(
                    Icons.emoji_events,
                    color: Colors.amber,
                    size: 24,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),

          // اسم اللاعب
          Text(
            name,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeights.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),

          // النقاط
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isWinner
                  ? Colors.green.withOpacity(0.2)
                  : Theme.of(context).colorScheme.surface.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              'النقاط: $points',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeights.bold,
                color: isWinner
                    ? Colors.green
                    : Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),

          // مؤشر اللاعب الحالي
          if (isCurrentUser) ...[
            const SizedBox(height: 4),
            Text(
              'أنت',
              style: TextStyle(
                fontSize: 10,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeights.medium,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء تفاصيل المعركة
  Widget _buildBattleDetails(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // نوع المعركة
          _buildDetailRow(
            context,
            icon: Icons.category,
            label: 'نوع المعركة',
            value: resultData.playWithBot ? 'ضد الروبوت' : 'ضد لاعب',
          ),

          // رسوم الدخول
          if (resultData.battleRoom?.entryFee != null) ...[
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              icon: Icons.monetization_on,
              label: 'رسوم الدخول',
              value: '${resultData.battleRoom!.entryFee}',
            ),
          ],

          // العملات المكتسبة (معلق حالياً للنموذج الاشتراكي)
          // TODO: يمكن إعادة تفعيل هذا القسم عند الحاجة لنظام العملات
          /*
          if (resultState.earnedCoins > 0) ...[
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              icon: Icons.stars,
              label: 'العملات المكتسبة',
              value: '${resultState.earnedCoins}',
            ),
          ],
          */
        ],
      ),
    );
  }

  /// بناء صف تفصيل
  Widget _buildDetailRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeights.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }
}
