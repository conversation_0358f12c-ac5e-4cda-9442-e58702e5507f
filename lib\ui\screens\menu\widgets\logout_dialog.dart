import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/audioQuestionBookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';

void showLogoutDialog(BuildContext context) {
  showDialog<void>(
    context: context,
    builder: (_) {
      final size = MediaQuery.of(context).size;
      final colorScheme = Theme.of(context).colorScheme;
      final primaryColor = Theme.of(context).primaryColor;

      return Dialog(
        insetPadding: EdgeInsets.symmetric(
          horizontal: size.width * UiUtils.hzMarginPct,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        elevation: 8,
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: size.height * UiUtils.vtMarginPct,
            horizontal: size.width * UiUtils.hzMarginPct,
          ),
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(
                  Assets.logoutAccount,
                  height: 64,
                  width: 64,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                context.tr(logoutLbl)!,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeights.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                context.tr(logoutDialogLbl)!,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  height: 1.5,
                  color: colorScheme.onSurface.withOpacity(0.8),
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<BadgesCubit>().updateState(BadgesInitial());
                  context.read<BookmarkCubit>().updateState(BookmarkInitial());

                  context
                      .read<AudioQuestionBookmarkCubit>()
                      .updateState(AudioQuestionBookmarkInitial());
                  context.read<AuthCubit>().signOut();
                  Navigator.of(context).pushReplacementNamed(Routes.login);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: Text(
                  context.tr('yesLogoutLbl')!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeights.semiBold,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: Navigator.of(context).pop,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 12,
                  ),
                ),
                child: Text(
                  context.tr('stayLoggedLbl')!,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeights.semiBold,
                    color: primaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}
