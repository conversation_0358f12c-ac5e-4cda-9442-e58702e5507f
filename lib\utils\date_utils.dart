import 'package:intl/intl.dart';

class CustomDateUtils {
  static String formatDateTime(String dateTime) {
    try {
      // تحقق من صحة التاريخ
      if (dateTime.isEmpty) return '';
      
      // محاولة تحليل التاريخ بعدة تنسيقات مختلفة للتأكد من العمل مع جميع الصيغ
      DateTime? parsedDate;
      final formats = [
        'yyyy-MM-dd HH:mm:ss',
        'yyyy-MM-dd',
        'dd-MMM',
        'dd-MMM-yyyy',
        'MMM dd',
        'yyyy/MM/dd',
      ];
      
      for (final format in formats) {
        try {
          parsedDate = DateFormat(format).parse(dateTime);
          break;
        } catch (_) {
          // استمر في المحاولة مع التنسيق التالي
        }
      }
      
      // إذا لم ينجح أي تنسيق، حاول تحليل التاريخ بطريقة عامة
      parsedDate ??= DateTime.parse(dateTime);
      
      // تنسيق التاريخ بشكل عربي
      final arabicFormat = DateFormat('d MMM yyyy', 'ar');
      return arabicFormat.format(parsedDate);
      
    } catch (e) {
      print('Error parsing date: $dateTime, Error: $e');
      return dateTime; // إعادة نفس القيمة في حالة الفشل
    }
  }
}
