import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutterquiz/ui/styles/theme/themeCubit.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/utils/constants/assets_constants.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:flutterquiz/ui/styles/theme/themeCubit.dart' as theme_cubit;

void showThemeSelectorSheet(BuildContext context) {
  showModalBottomSheet<void>(
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: UiUtils.bottomSheetTopRadius,
    ),
    builder: (_) => const ThemeSelectorSheet(),
  );
}

class ThemeSelectorSheet extends StatelessWidget {
  const ThemeSelectorSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const _ThemeSelectorWidget();
  }
}

class _ThemeSelectorWidget extends StatelessWidget {
  const _ThemeSelectorWidget();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: UiUtils.bottomSheetTopRadius,
      ),
      padding: EdgeInsets.only(top: size.height * .02),
      child: BlocBuilder<ThemeCubit, ThemeState>(
        bloc: context.read<ThemeCubit>(),
        builder: (context, state) {
          theme_cubit.ThemeMode currThemeMode = state.themeMode;
          final colorScheme = Theme.of(context).colorScheme;

          return Padding(
            padding: EdgeInsets.symmetric(
              horizontal: size.width * UiUtils.hzMarginPct,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Align(
                  child: Text(
                    context.tr('theme')!,
                    style: TextStyle(
                      fontWeight: FontWeights.bold,
                      fontSize: 18,
                      color: colorScheme.onTertiary,
                    ),
                  ),
                ),
                Divider(
                  color: colorScheme.onTertiary.withOpacity(0.2),
                  thickness: 1,
                ),
                SizedBox(height: size.height * 0.02),

                // الثيم الفاتح
                Container(
                  decoration: BoxDecoration(
                    color: currThemeMode == theme_cubit.ThemeMode.light
                        ? Theme.of(context).primaryColor
                        : colorScheme.onTertiary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: RadioListTile<theme_cubit.ThemeMode>(
                    toggleable: true,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    tileColor: colorScheme.onTertiary.withOpacity(0.2),
                    value: theme_cubit.ThemeMode.light,
                    groupValue: currThemeMode,
                    activeColor: Colors.white,
                    title: Text(
                      context.tr('lightTheme')!,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                        color: currThemeMode == theme_cubit.ThemeMode.light
                            ? Colors.white
                            : colorScheme.onTertiary,
                      ),
                    ),
                    secondary: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: currThemeMode == theme_cubit.ThemeMode.light
                              ? Colors.white
                              : colorScheme.onTertiary.withOpacity(0.2),
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.all(2),
                      child: SvgPicture.asset(
                        Assets.day,
                        width: 76,
                        height: 28,
                      ),
                    ),
                    controlAffinity: ListTileControlAffinity.leading,
                    onChanged: (v) {
                      context.read<ThemeCubit>().changeTheme(v!);
                    },
                  ),
                ),
                SizedBox(height: size.height * 0.02),
                // الثيم الداكن
                Container(
                  decoration: BoxDecoration(
                    color: currThemeMode == theme_cubit.ThemeMode.dark
                        ? Theme.of(context).primaryColor
                        : colorScheme.onTertiary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: RadioListTile<theme_cubit.ThemeMode>(
                    toggleable: true,
                    value: theme_cubit.ThemeMode.dark,
                    groupValue: currThemeMode,
                    activeColor: Colors.white,
                    title: Text(
                      context.tr('darkTheme')!,
                      style: TextStyle(
                        fontWeight: FontWeights.medium,
                        fontSize: 18,
                        color: currThemeMode == theme_cubit.ThemeMode.dark
                            ? Colors.white
                            : colorScheme.onTertiary,
                      ),
                    ),
                    secondary: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: currThemeMode == theme_cubit.ThemeMode.dark
                              ? Colors.white
                              : colorScheme.onTertiary.withOpacity(0.2),
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.all(2),
                      child: SvgPicture.asset(
                        Assets.night,
                        width: 76,
                        height: 28,
                      ),
                    ),
                    controlAffinity: ListTileControlAffinity.leading,
                    onChanged: (v) {
                      context.read<ThemeCubit>().changeTheme(v!);
                    },
                  ),
                ),
                SizedBox(height: size.height * 0.02),
                // الثيم التلقائي (حسب النظام)
                Container(
                  decoration: BoxDecoration(
                    color: currThemeMode == theme_cubit.ThemeMode.system
                        ? Theme.of(context).primaryColor
                        : colorScheme.onTertiary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: RadioListTile<theme_cubit.ThemeMode>(
                    toggleable: true,
                    value: theme_cubit.ThemeMode.system,
                    groupValue: currThemeMode,
                    activeColor: Colors.white,
                    title: Text(
                      context.tr('systemTheme') ?? 'تلقائي',
                      style: TextStyle(
                        fontWeight: FontWeights.medium,
                        fontSize: 18,
                        color: currThemeMode == theme_cubit.ThemeMode.system
                            ? Colors.white
                            : colorScheme.onTertiary,
                      ),
                    ),
                    secondary: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: currThemeMode == theme_cubit.ThemeMode.system
                              ? Colors.white
                              : colorScheme.onTertiary.withOpacity(0.2),
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.all(2),
                      child: Icon(
                        Icons.brightness_auto,
                        color: currThemeMode == theme_cubit.ThemeMode.system
                            ? Colors.white
                            : colorScheme.onTertiary,
                        size: 28,
                      ),
                    ),
                    controlAffinity: ListTileControlAffinity.leading,
                    onChanged: (v) {
                      context.read<ThemeCubit>().changeTheme(v!);
                    },
                  ),
                ),

                ///
                const Spacer(),
                CustomRoundedButton(
                  onTap: Navigator.of(context).pop,
                  widthPercentage: 1,
                  backgroundColor: Theme.of(context).primaryColor,
                  buttonTitle: context.tr('save'),
                  radius: 8,
                  showBorder: false,
                  height: 45,
                ),
                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }
}
