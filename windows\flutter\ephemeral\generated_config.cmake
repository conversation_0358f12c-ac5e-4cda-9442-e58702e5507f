# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\apps\\flutter_windows_3.16.6-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\ALL_about_quiz\\LastMogtahed\\lastmogtahed" PROJECT_DIR)

set(FLUTTER_VERSION "1.1.63+88" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 63 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 88 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\apps\\flutter_windows_3.16.6-stable\\flutter"
  "PROJECT_DIR=D:\\ALL_about_quiz\\LastMogtahed\\lastmogtahed"
  "FLUTTER_ROOT=D:\\apps\\flutter_windows_3.16.6-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\ALL_about_quiz\\LastMogtahed\\lastmogtahed\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\ALL_about_quiz\\LastMogtahed\\lastmogtahed"
  "FLUTTER_TARGET=D:\\ALL_about_quiz\\LastMogtahed\\lastmogtahed\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YjI1MzA1YTg4Mw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTQyNWU1ZTllYw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\ALL_about_quiz\\LastMogtahed\\lastmogtahed\\.dart_tool\\package_config.json"
)
