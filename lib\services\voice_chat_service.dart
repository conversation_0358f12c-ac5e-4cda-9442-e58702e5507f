import 'dart:async';
import 'dart:io';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutterquiz/utils/secure_config.dart';

/// رموز أخطاء المحادثة الصوتية
enum VoiceChatErrorCode {
  invalidChannelId,
  initializationFailed,
  microphonePermissionDenied,
  joinChannelFailed,
  connectionFailed,
}

/// نتيجة الانضمام إلى قناة صوتية
class JoinChannelResult {
  final bool success;
  final VoiceChatErrorCode? errorCode;
  final String? errorMessage;
  final PermissionStatus? permissionStatus;

  JoinChannelResult({
    required this.success,
    this.errorCode,
    this.errorMessage,
    this.permissionStatus,
  });
}

/// حالة تهيئة محرك Agora
class InitializationStatus {
  final bool isInitialized;
  final bool isInProgress;
  final String? errorMessage;

  InitializationStatus({
    required this.isInitialized,
    required this.isInProgress,
    this.errorMessage,
  });
}

/// فئة لتمثيل أخطاء Agora
class AgoraError {
  final int code;
  final String message;

  AgoraError({required this.code, required this.message});
}

/// خدمة المحادثة الصوتية باستخدام Agora
/// تم تحسينها لمعالجة المشاكل المختلفة وتحسين الأداء
class VoiceChatService {
  static final VoiceChatService _instance = VoiceChatService._internal();

  factory VoiceChatService() => _instance;

  VoiceChatService._internal();

  // الحصول على App ID بشكل آمن من SecureConfig
  String get appId => SecureConfig.agoraAppId;

  // محرك RTC
  RtcEngine? _engine;

  // حالة الاتصال
  bool _isConnected = false;
  bool get isConnected => _isConnected;
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  bool _isInitializing = false; // لمنع محاولات التهيئة المتزامنة

  // معرف المستخدم المحلي
  int? _localUid;
  String? _lastChannelId;

  // للتعامل مع إعادة المحاولة
  int _initRetryCount = 0;
  static const int _maxInitRetries = 3;
  Timer? _reconnectTimer;

  // نظام تصفية الضوضاء الذكي
  bool _noiseDetectionEnabled = true;
  double _noiseThreshold = SecureConfig.defaultNoiseThreshold;
  final Map<int, int> _noisyUserWarnings = {}; // عدد التحذيرات لكل مستخدم
  final Map<int, DateTime> _lastNoiseDetection = {}; // آخر مرة تم اكتشاف ضوضاء
  Timer? _noiseCleanupTimer;

  // مستمعو الأحداث
  final _onUserJoinedStreamController = StreamController<int>.broadcast();
  final _onUserLeftStreamController = StreamController<int>.broadcast();
  final _onConnectionStateChangedStreamController = StreamController<ConnectionStateType>.broadcast();
  final _onErrorStreamController = StreamController<AgoraError>.broadcast();
  final _onAudioVolumeIndicationStreamController = StreamController<List<AudioVolumeInfo>>.broadcast();
  final _onInitializationStatusStreamController = StreamController<InitializationStatus>.broadcast();

  Stream<int> get onUserJoined => _onUserJoinedStreamController.stream;
  Stream<int> get onUserLeft => _onUserLeftStreamController.stream;
  Stream<ConnectionStateType> get onConnectionStateChanged => _onConnectionStateChangedStreamController.stream;
  Stream<AgoraError> get onError => _onErrorStreamController.stream;
  Stream<List<AudioVolumeInfo>> get onAudioVolumeIndication => _onAudioVolumeIndicationStreamController.stream;
  Stream<InitializationStatus> get onInitializationStatus => _onInitializationStatusStreamController.stream;

  // إعدادات نظام تصفية الضوضاء
  bool get isNoiseDetectionEnabled => _noiseDetectionEnabled;
  double get noiseThreshold => _noiseThreshold;

  /// تمكين/تعطيل نظام كشف الضوضاء
  void setNoiseDetectionEnabled(bool enabled) {
    _noiseDetectionEnabled = enabled;
    _log("تم ${enabled ? 'تمكين' : 'تعطيل'} نظام كشف الضوضاء");

    if (!enabled) {
      // تنظيف البيانات عند التعطيل
      _noisyUserWarnings.clear();
      _lastNoiseDetection.clear();
      _noiseCleanupTimer?.cancel();
    } else {
      // بدء مؤقت التنظيف
      _startNoiseCleanupTimer();
    }
  }

  /// تعيين عتبة الضوضاء (0.0 - 1.0)
  void setNoiseThreshold(double threshold) {
    _noiseThreshold = threshold.clamp(0.0, 1.0);
    _log("تم تعيين عتبة الضوضاء إلى: $_noiseThreshold");
  }

  /// إعادة تعيين تحذيرات الضوضاء لمستخدم معين
  void resetNoiseWarnings(int uid) {
    _noisyUserWarnings.remove(uid);
    _lastNoiseDetection.remove(uid);
    _log("تم إعادة تعيين تحذيرات الضوضاء للمستخدم $uid");
  }

  /// إعادة تعيين جميع تحذيرات الضوضاء
  void resetAllNoiseWarnings() {
    final userCount = _noisyUserWarnings.length;
    _noisyUserWarnings.clear();
    _lastNoiseDetection.clear();
    _log("تم إعادة تعيين تحذيرات الضوضاء لجميع المستخدمين ($userCount مستخدم)");
  }

  /// الحصول على عدد تحذيرات الضوضاء لمستخدم معين
  int getNoiseWarningCount(int uid) {
    return _noisyUserWarnings[uid] ?? 0;
  }

  /// الحصول على إحصائيات الضوضاء
  Map<String, dynamic> getNoiseStatistics() {
    return {
      'total_users_with_warnings': _noisyUserWarnings.length,
      'total_warnings': _noisyUserWarnings.values.fold(0, (sum, count) => sum + count),
      'noise_threshold': _noiseThreshold,
      'detection_enabled': _noiseDetectionEnabled,
    };
  }

  /// تحسين الأداء لتوفير البطارية
  Future<void> enableBatteryOptimization() async {
    if (_engine != null) {
      _log("تمكين تحسينات البطارية");

      // تقليل معدل تحديث مستوى الصوت
      await _engine!.enableAudioVolumeIndication(
        interval: 500, // بدلاً من 200ms
        smooth: 5,     // تنعيم أكثر
        reportVad: false, // تعطيل Voice Activity Detection
      );

      // تقليل جودة الصوت قليلاً لتوفير البطارية
      await _engine!.setAudioProfile(
        profile: AudioProfileType.audioProfileDefault,
        scenario: AudioScenarioType.audioScenarioGameStreaming,
      );

      _log("تم تمكين تحسينات البطارية");
    }
  }

  /// تحسين الأداء للجودة العالية
  Future<void> enableHighQualityMode() async {
    if (_engine != null) {
      _log("تمكين وضع الجودة العالية");

      // استعادة معدل التحديث العادي
      await _engine!.enableAudioVolumeIndication(
        interval: SecureConfig.audioVolumeIndicationInterval,
        smooth: SecureConfig.audioVolumeSmoothing,
        reportVad: SecureConfig.enableVoiceActivityDetection,
      );

      // تحسين جودة الصوت
      await _engine!.setAudioProfile(
        profile: AudioProfileType.audioProfileMusicHighQuality,
        scenario: AudioScenarioType.audioScenarioGameStreaming,
      );

      _log("تم تمكين وضع الجودة العالية");
    }
  }

  /// تكيف الجودة حسب قوة الشبكة
  Future<void> adaptToNetworkQuality(int quality) async {
    if (_engine != null) {
      _log("تكييف الجودة حسب الشبكة: $quality");

      if (quality <= 2) {
        // شبكة ضعيفة - تقليل الجودة
        await enableBatteryOptimization();
        _log("تم تقليل الجودة بسبب ضعف الشبكة");
      } else if (quality >= 4) {
        // شبكة قوية - جودة عالية
        await enableHighQualityMode();
        _log("تم تمكين الجودة العالية بسبب قوة الشبكة");
      }
    }
  }

  /// سجل تصحيح
  void _log(String message) {
    debugPrint("[VoiceChat] $message");
  }

  /// بدء مؤقت تنظيف بيانات الضوضاء
  void _startNoiseCleanupTimer() {
    _noiseCleanupTimer?.cancel();
    _noiseCleanupTimer = Timer.periodic(SecureConfig.noiseDataCleanupInterval, (timer) {
      _cleanupNoiseData();
    });
  }

  /// تنظيف بيانات الضوضاء القديمة
  void _cleanupNoiseData() {
    final now = DateTime.now();
    final expiredUsers = <int>[];

    // البحث عن المستخدمين الذين لم يصدروا ضوضاء لأكثر من المدة المحددة
    for (final entry in _lastNoiseDetection.entries) {
      if (now.difference(entry.value) > SecureConfig.noiseDataExpiration) {
        expiredUsers.add(entry.key);
      }
    }

    // إزالة البيانات المنتهية الصلاحية
    for (final uid in expiredUsers) {
      _noisyUserWarnings.remove(uid);
      _lastNoiseDetection.remove(uid);
    }

    if (expiredUsers.isNotEmpty) {
      _log("تم تنظيف بيانات الضوضاء لـ ${expiredUsers.length} مستخدم");
    }
  }

  /// كشف الضوضاء في مستوى الصوت
  void _detectNoise(List<AudioVolumeInfo> speakers) {
    if (!_noiseDetectionEnabled) return;

    final now = DateTime.now();

    for (final speaker in speakers) {
      if (speaker.uid == 0) continue; // تجاهل المستخدم المحلي

      final volume = speaker.volume! / 255.0; // تحويل إلى نطاق 0-1

      // كشف الضوضاء العالية
      if (volume > _noiseThreshold) {
        final uid = speaker.uid!;

        // التحقق من آخر مرة تم اكتشاف ضوضاء من هذا المستخدم
        final lastDetection = _lastNoiseDetection[uid];
        if (lastDetection == null || now.difference(lastDetection) > SecureConfig.noiseDetectionCooldown) {
          _handleNoiseDetection(uid, volume);
          _lastNoiseDetection[uid] = now;
        }
      }
    }
  }

  /// معالجة اكتشاف الضوضاء
  void _handleNoiseDetection(int uid, double volume) {
    _noisyUserWarnings[uid] = (_noisyUserWarnings[uid] ?? 0) + 1;
    final warningCount = _noisyUserWarnings[uid]!;

    _log("تم اكتشاف ضوضاء من المستخدم $uid (مستوى: ${(volume * 100).toInt()}%) - تحذير رقم $warningCount");

    // إجراءات تدريجية حسب عدد التحذيرات
    if (warningCount >= SecureConfig.maxNoiseWarnings) {
      // كتم تلقائي بعد الوصول للحد الأقصى من التحذيرات
      _log("كتم تلقائي للمستخدم $uid بسبب الضوضاء المستمرة (تحذير رقم $warningCount)");
      muteRemoteAudio(uid, true);

      // إرسال إشعار للمستخدمين الآخرين
      _onErrorStreamController.add(AgoraError(
        code: 9999, // رمز مخصص للضوضاء
        message: "تم كتم المستخدم $uid تلقائياً بسبب الضوضاء العالية",
      ));

      // إعادة تعيين عداد التحذيرات بعد الكتم
      _noisyUserWarnings[uid] = 0;
    }
  }

  /// تهيئة محرك Agora مع آلية إعادة المحاولة
  Future<bool> initialize() async {
    // إذا كانت التهيئة جارية بالفعل، انتظر حتى تنتهي
    if (_isInitializing) {
      _log("التهيئة جارية بالفعل، في انتظار الانتهاء...");
      _onInitializationStatusStreamController.add(InitializationStatus(
        isInitialized: false,
        isInProgress: true,
        errorMessage: null,
      ));
      return _isInitialized;
    }

    // إذا كان المحرك مهيأ بالفعل، أعد استخدامه
    if (_isInitialized && _engine != null) {
      _log("محرك Agora مهيأ بالفعل");
      _onInitializationStatusStreamController.add(InitializationStatus(
        isInitialized: true,
        isInProgress: false,
        errorMessage: null,
      ));
      return true;
    }

    _isInitializing = true;
    _initRetryCount = 0;

    // تحقق من أذونات الميكروفون قبل التهيئة
    final permissionStatus = await _requestPermissions();
    if (!permissionStatus.isGranted) {
      _log("تم رفض أذونات الميكروفون، لا يمكن تهيئة المحرك");
      _isInitializing = false;
      _onInitializationStatusStreamController.add(InitializationStatus(
        isInitialized: false,
        isInProgress: false,
        errorMessage: "تم رفض أذونات الميكروفون",
      ));
      return false;
    }

    return _initializeWithRetry();
  }

  /// تكوين إعدادات جودة الصوت
  Future<void> _configureAudioSettings() async {
    if (_engine != null) {
      final settings = SecureConfig.audioQualitySettings;

      // تمكين إلغاء الضوضاء
      if (settings['noise_suppression'] == true) {
        await _engine!.setParameters('{"che.audio.enable_ns": true}');
        _log("تم تمكين إلغاء الضوضاء");
      }

      // تمكين التحكم التلقائي في الكسب
      if (settings['auto_gain_control'] == true) {
        await _engine!.setParameters('{"che.audio.enable_agc": true}');
        _log("تم تمكين التحكم التلقائي في الكسب");
      }

      // تمكين إلغاء الصدى
      if (settings['echo_cancellation'] == true) {
        await _engine!.setParameters('{"che.audio.enable_aec": true}');
        _log("تم تمكين إلغاء الصدى");
      }
    }
  }

  /// تسجيل مستمعي أحداث Agora
  void _registerEventHandlers() {
    _engine!.registerEventHandler(RtcEngineEventHandler(
      onJoinChannelSuccess: (connection, elapsed) {
        _log("تم الانضمام إلى القناة: ${connection.channelId} بعد $elapsed مللي ثانية");
        _isConnected = true;
        _lastChannelId = connection.channelId;

        // إلغاء أي مؤقت لإعادة الاتصال
        _reconnectTimer?.cancel();
        _reconnectTimer = null;
      },
      onUserJoined: (connection, remoteUid, elapsed) {
        _log("انضم المستخدم: $remoteUid بعد $elapsed مللي ثانية");
        _onUserJoinedStreamController.add(remoteUid);
      },
      onUserOffline: (connection, remoteUid, reason) {
        _log("غادر المستخدم: $remoteUid، السبب: $reason");
        _onUserLeftStreamController.add(remoteUid);
      },
      onConnectionStateChanged: (connection, state, reason) {
        _log("تغيرت حالة الاتصال: $state، السبب: $reason");
        _onConnectionStateChangedStreamController.add(state);

        if (state == ConnectionStateType.connectionStateDisconnected ||
            state == ConnectionStateType.connectionStateFailed) {
          _isConnected = false;

          // محاولة إعادة الاتصال تلقائيًا عند فقدان الاتصال
          if (_lastChannelId != null && _localUid != null) {
            _scheduleReconnect();
          }
        } else if (state == ConnectionStateType.connectionStateConnected) {
          _isConnected = true;

          // إلغاء أي مؤقت لإعادة الاتصال
          _reconnectTimer?.cancel();
          _reconnectTimer = null;
        }
      },
      onError: (errorCode, msg) {
        _log("خطأ Agora: $errorCode - $msg");
        final error = AgoraError(code: errorCode.index, message: msg);
        _onErrorStreamController.add(error);

        // معالجة أخطاء معينة قد تتطلب إعادة الاتصال
        if (errorCode == ErrorCodeType.errTokenExpired) {
          _log("انتهت صلاحية الرمز، محاولة إعادة الاتصال...");
          _scheduleReconnect();
        }
      },
      onAudioVolumeIndication: (connection, speakers, totalVolume, speakerNumber) {
        // كشف الضوضاء أولاً
        _detectNoise(speakers);

        // إرسال معلومات مستوى الصوت إلى المستمعين
        // تقليل كمية السجلات لتجنب الإفراط في التسجيل
        if (speakerNumber > 0 && totalVolume > 50) {
          _log("مؤشر مستوى الصوت: $totalVolume، عدد المتحدثين: $speakerNumber");
        }
        _onAudioVolumeIndicationStreamController.add(speakers);
      },
    ));
  }

  /// جدولة إعادة الاتصال تلقائيًا بعد فترة زمنية
  void _scheduleReconnect() {
    // إلغاء أي مؤقت سابق
    _reconnectTimer?.cancel();

    // إعادة الاتصال بعد 3 ثوانٍ
    _reconnectTimer = Timer(const Duration(seconds: 3), () async {
      if (!_isConnected && _lastChannelId != null && _localUid != null) {
        _log("محاولة إعادة الاتصال تلقائيًا...");
        final result = await reconnect();
        if (result.success) {
          _log("تم إعادة الاتصال بنجاح");
        } else {
          _log("فشل في إعادة الاتصال: ${result.errorMessage}");
        }
      }
    });
  }

  /// تهيئة المحرك مع إعادة المحاولة عند الفشل
  Future<bool> _initializeWithRetry() async {
    _log("بدء تهيئة محرك Agora (محاولة ${_initRetryCount + 1}/$_maxInitRetries)");
    _onInitializationStatusStreamController.add(InitializationStatus(
      isInitialized: false,
      isInProgress: true,
      errorMessage: null,
    ));

    try {
      // إنشاء محرك RTC
      _engine = createAgoraRtcEngine();
      await _engine!.initialize(RtcEngineContext(
        appId: appId,
        channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
      ));

      _log("تم إنشاء محرك RTC بنجاح");

      // إعدادات خاصة بنظام iOS
      if (Platform.isIOS) {
        _log("تطبيق إعدادات خاصة بنظام iOS");
        final iosSettings = SecureConfig.iosSpecificSettings;

        // تكوين خاص بـ iOS لتحسين طلب الإذن
        if (iosSettings['use_apple_recording'] == true) {
          await _engine!.setParameters('{"che.audio.use.apple.recording": true}');
          _log("تم تمكين Apple Recording");
        }

        // تكوين إضافي لتحسين الأداء على iOS
        if (iosSettings['force_ios_speaker'] == true) {
          await _engine!.setParameters('{"che.audio.force.ios.speaker": true}');
          _log("تم تمكين Force iOS Speaker");
        }
      }

      // تعيين وضع الصوت
      await _engine!.setClientRole(role: ClientRoleType.clientRoleBroadcaster);
      _log("تم تعيين دور العميل: clientRoleBroadcaster");

      // تسجيل مستمعي الأحداث
      _registerEventHandlers();
      _log("تم تسجيل مستمعي الأحداث");

      // تمكين الصوت
      await _engine!.enableAudio();
      _log("تم تمكين الصوت");

      // تأخير تمكين مكبر الصوت إلى ما بعد الانضمام إلى القناة
      // سنقوم بذلك في دالة joinChannel

      // تكوين إعدادات جودة الصوت
      await _configureAudioSettings();
      _log("تم تكوين إعدادات جودة الصوت");

      // تمكين مؤشر مستوى الصوت
      await _engine!.enableAudioVolumeIndication(
        interval: SecureConfig.audioVolumeIndicationInterval,
        smooth: SecureConfig.audioVolumeSmoothing,
        reportVad: SecureConfig.enableVoiceActivityDetection,
      );
      _log("تم تمكين مؤشر مستوى الصوت");

      _isInitialized = true;
      _isInitializing = false;
      _log("تم تهيئة محرك Agora بنجاح");

      _onInitializationStatusStreamController.add(InitializationStatus(
        isInitialized: true,
        isInProgress: false,
        errorMessage: null,
      ));

      return true;
    } catch (e) {
      _log("خطأ في تهيئة محرك Agora: $e");
      _isInitialized = false;

      // محاولة إعادة التهيئة إذا لم نصل للحد الأقصى من المحاولات
      if (_initRetryCount < _maxInitRetries - 1) {
        _initRetryCount++;
        _log("إعادة المحاولة بعد $_initRetryCount ثانية...");

        // إعادة المحاولة بعد تأخير متزايد
        await Future.delayed(Duration(seconds: _initRetryCount));
        return _initializeWithRetry();
      } else {
        _isInitializing = false;
        _log("فشلت جميع محاولات التهيئة");

        _onInitializationStatusStreamController.add(InitializationStatus(
          isInitialized: false,
          isInProgress: false,
          errorMessage: e.toString(),
        ));

        return false;
      }
    }
  }

  /// طلب أذونات الميكروفون مع معالجة أفضل للرفض
  Future<PermissionStatus> _requestPermissions() async {
    _log("طلب أذونات الميكروفون");

    // التحقق من حالة الإذن الحالية
    final status = await Permission.microphone.status;

    if (status.isGranted) {
      _log("أذونات الميكروفون ممنوحة بالفعل");
      return status;
    }

    if (status.isPermanentlyDenied) {
      _log("تم رفض أذونات الميكروفون بشكل دائم، يجب فتح إعدادات التطبيق");
      // لا نفتح الإعدادات هنا تلقائيًا، سنعرض حوار للمستخدم أولاً
      return status;
    }

    // معالجة خاصة لنظام iOS
    if (Platform.isIOS) {
      _log("طلب إذن الميكروفون على iOS");

      // محاولة أولى
      var result = await Permission.microphone.request();
      if (result.isGranted) {
        _log("تم منح إذن الميكروفون على iOS");
        return result;
      }

      // إذا لم يتم منح الإذن، ننتظر قليلاً ونحاول مرة أخرى
      _log("محاولة ثانية لطلب إذن الميكروفون على iOS");
      await Future.delayed(const Duration(milliseconds: 500));
      result = await Permission.microphone.request();
      _log("حالة أذونات الميكروفون بعد المحاولة الثانية: ${result.isGranted ? 'ممنوحة' : 'مرفوضة'}");
      return result;
    } else {
      // طلب الإذن بشكل صريح على Android
      _log("طلب إذن الميكروفون بشكل صريح");
      final result = await Permission.microphone.request();
      _log("حالة أذونات الميكروفون: ${result.isGranted ? 'ممنوحة' : 'مرفوضة'}");
      return result;
    }
  }

  /// الانضمام إلى قناة صوتية مع معالجة أفضل للأخطاء
  ///
  /// [channelId] معرف القناة الصوتية
  /// [uid] معرف المستخدم الفريد
  /// [enableSpeaker] تمكين مكبر الصوت بعد الانضمام (الافتراضي: true)
  /// [speakerEnableDelayMs] تأخير تمكين مكبر الصوت بعد الانضمام (بالمللي ثانية)
  Future<JoinChannelResult> joinChannel(
    String channelId,
    int uid, {
    bool enableSpeaker = true, // مكبر الصوت هو الوضع الافتراضي
    int speakerEnableDelayMs = 1000,
  }) async {
    if (channelId.isEmpty) {
      _log("خطأ: معرف القناة فارغ");
      return JoinChannelResult(
        success: false,
        errorCode: VoiceChatErrorCode.invalidChannelId,
        errorMessage: "معرف القناة فارغ",
        permissionStatus: null,
      );
    }

    _log("محاولة الانضمام إلى القناة: $channelId بمعرف المستخدم: $uid");

    // التأكد من تهيئة المحرك أولاً
    if (_engine == null || !_isInitialized) {
      _log("محرك Agora غير مهيأ، جاري التهيئة...");
      final initialized = await initialize();
      if (!initialized) {
        return JoinChannelResult(
          success: false,
          errorCode: VoiceChatErrorCode.initializationFailed,
          errorMessage: "فشل في تهيئة محرك Agora",
          permissionStatus: null,
        );
      }
    }

    // التحقق من الأذونات
    final permissionStatus = await _requestPermissions();
    if (!permissionStatus.isGranted) {
      _log("تم رفض أذونات الميكروفون");

      // محاولة إضافية لطلب الإذن على iOS
      if (Platform.isIOS && !permissionStatus.isPermanentlyDenied) {
        _log("محاولة إضافية لطلب الإذن على iOS");
        // تأخير قصير قبل المحاولة مرة أخرى
        await Future.delayed(const Duration(milliseconds: 800));
        final retryStatus = await Permission.microphone.request();

        if (retryStatus.isGranted) {
          _log("تم منح الإذن في المحاولة الإضافية على iOS");
        } else {
          _log("فشلت المحاولة الإضافية على iOS");
          return JoinChannelResult(
            success: false,
            errorCode: VoiceChatErrorCode.microphonePermissionDenied,
            errorMessage: retryStatus.isPermanentlyDenied
                ? "تم رفض أذونات الميكروفون بشكل دائم، يرجى تمكينها من إعدادات التطبيق"
                : "تم رفض أذونات الميكروفون",
            permissionStatus: retryStatus,
          );
        }
      } else {
        return JoinChannelResult(
          success: false,
          errorCode: VoiceChatErrorCode.microphonePermissionDenied,
          errorMessage: permissionStatus.isPermanentlyDenied
              ? "تم رفض أذونات الميكروفون بشكل دائم، يرجى تمكينها من إعدادات التطبيق"
              : "تم رفض أذونات الميكروفون",
          permissionStatus: permissionStatus,
        );
      }
    }

    try {
      // إلغاء أي مؤقت لإعادة الاتصال
      _reconnectTimer?.cancel();

      // الانضمام إلى القناة
      _log("جاري الانضمام إلى القناة: $channelId");

      // تسجيل وقت بدء الانضمام للقياس
      final joinStartTime = DateTime.now();

      await _engine!.joinChannel(
        token: '', // استخدم رمز مصادقة إذا كان مكونًا في لوحة تحكم Agora
        channelId: channelId,
        uid: uid,
        options: const ChannelMediaOptions(
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
        ),
      );

      // حساب الوقت المستغرق للانضمام
      final joinDuration = DateTime.now().difference(joinStartTime).inMilliseconds;
      _log("تم الانضمام إلى القناة: $channelId بعد $joinDuration مللي ثانية");

      _localUid = uid;
      _lastChannelId = channelId;
      _isConnected = true; // تحديث حالة الاتصال

      // تمكين مكبر الصوت بعد الانضمام إذا كان مطلوبًا
      if (enableSpeaker) {
        // تشغيل في خلفية منفصلة لتجنب تأخير إرجاع نتيجة الانضمام
        Future.delayed(Duration.zero, () async {
          await enableSpeakerphoneAfterJoin(delayMs: speakerEnableDelayMs);
        });
      }

      return JoinChannelResult(
        success: true,
        errorCode: null,
        errorMessage: null,
        permissionStatus: permissionStatus,
      );
    } catch (e) {
      _log("خطأ في الانضمام إلى القناة: $e");
      return JoinChannelResult(
        success: false,
        errorCode: VoiceChatErrorCode.joinChannelFailed,
        errorMessage: "فشل في الانضمام إلى القناة: $e",
        permissionStatus: permissionStatus,
      );
    }
  }

  /// مغادرة القناة الصوتية
  Future<void> leaveChannel() async {
    if (_engine != null && _isConnected) {
      _log("جاري مغادرة القناة");
      await _engine!.leaveChannel();
      _isConnected = false;
      _localUid = null;
      _log("تمت مغادرة القناة بنجاح");
    } else {
      _log("لا يمكن مغادرة القناة: المستخدم غير متصل");
    }
  }

  /// مغادرة القناة الصوتية بشكل متزامن (للاستخدام في dispose)
  void leaveChannelSync() {
    if (_engine != null) {
      try {
        _log("جاري مغادرة القناة بشكل متزامن");
        _engine!.leaveChannel();
        _isConnected = false;
        _localUid = null;
      } catch (e) {
        _log("خطأ في مغادرة القناة بشكل متزامن: $e");
      }
    }
  }

  /// كتم/إلغاء كتم الميكروفون المحلي
  Future<void> muteLocalAudio(bool mute) async {
    if (_engine != null) {
      _log("${mute ? 'كتم' : 'إلغاء كتم'} الميكروفون المحلي");
      await _engine!.muteLocalAudioStream(mute);
    } else {
      _log("لا يمكن كتم الميكروفون: محرك Agora غير مهيأ");
    }
  }

  /// كتم/إلغاء كتم ميكروفون مستخدم معين
  Future<void> muteRemoteAudio(int uid, bool mute) async {
    if (_engine != null) {
      _log("${mute ? 'كتم' : 'إلغاء كتم'} ميكروفون المستخدم: $uid");
      await _engine!.muteRemoteAudioStream(uid: uid, mute: mute);
    } else {
      _log("لا يمكن كتم ميكروفون المستخدم: محرك Agora غير مهيأ");
    }
  }

  /// التبديل بين مكبر الصوت والسماعة مع آلية إعادة المحاولة
  Future<bool> setSpeakerphone(bool enable, {int maxRetries = 3, int delayMs = 500}) async {
    if (_engine == null) {
      _log("لا يمكن تغيير حالة مكبر الصوت: محرك Agora غير مهيأ");
      return false;
    }

    // تأكد من أن المستخدم متصل بالقناة أولاً
    if (!_isConnected) {
      _log("لا يمكن تغيير حالة مكبر الصوت: المستخدم غير متصل بالقناة");
      return false;
    }

    _log("${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت (محاولة 1/$maxRetries)");

    // المحاولة الأولى
    try {
      await _engine!.setEnableSpeakerphone(enable);
      _log("تم ${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت بنجاح");
      return true;
    } catch (e) {
      _log("خطأ في تغيير حالة مكبر الصوت: $e");

      // إذا فشلت المحاولة الأولى، جرب طريقة بديلة باستخدام setParameters
      try {
        await _engine!.setParameters(
          '{"che.audio.output.speakerOn": ${enable ? "true" : "false"}}',
        );
        _log("تم استخدام طريقة بديلة لـ ${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت");
        return true;
      } catch (e2) {
        _log("فشلت الطريقة البديلة أيضًا: $e2");
      }

      // إعادة المحاولة عدة مرات مع تأخير
      for (var i = 1; i < maxRetries; i++) {
        await Future.delayed(Duration(milliseconds: delayMs * i));
        _log("${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت (محاولة ${i+1}/$maxRetries)");

        try {
          await _engine!.setEnableSpeakerphone(enable);
          _log("تم ${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت بنجاح في المحاولة ${i+1}");
          return true;
        } catch (e) {
          _log("فشلت المحاولة ${i+1}: $e");
        }
      }

      _log("فشلت جميع محاولات ${enable ? 'تمكين' : 'تعطيل'} مكبر الصوت");
      return false;
    }
  }

  /// تمكين مكبر الصوت بعد الانضمام إلى القناة
  Future<bool> enableSpeakerphoneAfterJoin({int delayMs = 1000}) async {
    if (_engine == null || !_isInitialized) {
      _log("لا يمكن تمكين مكبر الصوت: محرك Agora غير مهيأ");
      return false;
    }

    // انتظر لفترة قصيرة بعد الانضمام إلى القناة
    _log("انتظار $delayMs مللي ثانية قبل تمكين مكبر الصوت");
    await Future.delayed(Duration(milliseconds: delayMs));

    return setSpeakerphone(true);
  }

  /// إعادة الاتصال بالقناة الأخيرة
  Future<JoinChannelResult> reconnect() async {
    if (_lastChannelId != null && _localUid != null) {
      _log("محاولة إعادة الاتصال بالقناة: $_lastChannelId");
      return joinChannel(_lastChannelId!, _localUid!);
    } else {
      _log("لا يمكن إعادة الاتصال: لا توجد معلومات عن القناة الأخيرة");
      return JoinChannelResult(
        success: false,
        errorCode: VoiceChatErrorCode.invalidChannelId,
        errorMessage: "لا توجد معلومات عن القناة الأخيرة",
        permissionStatus: null,
      );
    }
  }

  /// عرض حوار طلب إذن الميكروفون
  Future<bool> showPermissionDialog(BuildContext context) async {
    _log("عرض حوار طلب إذن الميكروفون");

    final status = await Permission.microphone.status;

    if (status.isGranted) {
      return true;
    }

    // استخدام متغير محلي للسياق لتجنب مشاكل عدم التزامن
    final localContext = context;

    if (status.isPermanentlyDenied) {
      // عرض حوار لتوجيه المستخدم لفتح الإعدادات
      bool? result;

      // التحقق من أن السياق لا يزال صالحًا
      if (localContext.mounted) {
        result = await showDialog<bool>(
          context: localContext,
          barrierDismissible: false,
          builder: (dialogContext) => AlertDialog(
            title: const Text('إذن الميكروفون مطلوب'),
            content: const Text(
              'تم رفض إذن الميكروفون بشكل دائم. يرجى فتح إعدادات التطبيق وتمكين إذن الميكروفون للاستمتاع بميزة المحادثة الصوتية.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop(true);
                  openAppSettings();
                },
                child: const Text('فتح الإعدادات'),
              ),
            ],
          ),
        );
      }

      return result ?? false;
    } else {
      // عرض حوار لشرح سبب الحاجة للإذن
      bool? shouldRequest;

      // التحقق من أن السياق لا يزال صالحًا
      if (localContext.mounted) {
        shouldRequest = await showDialog<bool>(
          context: localContext,
          barrierDismissible: false,
          builder: (dialogContext) => AlertDialog(
            title: const Text('إذن الميكروفون مطلوب'),
            content: const Text(
              'للمشاركة في المحادثة الصوتية، نحتاج إلى إذن الوصول إلى الميكروفون. هل تسمح لنا باستخدام الميكروفون؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(false),
                child: const Text('رفض'),
              ),
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(true),
                child: const Text('السماح'),
              ),
            ],
          ),
        );
      }

      if (shouldRequest == true) {
        final result = await Permission.microphone.request();
        return result.isGranted;
      }

      return false;
    }
  }

  /// التخلص من الموارد مع تحسين إدارة الذاكرة
  Future<void> dispose() async {
    _log("جاري التخلص من موارد المحادثة الصوتية");

    try {
      // إيقاف جميع المؤقتات أولاً
      _reconnectTimer?.cancel();
      _reconnectTimer = null;
      _noiseCleanupTimer?.cancel();
      _noiseCleanupTimer = null;

      // مغادرة القناة
      await leaveChannel();

      // تنظيف بيانات الضوضاء
      _noisyUserWarnings.clear();
      _lastNoiseDetection.clear();

      // إغلاق جميع StreamControllers بشكل آمن
      if (!_onUserJoinedStreamController.isClosed) {
        await _onUserJoinedStreamController.close();
      }
      if (!_onUserLeftStreamController.isClosed) {
        await _onUserLeftStreamController.close();
      }
      if (!_onConnectionStateChangedStreamController.isClosed) {
        await _onConnectionStateChangedStreamController.close();
      }
      if (!_onErrorStreamController.isClosed) {
        await _onErrorStreamController.close();
      }
      if (!_onAudioVolumeIndicationStreamController.isClosed) {
        await _onAudioVolumeIndicationStreamController.close();
      }
      if (!_onInitializationStatusStreamController.isClosed) {
        await _onInitializationStatusStreamController.close();
      }

      // تحرير محرك Agora
      if (_engine != null) {
        await _engine!.release();
        _engine = null;
      }

      // إعادة تعيين المتغيرات
      _isInitialized = false;
      _isConnected = false;
      _isInitializing = false;
      _localUid = null;
      _lastChannelId = null;
      _initRetryCount = 0;

      _log("تم التخلص من موارد المحادثة الصوتية بنجاح");
    } catch (e) {
      _log("خطأ أثناء التخلص من الموارد: $e");
      // حتى في حالة الخطأ، نتأكد من تنظيف المتغيرات الأساسية
      _engine = null;
      _isInitialized = false;
      _isConnected = false;
    }
  }
}
