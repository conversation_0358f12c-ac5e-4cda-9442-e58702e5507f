import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/utils/constants/assets_constants.dart';







class AppLogo extends StatelessWidget {
  const AppLogo({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the screen width and height using MediaQuery
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Adjust the size of the logo based on screen dimensions
    final logoWidth = screenWidth * 0.5; // Example: 45% of screen width
    final logoHeight = screenHeight * 0.2; // Example: 8% of screen height

    return QImage(
      imageUrl: Assets.appLogo,
      height: logoHeight,
      width: logoWidth,
      fit: BoxFit.contain,
      color: const Color(0xFF0066FF), padding: EdgeInsets.zero, // Primary color
    );
  }
}



// class AppLogo extends StatelessWidget {
//   const AppLogo({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return const QImage(
//       imageUrl: Assets.appLogo,
//       height: 66,
//       width: 168,
//       color: Color(0xFF0066FF),
//     );
//   }
// }
