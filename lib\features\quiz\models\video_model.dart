class VideoModel {
  final String id;
  final String title;
  final String description;
  final String youtubeUrl;
  final String thumbnailUrl;
  final String instructor;
  final String duration;
  final String categoryId;
  final int viewCount;
  final double rating;
  final DateTime uploadDate;
  final String difficulty;

  VideoModel({
    required this.id,
    required this.title,
    required this.description,
    required this.youtubeUrl,
    required this.thumbnailUrl,
    required this.instructor,
    required this.duration,
    required this.categoryId,
    required this.viewCount,
    required this.rating,
    required this.uploadDate,
    required this.difficulty,
  });

  factory VideoModel.fromMap(Map<String, dynamic> map) {
    return VideoModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      youtubeUrl: map['youtube_url'] ?? '',
      thumbnailUrl: map['thumbnail_url'] ?? '',
      instructor: map['instructor'] ?? '',
      duration: map['duration'] ?? '',
      categoryId: map['category_id'] ?? '',
      viewCount: map['view_count'] ?? 0,
      rating: (map['rating'] ?? 0.0).toDouble(),
      uploadDate: map['upload_date']?.toDate() ?? DateTime.now(),
      difficulty: map['difficulty'] ?? 'beginner',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'youtube_url': youtubeUrl,
      'thumbnail_url': thumbnailUrl,
      'instructor': instructor,
      'duration': duration,
      'category_id': categoryId,
      'view_count': viewCount,
      'rating': rating,
      'upload_date': uploadDate,
      'difficulty': difficulty,
    };
  }

  String get videoId {
    // Extract video ID from YouTube URL
    final RegExp regExp = RegExp(
      r'^.*(?:youtu.be\/|v\/|e\/|u\/\w+\/|embed\/|v=)([^#\&\?]*).*$',
      caseSensitive: false,
      multiLine: false,
    );
    if (youtubeUrl.isEmpty) return '';
    final Match? match = regExp.firstMatch(youtubeUrl);
    return match?.group(1) ?? '';
  }

  String get highQualityThumbnail =>
      'https://img.youtube.com/vi/${videoId}/hqdefault.jpg';

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'youtubeUrl': youtubeUrl,
      'thumbnailUrl': thumbnailUrl,
      'instructor': instructor,
      'duration': duration,
      'categoryId': categoryId,
      'viewCount': viewCount,
      'rating': rating,
      'difficulty': difficulty,
      'uploadDate': uploadDate.toIso8601String(),
    };
  }

  factory VideoModel.fromJson(Map<String, dynamic> json) {
    return VideoModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      youtubeUrl: json['youtubeUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      instructor: json['instructor'],
      duration: json['duration'],
      categoryId: json['categoryId'],
      viewCount: json['viewCount'],
      rating: json['rating'].toDouble(),
      difficulty: json['difficulty'],
      uploadDate: DateTime.parse(json['uploadDate']),
    );
  }
}
