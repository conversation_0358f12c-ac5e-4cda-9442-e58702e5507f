import 'package:just_audio/just_audio.dart';

class AssetsUtils {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  
  static const String _soundPath = 'assets/sounds';
  
  static Future<void> playCorrectAnswerSound() async {
    await _audioPlayer.setAsset('$_soundPath/right.mp3');
    await _audioPlayer.play();
  }
  
  static Future<void> playWrongAnswerSound() async {
    await _audioPlayer.setAsset('$_soundPath/wrong.mp3');
    await _audioPlayer.play();
  }
  
  static Future<void> playClickSound() async {
    await _audioPlayer.setAsset('$_soundPath/click.mp3'); 
    await _audioPlayer.play();
  }
}
