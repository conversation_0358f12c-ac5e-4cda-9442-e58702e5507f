import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/features/systemConfig/model/answer_mode.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/constants/sound_constants.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:just_audio/just_audio.dart';

class OptionContainer extends StatefulWidget {
  const OptionContainer({
    required this.quizType,
    required this.answerMode,
    required this.showAudiencePoll,
    required this.hasSubmittedAnswerForCurrentQuestion,
    required this.constraints,
    required this.answerOption,
    required this.correctOptionId,
    required this.submitAnswer,
    required this.submittedAnswerId,
    this.canResubmitAnswer = false,
    this.audiencePollPercentage,
    this.trueFalseOption = false,
    super.key,
  });

  final bool Function() hasSubmittedAnswerForCurrentQuestion;
  final void Function(String) submitAnswer;
  final AnswerOption answerOption;
  final BoxConstraints constraints;
  final String correctOptionId;
  final String submittedAnswerId;
  final bool showAudiencePoll;
  final int? audiencePollPercentage;
  final AnswerMode answerMode;
  final bool canResubmitAnswer;
  final QuizTypes quizType;
  final bool trueFalseOption;

  @override
  State<OptionContainer> createState() => _OptionContainerState();
}

class _OptionContainerState extends State<OptionContainer>
    with TickerProviderStateMixin {
  late final animationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 90),
  );
  late Animation<double> animation = Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(parent: animationController, curve: Curves.easeInQuad),
  );

  late AnimationController topContainerAnimationController =
      AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 180),
  );
  late Animation<double> topContainerOpacityAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: topContainerAnimationController,
      curve: const Interval(0, 0.25, curve: Curves.easeInQuad),
    ),
  );

  late Animation<double> topContainerAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: topContainerAnimationController,
      curve: const Interval(0, 0.5, curve: Curves.easeInQuad),
    ),
  );

  late Animation<double> answerCorrectnessAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: topContainerAnimationController,
      curve: const Interval(0.5, 1, curve: Curves.easeInQuad),
    ),
  );

  late double heightPercentage = 0.105;
  late final _audioPlayer = AudioPlayer();

  late TextSpan textSpan = TextSpan(
    text: widget.answerOption.title,
    style: GoogleFonts.ibmPlexSansArabic(
      textStyle: TextStyle(
        color: optionTextColor,
        height: 1,
        fontSize: 16,
      ),
    ),
  );

  @override
  void dispose() {
    animationController.dispose();
    topContainerAnimationController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> playSound(String trackName) async {
    if (context.read<SettingsCubit>().getSettings().sound) {
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }
      await _audioPlayer.setAsset(trackName);
      await _audioPlayer.play();
    }
  }

  Future<void> playVibrate() async {
    if (context.read<SettingsCubit>().getSettings().vibration) {
      UiUtils.vibrate();
    }
  }

  int calculateMaxLines() {
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: Directionality.of(context),
    )..layout(maxWidth: widget.constraints.maxWidth * 0.85);

    return textPainter.computeLineMetrics().length;
  }

  bool get isCorrectAnswer => widget.answerOption.id == widget.correctOptionId;

  bool get isSubmittedAnswer =>
      widget.answerOption.id == widget.submittedAnswerId;

  // Add these new getters
  bool get hasSubmittedAnswer => widget.hasSubmittedAnswerForCurrentQuestion();
  
  bool get isSelected => widget.answerOption.id == widget.submittedAnswerId;
  
  bool get showAnswerPercentage => widget.showAudiencePoll && widget.audiencePollPercentage != null;
  
  int? get answerPercentage => widget.audiencePollPercentage;
  
  String get title => widget.answerOption.title ?? "";

  Color get optionTextColor {
    final colorScheme = Theme.of(context).colorScheme;

    if (widget.answerMode == AnswerMode.noAnswerCorrectness) {
      return isSubmittedAnswer ? colorScheme.surface : colorScheme.onSurface;
    }

    if (widget.hasSubmittedAnswerForCurrentQuestion()) {
      if (widget.answerMode ==
              AnswerMode.showAnswerCorrectnessAndCorrectAnswer &&
          (isCorrectAnswer || isSubmittedAnswer)) {
        return colorScheme.surface;

        /// for showAnswerCorrectness
      } else if (isSubmittedAnswer) {
        return colorScheme.surface;
      }
    }

    return colorScheme.onSurface;
  }


  void _onTapOptionContainer() {
    if (widget.answerMode == AnswerMode.noAnswerCorrectness) {
      widget.submitAnswer(widget.answerOption.id!);

      playSound(clickEventSoundTrack);
      playVibrate();
    } else {
      if (!widget.hasSubmittedAnswerForCurrentQuestion()) {
        widget.submitAnswer(widget.answerOption.id!);

        topContainerAnimationController.forward();

        if (widget.correctOptionId == widget.answerOption.id) {
          playSound(correctAnswerSoundTrack);
        } else {
          playSound(wrongAnswerSoundTrack);
        }
        playVibrate();
      }
    }
  }

  Widget _buildOptionDetails(double maxWidth) {
    final primaryColor = Theme.of(context).primaryColor;
    

    LinearGradient getGradient() {
      final colorScheme = Theme.of(context).colorScheme;
      final isDark = Theme.of(context).brightness == Brightness.dark;
      
      // تحقق من حالة الإعدادات
      if (widget.answerMode == AnswerMode.noAnswerCorrectness) {
        // حالة عدم إظهار صحة الإجابة
        return isSelected
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor.withOpacity(isDark ? 0.3 : 0.15),
                  primaryColor.withOpacity(isDark ? 0.2 : 0.1),
                ],
              )
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  colorScheme.surface,
                  colorScheme.surface.withOpacity(0.8),
                ],
              );
      }
      
      // حالات إظهار صحة الإجابة
      if (hasSubmittedAnswer) {
        if (widget.answerMode == AnswerMode.showAnswerCorrectnessAndCorrectAnswer) {
          // حالة تصحيح الإجابة وإظهار الإجابة الصحيحة
          if (isCorrectAnswer) {
            return LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.green.withOpacity(isDark ? 0.3 : 0.15),
                Colors.green.withOpacity(isDark ? 0.2 : 0.1),
              ],
            );
          }
          if (isSelected && !isCorrectAnswer) {
            return LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.red.withOpacity(isDark ? 0.3 : 0.15),
                Colors.red.withOpacity(isDark ? 0.2 : 0.1),
              ],
            );
          }
          return LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colorScheme.surface,
              colorScheme.surface.withOpacity(0.8),
            ],
          );
        } else {
          // حالة إظهار صحة الإجابة فقط
          if (isSelected) {
            return isCorrectAnswer
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.green.withOpacity(isDark ? 0.3 : 0.15),
                      Colors.green.withOpacity(isDark ? 0.2 : 0.1),
                    ],
                  )
                : LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.red.withOpacity(isDark ? 0.3 : 0.15),
                      Colors.red.withOpacity(isDark ? 0.2 : 0.1),
                    ],
                  );
          }
          return LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colorScheme.surface,
              colorScheme.surface.withOpacity(0.8),
            ],
          );
        }
      }
      
      // قبل تقديم الإجابة
      return isSelected
          ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                primaryColor.withOpacity(isDark ? 0.3 : 0.15),
                primaryColor.withOpacity(isDark ? 0.2 : 0.1),
              ],
            )
          : LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colorScheme.surface,
                colorScheme.surface.withOpacity(0.8),
              ],
            );
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(top: 15.0),
      decoration: BoxDecoration(
        gradient: getGradient(),
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: isSelected 
                ? primaryColor.withOpacity(0.2) 
                : Theme.of(context).colorScheme.shadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 3),
            spreadRadius: 0,
          ),
        ],
      ),
      width: maxWidth,
      child: Stack(
        children: [
          // الشريط الجانبي - يظهر فقط في حالات معينة
          if (isSelected && 
              (widget.answerMode == AnswerMode.noAnswerCorrectness || 
               !hasSubmittedAnswer ||
               (hasSubmittedAnswer && widget.answerMode == AnswerMode.showAnswerCorrectnessAndCorrectAnswer)))
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: Container(
                width: 5,
                decoration: BoxDecoration(
                  color: hasSubmittedAnswer && widget.answerMode != AnswerMode.noAnswerCorrectness
                      ? (isCorrectAnswer 
                          ? Color(0xFF4CAF50) 
                          : Color(0xFFE53935))
                      : primaryColor,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(16.0),
                    bottomRight: Radius.circular(16.0),
                  ),
                ),
              ),
            ),
            
          // المحتوى الرئيسي
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 14.0),
            child: Row(
              children: [
                // أيقونة الحالة - تظهر حسب حالة الإعدادات
                if (widget.answerMode != AnswerMode.noAnswerCorrectness && hasSubmittedAnswer && isCorrectAnswer)
                  Container(
                    margin: const EdgeInsets.only(left: 0, right: 10.0),
                    decoration: BoxDecoration(
                      color: const Color(0xFF4CAF50).withOpacity(0.15),
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(6),
                    child: const Icon(
                      Icons.check_rounded,
                      color: Color(0xFF4CAF50),
                      size: 18.0,
                    ),
                  )
                else if (widget.answerMode != AnswerMode.noAnswerCorrectness && hasSubmittedAnswer && isSelected && !isCorrectAnswer)
                  Container(
                    margin: const EdgeInsets.only(left: 0, right: 10.0),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE53935).withOpacity(0.15),
                      shape: BoxShape.circle,
                    ),
                    padding: EdgeInsets.all(6),
                    child: const Icon(
                      Icons.close_rounded,
                      color: Color(0xFFE53935),
                      size: 18.0,
                    ),
                  )
                else if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(left: 0, right: 10.0),
                    decoration: BoxDecoration(
                      color: primaryColor.withOpacity(0.15),
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(6),
                    child: Icon(
                      Icons.radio_button_checked_rounded,
                      color: primaryColor,
                      size: 18.0,
                    ),
                  )
                else
                  Container(
                    margin: const EdgeInsets.only(left: 0, right: 10.0),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(6),
                    child: Icon(
                      Icons.radio_button_unchecked_rounded,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      size: 18.0,
                    ),
                  ),
                
                // نسبة الجمهور إذا كانت متاحة
                if (showAnswerPercentage)
                  Container(
                    decoration: BoxDecoration(
                      color: primaryColor.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                    margin: const EdgeInsets.only(left: 0.0, right: 10.0),
                    child: Text(
                      "$answerPercentage%",
                      style: TextStyle(
                        color: primaryColor,
                        fontSize: 12.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                
                // نص الإجابة
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.ibmPlexSansArabic(
                      textStyle: TextStyle(
                        color: _getTextColor(),
                        fontSize: 16.0,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                        height: 1.3,
                      ),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  // دالة جديدة للحصول على لون النص بناءً على حالة الإعدادات
  Color _getTextColor() {
    final primaryColor = Theme.of(context).primaryColor;
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (widget.answerMode == AnswerMode.noAnswerCorrectness) {
      return isSelected ? primaryColor : colorScheme.onSurface;
    }
    
    if (hasSubmittedAnswer) {
      if (widget.answerMode == AnswerMode.showAnswerCorrectnessAndCorrectAnswer) {
        if (isCorrectAnswer) {
          return isDark ? Colors.green.shade300 : Colors.green.shade700;
        }
        if (isSelected && !isCorrectAnswer) {
          return isDark ? Colors.red.shade300 : Colors.red.shade700;
        }
        return colorScheme.onSurface;
      } else {
        if (isSelected) {
          return isCorrectAnswer 
              ? (isDark ? Colors.green.shade300 : Colors.green.shade700)
              : (isDark ? Colors.red.shade300 : Colors.red.shade700);
        }
        return colorScheme.onSurface;
      }
    }
    
    return isSelected ? primaryColor : colorScheme.onSurface;
  }

  @override
  Widget build(BuildContext context) {
    textSpan = TextSpan(
      text: widget.answerOption.title,
      style: GoogleFonts.ibmPlexSansArabic(
        textStyle: const TextStyle(
          color: Colors.white,
          height: 1,
          fontSize: 16,
        ),
      ),
    );
    return GestureDetector(
      onTapCancel: animationController.reverse,
      onTap: () async {
        await animationController.reverse();
        _onTapOptionContainer();
      },
      onTapDown: (_) => animationController.forward(),
      child: widget.showAudiencePoll
          ? Row(
              children: [
                _buildOptionDetails(widget.constraints.maxWidth * .8),
                const SizedBox(width: 10),
                Text(
                  '${widget.audiencePollPercentage}%',
                  style: GoogleFonts.ibmPlexSansArabic(
                    textStyle: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontSize: 16,
                      fontWeight: FontWeights.bold,
                    ),
                  ),
                ),
              ],
            )
          : _buildOptionDetails(widget.constraints.maxWidth),
    );
  }
}
        
