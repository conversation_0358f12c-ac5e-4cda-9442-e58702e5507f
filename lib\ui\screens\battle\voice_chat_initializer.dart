import 'dart:async';
import 'package:flutter/material.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutterquiz/services/voice_chat_service.dart';

/// مهيئ المحادثة الصوتية
/// يستخدم لتهيئة المحادثة الصوتية في صفحات التحديات
class VoiceChatInitializer {
  // مستويات الصوت
  double _localVolumeLevel = 0.0;
  final Map<int, double> _remoteVolumeLevels = {};

  // حالة الميكروفون
  bool _isMicMuted = false;
  final Map<int, bool> _remoteMicMuted = {};

  // اشتراكات الأحداث
  StreamSubscription<List<AudioVolumeInfo>>? _volumeSubscription;
  StreamSubscription<InitializationStatus>? _initStatusSubscription;

  // خدمة المحادثة الصوتية
  final VoiceChatService _voiceChatService = VoiceChatService();

  // المعرف المحلي

  // حالة التهيئة
  bool _isInitialized = false;
  bool _isConnected = false;

  // الحصول على مستوى الصوت المحلي
  double get localVolumeLevel => _localVolumeLevel;

  // الحصول على حالة الميكروفون المحلي
  bool get isMicMuted => _isMicMuted;

  // الحصول على حالة التهيئة
  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;

  // الحصول على مستوى صوت مستخدم معين
  double getRemoteVolumeLevel(int uid) {
    return _remoteVolumeLevels[uid] ?? 0.0;
  }

  // الحصول على حالة ميكروفون مستخدم معين
  bool isRemoteMicMuted(int uid) {
    return _remoteMicMuted[uid] ?? true;
  }

  // تهيئة المحادثة الصوتية
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    // الاستماع إلى حالة التهيئة
    _initStatusSubscription = _voiceChatService.onInitializationStatus.listen((status) {
      _isInitialized = status.isInitialized;
      debugPrint("حالة تهيئة المحادثة الصوتية: ${status.isInitialized}");
    });

    // تهيئة محرك المحادثة الصوتية
    final initialized = await _voiceChatService.initialize();
    
    if (initialized) {
      // الاستماع إلى تغييرات مستوى الصوت
      _volumeSubscription = _voiceChatService.onAudioVolumeIndication.listen((speakers) {
        // تحديث مستوى الصوت المحلي
        final localSpeaker = speakers.firstWhere(
          (speaker) => speaker.uid == 0, // 0 يمثل المستخدم المحلي
          orElse: () => const AudioVolumeInfo(uid: 0, volume: 0, vad: 0),
        );
        
        _localVolumeLevel = localSpeaker.volume! / 255.0; // تحويل القيمة إلى نطاق 0-1
        
        // تحديث مستويات صوت المستخدمين البعيدين
        for (final speaker in speakers) {
          if (speaker.uid != 0) { // تجاهل المستخدم المحلي
            _remoteVolumeLevels[speaker.uid!] = speaker.volume! / 255.0;
          }
        }
      });
    }

    return initialized;
  }

  // الانضمام إلى قناة صوتية
  Future<bool> joinChannel(String channelId, int uid) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return false;
    }

    final result = await _voiceChatService.joinChannel(channelId, uid);
    _isConnected = result.success;
    
    return result.success;
  }

  // كتم/إلغاء كتم الميكروفون المحلي
  Future<void> toggleMute() async {
    _isMicMuted = !_isMicMuted;
    await _voiceChatService.muteLocalAudio(_isMicMuted);
  }

  // كتم/إلغاء كتم ميكروفون مستخدم معين
  Future<void> toggleRemoteMute(int uid) async {
    _remoteMicMuted[uid] = !(_remoteMicMuted[uid] ?? false);
    await _voiceChatService.muteRemoteAudio(uid, _remoteMicMuted[uid]!);
  }

  // مغادرة القناة الصوتية
  Future<void> leaveChannel() async {
    await _voiceChatService.leaveChannel();
    _isConnected = false;
  }

  // التخلص من الموارد
  Future<void> dispose() async {
    await _volumeSubscription?.cancel();
    await _initStatusSubscription?.cancel();
    await leaveChannel();
    _remoteVolumeLevels.clear();
    _remoteMicMuted.clear();
  }
}
