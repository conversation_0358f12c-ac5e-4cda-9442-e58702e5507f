import 'dart:developer';
import 'dart:io';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutterquiz/StoreConfig.dart';
import 'package:flutterquiz/app/app.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/performance_utils.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'firebase_options.dart';

void main() {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    try {
      // عرض شاشة التحميل مع دعم الثيمات
      runApp(
        MaterialApp(
          debugShowCheckedModeBanner: false,
          home: Builder(
            builder: (context) {
              // التحقق من brightness النظام مباشرة
              final platformBrightness =
                  WidgetsBinding.instance.platformDispatcher.platformBrightness;
              final isDark = platformBrightness == Brightness.dark;

              return Scaffold(
                backgroundColor:
                    isDark ? const Color(0xFF121212) : Colors.white,
                body: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isDark
                          ? [
                              const Color(0xFF1E1E1E),
                              const Color(0xFF121212),
                              const Color(0xFF2C2C2C),
                              const Color(0xFF1A1A1A),
                              const Color(0xFF121212),
                            ]
                          : [
                              const Color(0xFF1E1E1E),
                              const Color(0xFF121212),
                              const Color(0xFF2C2C2C),
                              const Color(0xFF1A1A1A),
                              const Color(0xFF121212),
                            ],
                      stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                    ),
                  ),
                  child: Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isDark ? Colors.blueAccent : const Color(0xFF2196F3),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );

      // تهيئة Firebase أولاً (مع التحقق من عدم التهيئة المسبقة)
      if (!kIsWeb) {
        try {
          // التحقق من وجود Firebase apps مُهيأة مسبقاً
          if (Firebase.apps.isEmpty) {
            await Firebase.initializeApp(
              options: DefaultFirebaseOptions.currentPlatform,
            );
            log('✅ تم تهيئة Firebase بنجاح');
          } else {
            log('ℹ️ Firebase مُهيأ مسبقاً');
          }
        } catch (e) {
          if (e.toString().contains('duplicate-app')) {
            log('ℹ️ Firebase مُهيأ مسبقاً - تجاهل الخطأ');
          } else {
            log('❌ خطأ في تهيئة Firebase: $e');
            rethrow;
          }
        }
      }

      // تطبيق تحسينات الأداء
      await PerformanceUtils.applyAllOptimizations();

      // تهيئة Store Configuration
      _initializeStore();

      // تهيئة RevenueCat SDK
      await _configureSDK();

      // تهيئة التطبيق الرئيسي
      final app = await initializeApp();

      // تشغيل التطبيق
      runApp(app);
    } catch (e, stack) {
      log('Error during initialization: $e');
      log('Stack trace: $stack');
      _handleStartupError(e, stack);
    }
  }, (error, stack) {
    log('Uncaught error: $error');
    log('Stack trace: $stack');
  });
}

void _initializeStore() {
  try {
    String apiKey;
    Store store;

    if (Platform.isIOS || Platform.isMacOS) {
      apiKey = appleApiKey;
      store = Store.appStore;
      log('🍎 تهيئة متجر Apple مع API Key: ${apiKey.substring(0, 10)}...');
    } else if (Platform.isAndroid) {
      const useAmazon = bool.fromEnvironment("amazon");
      apiKey = useAmazon ? googleApiKey : googleApiKey;
      store = useAmazon ? Store.amazon : Store.playStore;
      log('🤖 تهيئة متجر ${useAmazon ? "Amazon" : "Google Play"} مع API Key: ${apiKey.substring(0, 10)}...');
    } else {
      log('⚠️ منصة غير مدعومة للمتجر');
      return;
    }

    // التحقق من صحة API Key
    if (apiKey.isEmpty) {
      log('❌ خطأ: API Key فارغ للمنصة ${Platform.operatingSystem}');
      return;
    }

    // إنشاء StoreConfig
    StoreConfig(store: store, apiKey: apiKey);
    log('✅ تم تهيئة StoreConfig بنجاح');
  } catch (e, stack) {
    log('❌ خطأ في تهيئة Store Config: $e');
    log('Stack trace: $stack');
  }
}

void _handleStartupError(Object error, StackTrace stackTrace) {
  runApp(MaterialApp(
    debugShowCheckedModeBanner: false,
    theme: ThemeData(
      brightness: Brightness.light,
      primarySwatch: Colors.red,
    ),
    darkTheme: ThemeData(
      brightness: Brightness.dark,
      primarySwatch: Colors.red,
    ),
    themeMode: ThemeMode.system,
    home: Builder(
      builder: (context) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return Scaffold(
          backgroundColor: isDark ? const Color(0xFF121212) : Colors.white,
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isDark
                    ? [
                        const Color(0xFF2C1B1B),
                        const Color(0xFF121212),
                        const Color(0xFF3C2020),
                      ]
                    : [
                        const Color(0xFFFFEBEE),
                        Colors.white,
                        const Color(0xFFFFCDD2),
                      ],
              ),
            ),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: isDark ? Colors.redAccent : Colors.red,
                      size: 64,
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'عذراً، حدث خطأ غير متوقع',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.redAccent : Colors.red,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'يرجى إعادة تشغيل التطبيق',
                      style: TextStyle(
                        fontSize: 16,
                        color: isDark ? Colors.grey[400] : Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isDark ? Colors.grey[800] : Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        error.toString(),
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? Colors.grey[400] : Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    ),
  ));
}

Future<void> _configureSDK() async {
  try {
    // التحقق من تهيئة StoreConfig أولاً
    if (!StoreConfig.isInitialized) {
      log('❌ خطأ: StoreConfig لم يتم تهيئته');
      return;
    }

    // التحقق من وجود API Key
    if (StoreConfig.instance.apiKey.isEmpty) {
      log('❌ خطأ: API Key فارغ');
      return;
    }

    await Purchases.setLogLevel(LogLevel.verbose);

    log('🔄 بدء تهيئة RevenueCat...');
    log('🔑 API Key: ${StoreConfig.instance.apiKey}');
    log('🏪 Store: ${StoreConfig.instance.store}');

    // التكوين البسيط - الإصدار الجديد
    final configuration = PurchasesConfiguration(StoreConfig.instance.apiKey);
    await Purchases.configure(configuration);

    log('✅ تم تهيئة RevenueCat بنجاح');

    if (Platform.isIOS) {
      log('📱 جهاز iOS - جاري مزامنة المشتريات...');
      await Purchases.syncPurchases();
      log('✅ تم مزامنة المشتريات بنجاح');
    }
  } catch (e, stack) {
    log('❌ خطأ في تهيئة RevenueCat: $e');
    log('Stack trace: $stack');
    // لا نرمي الخطأ هنا لأن التطبيق يمكن أن يعمل بدون RevenueCat
  }
}
