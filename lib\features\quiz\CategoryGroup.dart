import 'package:flutterquiz/features/quiz/models/category.dart';

class CategoryGroup {
  final String groupName;
  final String groupIcon;
  final List<Category> categories;
  final int totalQuestions;

  const CategoryGroup({
    required this.groupName,
    required this.groupIcon,
    required this.categories,
    required this.totalQuestions,
  });

  CategoryGroup copyWith({
    String? groupName,
    String? groupIcon,
    List<Category>? categories,
    int? totalQuestions,
  }) {
    return CategoryGroup(
      groupName: groupName ?? this.groupName,
      groupIcon: groupIcon ?? this.groupIcon,
      categories: categories ?? this.categories,
      totalQuestions: totalQuestions ?? this.totalQuestions,
    );
  }
}

class CategoryClassifier {
  static const Map<String, String> _groupIcons = {
    'القدرات': '🧠',
    'التحصيلي': '📊',
    'الرخصة المهنية': '📜',
    'اختبارات الإنجليزي': '🇺🇸',
    'القدرة المعرفية وقدرات الجامعيين': '🎓',
    'أخرى': '📚',
  };

  static String classifyCategory(String categoryName) {
    final lowerName = categoryName.toLowerCase();

    // القدرة المعرفية وقدرات الجامعيين (القدرة المعرفية أو قدرات الجامعيين)
    if (lowerName.contains('القدرة المعرفية') ||
        lowerName.contains('قدرات الجامعيين')) {
      return 'القدرة المعرفية وقدرات الجامعيين';
    }

    // الرخصة المهنية
    if (lowerName.contains('رخصة مهنية') ||
        lowerName.contains('الرخصة المهنية') ||
        lowerName.contains('رخص مهنية')) {
      return 'الرخصة المهنية';
    }

    // القدرات (عدا قدرات الجامعيين - تم التحقق منها أعلاه)
    if (lowerName.contains('قدرات') && !lowerName.contains('قدرات الجامعيين')) {
      return 'القدرات';
    }

    // التحصيلي
    if (lowerName.contains('التحصيلي') || lowerName.contains('تحصيلي')) {
      return 'التحصيلي';
    }

    // اختبارات الإنجليزي (أي كلمة أو رمز إنجليزي)
    final englishPattern = RegExp(r'[a-zA-Z]');
    if (englishPattern.hasMatch(categoryName) ||
        lowerName.contains('انجليزي') ||
        lowerName.contains('انجليزية') ||
        lowerName.contains('إنجليزي') ||
        lowerName.contains('إنجليزية')) {
      return 'اختبارات الإنجليزي';
    }

    // أخرى (أي فئة لا تنطبق عليها الشروط أعلاه)
    return 'أخرى';
  }

  /// تصنيف الفئات إلى مجموعات بناءً على الكلمات المفتاحية
  static List<CategoryGroup> classifyCategories(List<Category> categories) {
    final Map<String, List<Category>> groupedCategories = {};
    final List<Category> uncategorizedCategories = [];

    // تهيئة المجموعات
    for (final groupName in _groupIcons.keys) {
      groupedCategories[groupName] = [];
    }

    // تصنيف الفئات
    for (final category in categories) {
      final categoryName = category.categoryName ?? '';
      final groupName = classifyCategory(categoryName);

      if (groupedCategories.containsKey(groupName)) {
        groupedCategories[groupName]!.add(category);
      } else {
        uncategorizedCategories.add(category);
      }
    }

    // إنشاء قائمة المجموعات النهائية
    final List<CategoryGroup> result = [];

    // إضافة المجموعات التي تحتوي على فئات
    for (final entry in groupedCategories.entries) {
      final groupName = entry.key;
      final groupCategories = entry.value;

      if (groupCategories.isNotEmpty) {
        final totalQuestions = groupCategories.fold<int>(
          0,
          (sum, category) => sum + int.parse(category.noOfQues ?? '0'),
        );

        result.add(CategoryGroup(
          groupName: groupName,
          groupIcon: _groupIcons[groupName] ?? '📚',
          categories: groupCategories,
          totalQuestions: totalQuestions,
        ));
      }
    }

    // إضافة مجموعة "أخرى" إذا كانت هناك فئات غير مصنفة
    if (uncategorizedCategories.isNotEmpty) {
      final totalQuestions = uncategorizedCategories.fold<int>(
        0,
        (sum, category) => sum + int.parse(category.noOfQues ?? '0'),
      );

      result.add(CategoryGroup(
        groupName: 'أخرى',
        groupIcon: _groupIcons['أخرى']!,
        categories: uncategorizedCategories,
        totalQuestions: totalQuestions,
      ));
    }

    return result;
  }

  /// دمج الفئات المتشابهة (مثل قدرات جامعيين مع القدرة المعرفية)
  static List<CategoryGroup> mergeRelatedCategories(
      List<CategoryGroup> groups) {
    final List<CategoryGroup> mergedGroups = [];
    final Set<String> processedGroups = {};

    for (final group in groups) {
      if (processedGroups.contains(group.groupName)) {
        continue;
      }

      // البحث عن مجموعات مشابهة للدمج
      final List<Category> mergedCategories = List.from(group.categories);
      int totalQuestions = group.totalQuestions;

      // دمج المجموعات المتشابهة
      for (final otherGroup in groups) {
        if (otherGroup.groupName != group.groupName &&
            !processedGroups.contains(otherGroup.groupName) &&
            _shouldMergeGroups(group.groupName, otherGroup.groupName)) {
          mergedCategories.addAll(otherGroup.categories);
          totalQuestions += otherGroup.totalQuestions;
          processedGroups.add(otherGroup.groupName);
        }
      }

      mergedGroups.add(CategoryGroup(
        groupName: group.groupName,
        groupIcon: group.groupIcon,
        categories: mergedCategories,
        totalQuestions: totalQuestions,
      ));

      processedGroups.add(group.groupName);
    }

    return mergedGroups;
  }

  /// تحديد ما إذا كان يجب دمج مجموعتين
  static bool _shouldMergeGroups(String group1, String group2) {
    // قواعد الدمج المخصصة
    const mergeRules = {
      'قدرات': ['القدرة المعرفية'],
      'الرخصة المهنية': [],
    };

    return mergeRules[group1]?.contains(group2) == true ||
        mergeRules[group2]?.contains(group1) == true;
  }
}
