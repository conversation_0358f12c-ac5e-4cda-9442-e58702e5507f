import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/screens/battle/voice_chat_controller.dart';

/// مزود المحادثة الصوتية للمعارك
/// يستخدم لمشاركة وحدة تحكم المحادثة الصوتية بين جميع الشاشات
class BattleVoiceChatProvider extends InheritedWidget {
  final VoiceChatController controller;

  const BattleVoiceChatProvider({
    super.key,
    required this.controller,
    required super.child,
  });

  /// الحصول على وحدة تحكم المحادثة الصوتية من السياق
  static BattleVoiceChatProvider? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<BattleVoiceChatProvider>();
  }

  /// الحصول على وحدة تحكم المحادثة الصوتية من السياق بشكل آمن
  /// يرجع وحدة تحكم المحادثة الصوتية إذا كانت موجودة، وإلا يطبع رسالة خطأ
  static VoiceChatController? getControllerSafely(BuildContext context) {
    final provider = of(context);
    if (provider == null) {
      debugPrint("تحذير: لم يتم العثور على مزود المحادثة الصوتية في شجرة الويدجت");
      return null;
    }
    return provider.controller;
  }

  /// تهيئة المحادثة الصوتية والانضمام إلى القناة
  static Future<bool> initializeAndJoinChannel(
    BuildContext context,
    String channelId,
    int uid,
  ) async {
    final controller = getControllerSafely(context);
    if (controller == null) {
      debugPrint("خطأ: لا يمكن تهيئة المحادثة الصوتية، مزود المحادثة الصوتية غير موجود");
      return false;
    }

    // تهيئة المحادثة الصوتية
    final initialized = await controller.initialize();
    if (!initialized) {
      debugPrint("خطأ: فشل في تهيئة المحادثة الصوتية");
      return false;
    }

    // الانضمام إلى القناة
    final joined = await controller.joinChannel(channelId, uid);
    if (!joined) {
      debugPrint("خطأ: فشل في الانضمام إلى القناة الصوتية");
      return false;
    }

    debugPrint("تم تهيئة المحادثة الصوتية والانضمام إلى القناة بنجاح");
    return true;
  }

  @override
  bool updateShouldNotify(BattleVoiceChatProvider oldWidget) {
    return controller != oldWidget.controller;
  }
}
