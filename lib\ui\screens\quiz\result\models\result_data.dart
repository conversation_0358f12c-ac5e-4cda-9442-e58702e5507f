import 'package:flutterquiz/features/battleRoom/models/battleRoom.dart';
import 'package:flutterquiz/features/exam/models/exam.dart';
import 'package:flutterquiz/features/quiz/models/comprehension.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';

/// نموذج بيانات شامل لجميع أنواع النتائج
class ResultData {
  // معلومات أساسية
  final QuizTypes quizType;
  final int numberOfPlayers;
  final List<Question>? questions;
  final bool isPlayed;
  final bool isPremiumCategory;

  // معلومات النقاط والنتائج
  final int? myPoints;
  final double? timeTakenToCompleteQuiz;
  final bool? hasUsedAnyLifeline;

  // معلومات المعارك
  final BattleRoom? battleRoom;
  final bool playWithBot;

  // معلومات الامتحانات
  final Exam? exam;
  final int? obtainedMarks;
  final int? examCompletedInMinutes;
  final int? correctExamAnswers;
  final int? incorrectExamAnswers;

  // معلومات المستويات والفئات
  final String? categoryId;
  final String? subcategoryId;
  final String? subcategoryMaxLevel;
  final int? unlockedLevel;

  // معلومات المسابقات
  final String? contestId;
  final int? entryFee;

  // معلومات الفهم
  final Comprehension comprehension;

  const ResultData({
    required this.quizType,
    required this.numberOfPlayers,
    required this.isPlayed,
    required this.isPremiumCategory,
    required this.comprehension,
    this.questions,
    this.myPoints,
    this.timeTakenToCompleteQuiz,
    this.hasUsedAnyLifeline,
    this.battleRoom,
    this.playWithBot = false,
    this.exam,
    this.obtainedMarks,
    this.examCompletedInMinutes,
    this.correctExamAnswers,
    this.incorrectExamAnswers,
    this.categoryId,
    this.subcategoryId,
    this.subcategoryMaxLevel,
    this.unlockedLevel,
    this.contestId,
    this.entryFee,
  });

  /// إنشاء ResultData من arguments الشاشة
  factory ResultData.fromArguments(Map<String, dynamic> args) {
    final questions = args['questions'] as List<Question>?;
    final quizType = args['quizType'] as QuizTypes;

    // طباعة معلومات تشخيصية
    print('ResultData.fromArguments: quizType = $quizType');
    print(
        'ResultData.fromArguments: questions = ${questions?.length ?? 'null'}');

    return ResultData(
      quizType: quizType,
      numberOfPlayers: args['numberOfPlayer'] as int? ?? 1,
      isPlayed: args['isPlayed'] as bool? ?? true,
      isPremiumCategory: args['isPremiumCategory'] as bool? ?? false,
      comprehension:
          args['comprehension'] as Comprehension? ?? Comprehension.empty(),
      questions: questions,
      myPoints: args['myPoints'] as int?,
      timeTakenToCompleteQuiz: args['timeTakenToCompleteQuiz'] as double?,
      hasUsedAnyLifeline: args['hasUsedAnyLifeline'] as bool?,
      battleRoom: args['battleRoom'] as BattleRoom?,
      playWithBot: args['play_with_bot'] as bool? ?? false,
      exam: args['exam'] as Exam?,
      obtainedMarks: args['obtainedMarks'] as int?,
      examCompletedInMinutes: args['examCompletedInMinutes'] as int?,
      correctExamAnswers: args['correctExamAnswers'] as int?,
      incorrectExamAnswers: args['incorrectExamAnswers'] as int?,
      categoryId: args['categoryId'] as String?,
      subcategoryId: args['subcategoryId'] as String?,
      subcategoryMaxLevel: args['subcategoryMaxLevel'] as String?,
      unlockedLevel: args['unlockedLevel'] as int?,
      contestId: args['contestId'] as String?,
      entryFee: args['entryFee'] as int?,
    );
  }

  /// حساب عدد الإجابات الصحيحة (سيتم حسابها في ResultCubit)
  int get correctAnswers {
    // هذه دالة مؤقتة - سيتم حساب الإجابات الصحيحة في ResultCubit
    // باستخدام AnswerEncryption.decryptCorrectAnswer()
    return 0;
  }

  /// حساب عدد الأسئلة المحاولة
  int get attemptedQuestions {
    if (questions == null) return 0;
    return questions!.where((question) {
      return question.submittedAnswerId.isNotEmpty &&
          question.submittedAnswerId != "0";
    }).length;
  }

  /// حساب إجمالي الأسئلة
  int get totalQuestions {
    return questions?.length ?? 0;
  }

  /// حساب نسبة النجاح (معطل - سيتم حسابها في ResultCubit)
  double get winPercentage {
    // هذه دالة مؤقتة - سيتم حساب النسبة في ResultCubit
    return 0.0;
  }

  /// تحديد ما إذا كان المستخدم فائزاً (معطل - سيتم حسابها في ResultCubit)
  bool isWinner(double requiredPercentage) {
    // هذه دالة مؤقتة - سيتم حساب الفوز في ResultCubit
    return false;
  }

  /// تحديد ما إذا كان المستخدم تخطى أسئلة في QuizZone
  bool get didSkipQuestions {
    return quizType == QuizTypes.quizZone &&
        questions != null &&
        questions!.any((e) => e.submittedAnswerId == '0');
  }

  /// الحصول على معرف فئة السؤال
  String getCategoryId() {
    if (questions != null && questions!.isNotEmpty) {
      return questions!.first.categoryId ?? '';
    }
    return categoryId ?? '';
  }

  /// نسخ البيانات مع تعديل بعض القيم
  ResultData copyWith({
    QuizTypes? quizType,
    int? numberOfPlayers,
    List<Question>? questions,
    bool? isPlayed,
    bool? isPremiumCategory,
    int? myPoints,
    double? timeTakenToCompleteQuiz,
    bool? hasUsedAnyLifeline,
    BattleRoom? battleRoom,
    bool? playWithBot,
    Exam? exam,
    int? obtainedMarks,
    int? examCompletedInMinutes,
    int? correctExamAnswers,
    int? incorrectExamAnswers,
    String? categoryId,
    String? subcategoryId,
    String? subcategoryMaxLevel,
    int? unlockedLevel,
    String? contestId,
    int? entryFee,
    Comprehension? comprehension,
  }) {
    return ResultData(
      quizType: quizType ?? this.quizType,
      numberOfPlayers: numberOfPlayers ?? this.numberOfPlayers,
      questions: questions ?? this.questions,
      isPlayed: isPlayed ?? this.isPlayed,
      isPremiumCategory: isPremiumCategory ?? this.isPremiumCategory,
      myPoints: myPoints ?? this.myPoints,
      timeTakenToCompleteQuiz:
          timeTakenToCompleteQuiz ?? this.timeTakenToCompleteQuiz,
      hasUsedAnyLifeline: hasUsedAnyLifeline ?? this.hasUsedAnyLifeline,
      battleRoom: battleRoom ?? this.battleRoom,
      playWithBot: playWithBot ?? this.playWithBot,
      exam: exam ?? this.exam,
      obtainedMarks: obtainedMarks ?? this.obtainedMarks,
      examCompletedInMinutes:
          examCompletedInMinutes ?? this.examCompletedInMinutes,
      correctExamAnswers: correctExamAnswers ?? this.correctExamAnswers,
      incorrectExamAnswers: incorrectExamAnswers ?? this.incorrectExamAnswers,
      categoryId: categoryId ?? this.categoryId,
      subcategoryId: subcategoryId ?? this.subcategoryId,
      subcategoryMaxLevel: subcategoryMaxLevel ?? this.subcategoryMaxLevel,
      unlockedLevel: unlockedLevel ?? this.unlockedLevel,
      contestId: contestId ?? this.contestId,
      entryFee: entryFee ?? this.entryFee,
      comprehension: comprehension ?? this.comprehension,
    );
  }
}
