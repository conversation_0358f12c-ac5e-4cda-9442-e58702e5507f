import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/ui/widgets/all.dart';
import 'package:flutterquiz/utils/constants/assets_constants.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/extensions.dart';
class IntroSliderScreen extends StatefulWidget {
  const IntroSliderScreen({super.key});

  @override
  State<IntroSliderScreen> createState() => _GettingStartedScreenState();
}

class _GettingStartedScreenState extends State<IntroSliderScreen>
    with TickerProviderStateMixin {
  int sliderIndex = 0;
  late PageController _pageController;

  late AnimationController buttonController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 400),
  );
  late Animation<double> buttonSqueezeAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: buttonController,
      curve: Curves.easeInOut,
    ),
  );

  late AnimationController imageSlideAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 2000),
  )..repeat(reverse: true);

  late Animation<Offset> imageSlideAnimation =
      Tween<Offset>(begin: Offset.zero, end: const Offset(0, -0.035)).animate(
    CurvedAnimation(
      parent: imageSlideAnimationController,
      curve: Curves.easeInOut,
    ),
  );

  late final List<({String image, String title, String desc})> slideList = [
    (
      image: Assets.onboardingA,
      title: context.tr('title1')!,
      desc: context.tr('description1')!,
    ),
    (
      image: Assets.onboardingB,
      title: context.tr('title2')!,
      desc: context.tr('description2')!,
    ),
    (
      image: Assets.onboardingC,
      title: context.tr('title3')!,
      desc: context.tr('description3')!,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    buttonController.forward();
  }

  @override
  void dispose() {
    buttonController.dispose();
    imageSlideAnimationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void onPageChanged(int index) => setState(() {
        sliderIndex = index;
      });

  Widget _buildPageIndicatorNew() {
    const indicatorWidth = 10.0;
    const indicatorHeight = 10.0;
    const selectedIndicatorWidth = 10.0 * 3;
    final borderRadius = BorderRadius.circular(5);
    final secondaryColor = Theme.of(context).primaryColor;
    const duration = Duration(milliseconds: 300);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (int i = 0; i < slideList.length; i++) ...[
          if (i > 0) const SizedBox(width: 8),
          AnimatedContainer(
            duration: duration,
            height: indicatorHeight,
            width: sliderIndex == i ? selectedIndicatorWidth : indicatorWidth,
            decoration: BoxDecoration(
              color: sliderIndex == i
                  ? secondaryColor
                  : secondaryColor.withOpacity(0.3),
              borderRadius: borderRadius,
              boxShadow: sliderIndex == i
                  ? [
                      BoxShadow(
                        color: secondaryColor.withOpacity(0.3),
                        blurRadius: 8,
                        spreadRadius: 2,
                      )
                    ]
                  : null,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildIntroSlider() {
    final theme = Theme.of(context);
    return PageView.builder(
      controller: _pageController,
      physics: const BouncingScrollPhysics(),
      onPageChanged: onPageChanged,
      itemBuilder: (context, index) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SlideTransition(
              position: imageSlideAnimation,
              child: Container(
                height: MediaQuery.of(context).size.height * (0.45),
                width: MediaQuery.of(context).size.width * 0.8,
                alignment: Alignment.center,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: theme.primaryColor.withOpacity(0.2),
                            blurRadius: 50,
                            spreadRadius: 20,
                          ),
                        ],
                      ),
                    ),
                    QImage(
                      imageUrl: slideList[index].image,
                      padding: EdgeInsets.zero,
                      width: MediaQuery.of(context).size.width * 0.7,
                      height: MediaQuery.of(context).size.height * 0.4,
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: MediaQuery.of(context).size.height * .03),
            ShaderMask(
              shaderCallback: (bounds) => LinearGradient(
                colors: [
                  theme.primaryColor,
                  theme.primaryColor.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ).createShader(bounds),
              child: Text(
                slideList[index].title,
                textAlign: TextAlign.center,
                maxLines: 1,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  height: 1.2,
                  fontWeight: FontWeights.bold,
                  letterSpacing: 1,
                ),
              ),
            ),
            SizedBox(height: MediaQuery.of(context).size.height * .025),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                slideList[index].desc,
                textAlign: TextAlign.center,
                maxLines: 3,
                style: TextStyle(
                  color: theme.colorScheme.onTertiary.withOpacity(0.8),
                  fontWeight: FontWeights.medium,
                  fontSize: 16,
                  height: 1.5,
                  letterSpacing: 0.5,
                ),
              ),
            ),
          ],
        );
      },
      itemCount: slideList.length,
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final theme = Theme.of(context);

    return Scaffold(
      body: Stack(
        children: [
          Container(
            height: size.height,
            width: size.width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.scaffoldBackgroundColor,
                  theme.scaffoldBackgroundColor.withOpacity(0.8),
                  theme.scaffoldBackgroundColor.withOpacity(0.9),
                ],
              ),
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                Expanded(
                  flex: 4,
                  child: _buildIntroSlider(),
                ),
                Expanded(
                  child: Column(
                    children: [
                      _buildPageIndicatorNew(),
                      const Spacer(),
                      Padding(
                        padding: EdgeInsets.only(
                          bottom: size.height * 0.05,
                          left: 20,
                          right: 20,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextButton(
                              onPressed: () {
                                context.read<SettingsCubit>().changeShowIntroSlider();
                                Navigator.of(context).pushReplacementNamed(
                                  Routes.home,
                                  arguments: true,
                                );
                              },
                              child: Text(
                                context.tr('skip')!,
                                style: TextStyle(
                                  color: theme.primaryColor.withOpacity(0.7),
                                  fontSize: 16,
                                  fontWeight: FontWeights.medium,
                                ),
                              ),
                            ),
                            AnimatedBuilder(
                              animation: buttonSqueezeAnimation,
                              builder: (_, child) => Transform.scale(
                                scale: 0.8 + buttonSqueezeAnimation.value * 0.2,
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(30),
                                    boxShadow: [
                                      BoxShadow(
                                        color: theme.primaryColor.withOpacity(0.3),
                                        blurRadius: 20,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: theme.primaryColor,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 32,
                                        vertical: 16,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                    ),
                                    onPressed: () {
                                      if (sliderIndex < slideList.length - 1) {
                                        _pageController.nextPage(
                                          duration:
                                              const Duration(milliseconds: 500),
                                          curve: Curves.easeInOut,
                                        );
                                      } else {
                                        context
                                            .read<SettingsCubit>()
                                            .changeShowIntroSlider();
                                        Navigator.of(context).pushReplacementNamed(
                                          Routes.home,
                                          arguments: true,
                                        );
                                      }
                                    },
                                    child: Text(
                                      sliderIndex == slideList.length - 1
                                          ? context.tr('getStarted')!
                                          : context.tr('next')!,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeights.semiBold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
