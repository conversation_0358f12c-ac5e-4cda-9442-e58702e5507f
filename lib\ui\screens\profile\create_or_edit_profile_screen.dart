import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'dart:developer';

import 'package:camera/camera.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/authLocalDataSource.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/auth/cubits/referAndEarnCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateUserDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/uploadProfileCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/models/userProfile.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/widgets/all.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/image_compression_service.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:flutterquiz/utils/validators.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

class CreateOrEditProfileScreen extends StatefulWidget {
  const CreateOrEditProfileScreen({
    required this.isNewUser,
    super.key,
  });

  final bool isNewUser;

  @override
  State<CreateOrEditProfileScreen> createState() =>
      _SelectProfilePictureScreen();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<UploadProfileCubit>(
            create: (_) => UploadProfileCubit(ProfileManagementRepository()),
          ),
          BlocProvider<ReferAndEarnCubit>(
            create: (_) => ReferAndEarnCubit(AuthRepository()),
          ),
          BlocProvider<UpdateUserDetailCubit>(
            create: (_) => UpdateUserDetailCubit(ProfileManagementRepository()),
          ),
        ],
        child: CreateOrEditProfileScreen(
          isNewUser: routeSettings.arguments! as bool,
        ),
      ),
    );
  }
}

class _SelectProfilePictureScreen extends State<CreateOrEditProfileScreen> {
  File? selectedImage;
  String? selectedAvatar;

  TextEditingController? nameController;
  TextEditingController? emailController;
  TextEditingController? phoneController;
  TextEditingController inviteTextEditingController = TextEditingController();
  bool iHaveInviteCode = false;

  bool isPhoneTextFieldEnabled = false;
  bool isEmailTextFieldEnabled = false;

  @override
  void initState() {
    super.initState();
    final authType = AuthLocalDataSource.getAuthType();
    if (!widget.isNewUser) {
      if (authType == 'mobile') {
        isEmailTextFieldEnabled = true;
        isPhoneTextFieldEnabled = false;
      } else if (authType == 'gmail' ||
          authType == 'email' ||
          authType == 'apple') {
        isEmailTextFieldEnabled = false;
        isPhoneTextFieldEnabled = true;
      }
    }
  }

  //convert image to file
  Future<void> uploadProfileImage(String imageName) async {
    try {
      final byteData = await rootBundle.load(Assets.profile(imageName));
      final file = File('${(await getTemporaryDirectory()).path}/temp.png');
      await file.writeAsBytes(
        byteData.buffer
            .asUint8List(byteData.offsetInBytes, byteData.lengthInBytes),
      );

      // التحقق من صحة الصورة وضغطها قبل رفعها
      final result = await ImageCompressionService.validateAndCompressImage(file);

      // إذا كانت الصورة غير صالحة، نعرض رسالة خطأ
      if (!result['isValid']) {
        if (mounted) {
          UiUtils.showSnackBar(
            result['message'],
            context,
          );
        }
        return;
      }

      // استخدام الصورة المضغوطة
      final compressedImage = result['file'] as File;

      if (mounted) {
        await context.read<UploadProfileCubit>().uploadProfilePicture(compressedImage);
      }
    } catch (e) {
      log('Error in uploadProfileImage: $e');
      if (mounted) {
        UiUtils.showSnackBar(
          'حدث خطأ أثناء رفع الصورة',
          context,
        );
      }
    }
  }

  Widget _buildCurrentProfilePictureContainer({
    required String image,
    required bool isFile,
    required bool isAsset,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final width = MediaQuery.of(context).size.width;
    final isTablet = width >= 600;

    if (image.isEmpty) {
      if (widget.isNewUser) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: width * 0.1),
          child: DottedBorder(
            options: RoundedRectDottedBorderOptions(
              radius: const Radius.circular(12),
              strokeWidth: 2,
              padding: EdgeInsets.zero,
              dashPattern: const [8, 4],
              color: colorScheme.primary.withValues(alpha: 0.5),
            ),
            child: TextButton(
              style: TextButton.styleFrom(
                backgroundColor: colorScheme.surface,
                padding: EdgeInsets.symmetric(
                  vertical: isTablet ? 20 : 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: chooseImageFromCameraOrGallery,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add_a_photo_outlined,
                    color: colorScheme.primary,
                    size: isTablet ? 28 : 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    context.tr('choosePhoto')!,
                    style: TextStyle(
                      fontSize: isTablet ? 18 : 16,
                      fontWeight: FontWeights.medium,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
      return GestureDetector(
        onTap: chooseImageFromCameraOrGallery,
        child: Container(
          width: width * (isTablet ? 0.25 : 0.3),
          height: width * (isTablet ? 0.25 : 0.3),
          decoration: BoxDecoration(
            color: colorScheme.primary.withOpacity(0.1),
            shape: BoxShape.circle,
            border: Border.all(
              color: colorScheme.primary.withOpacity(0.2),
              width: 2,
            ),
          ),
          child: Center(
            child: Icon(
              Icons.add_a_photo_outlined,
              color: colorScheme.primary,
              size: isTablet ? 40 : 32,
            ),
          ),
        ),
      );
    }

    return Container(
      width: width * (isTablet ? 0.25 : 0.3),
      height: width * (isTablet ? 0.25 : 0.3),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            width: width * (isTablet ? 0.25 : 0.3),
            height: width * (isTablet ? 0.25 : 0.3),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: colorScheme.primary.withOpacity(0.2),
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(width * 0.15),
              child: isFile
                  ? Image.file(
                      File(image),
                      fit: BoxFit.cover,
                    )
                  : QImage.circular(
                      imageUrl: isAsset ? Assets.profile(image) : image,
                      fit: BoxFit.cover,
                    ),
            ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: GestureDetector(
              onTap: chooseImageFromCameraOrGallery,
              child: Container(
                padding: EdgeInsets.all(isTablet ? 12 : 8),
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: colorScheme.primary.withOpacity(0.1),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.edit_outlined,
                  color: colorScheme.primary,
                  size: isTablet ? 24 : 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // get image File camera
  Future<void> _getFromCamera(BuildContext context) async {
    try {
      // التحقق من توفر الكاميرا قبل محاولة استخدامها
      List<CameraDescription> cameras = [];
      try {
        cameras = await availableCameras();
      } catch (e) {
        log('Error getting cameras: $e');
      }

      if (cameras.isEmpty) {
        if (mounted) {
          UiUtils.showSnackBar(
            'لا توجد كاميرا متاحة. يرجى استخدام معرض الصور بدلاً من ذلك.',
            context,
          );
        }
        // استخدام معرض الصور بدلاً من الكاميرا
        await _getFromGallery(context);
        return;
      }

      final pickedFile = await ImagePicker().pickImage(
        source: ImageSource.camera,
        maxWidth: 1800,
        maxHeight: 1800,
      );

      if (pickedFile == null) return;

      // عرض مؤشر التحميل
      if (mounted) {
        UiUtils.showSnackBar(
          'جاري معالجة الصورة...',
          context,
        );
      }

      final croppedFile = await _croppedImage(pickedFile.path);
      if (croppedFile == null) return;

      // التحقق من صحة الصورة وضغطها
      final result = await ImageCompressionService.validateAndCompressImage(
        File(croppedFile.path),
      );

      // إذا كانت الصورة غير صالحة، نعرض رسالة خطأ
      if (!result['isValid']) {
        if (mounted) {
          UiUtils.showSnackBar(
            result['message'],
            context,
          );
        }
        return;
      }

      // استخدام الصورة المضغوطة
      final compressedImage = result['file'] as File;

      if (mounted) {
        setState(() {
          selectedImage = compressedImage;
          selectedAvatar = null;
        });
      }
    } catch (e) {
      log('Error in _getFromCamera: $e');
      if (mounted) {
        // عرض رسالة خطأ للمستخدم
        UiUtils.showSnackBar(
          'حدث خطأ أثناء التقاط الصورة. يرجى المحاولة مرة أخرى أو استخدام معرض الصور.',
          context,
        );

        // محاولة استخدام معرض الصور بدلاً من الكاميرا
        try {
          if (mounted) {
            await _getFromGallery(context);
          }
        } catch (galleryError) {
          log('Error in fallback to gallery: $galleryError');
        }
      }
    }
  }

  //get image file from library
  Future<void> _getFromGallery(BuildContext context) async {
    try {
      final pickedFile = await ImagePicker().pickImage(
        source: ImageSource.gallery,
        maxWidth: 1800,
        maxHeight: 1800,
      );

      if (pickedFile == null) return;

      // عرض مؤشر التحميل
      if (mounted) {
        UiUtils.showSnackBar(
          'جاري معالجة الصورة...',
          context,
        );
      }

      final croppedFile = await _croppedImage(pickedFile.path);
      if (croppedFile == null) return;

      // التحقق من صحة الصورة وضغطها
      final result = await ImageCompressionService.validateAndCompressImage(
        File(croppedFile.path),
      );

      // إذا كانت الصورة غير صالحة، نعرض رسالة خطأ
      if (!result['isValid']) {
        if (mounted) {
          UiUtils.showSnackBar(
            result['message'],
            context,
          );
        }
        return;
      }

      // استخدام الصورة المضغوطة
      final compressedImage = result['file'] as File;

      if (mounted) {
        setState(() {
          selectedImage = compressedImage;
          selectedAvatar = null;
        });
      }
    } catch (e) {
      log('Error in _getFromGallery: $e');
      if (mounted) {
        UiUtils.showSnackBar(
          'حدث خطأ أثناء معالجة الصورة',
          context,
        );
      }
    }
  }

  Future<CroppedFile?> _croppedImage(String pickedFilePath) async {
    final title = context.tr('cropperLbl');

    return ImageCropper().cropImage(
      sourcePath: pickedFilePath,
      compressFormat: ImageCompressFormat.png,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: title,
          toolbarColor: Theme.of(context).primaryColor,
          toolbarWidgetColor: Theme.of(context).colorScheme.surface,
          initAspectRatio: CropAspectRatioPreset.square,
          activeControlsWidgetColor: Theme.of(context).primaryColor,
          cropStyle: CropStyle.circle,
          aspectRatioPresets: [CropAspectRatioPreset.square],
        ),
        IOSUiSettings(
          title: title,
          cropStyle: CropStyle.circle,
          aspectRatioPresets: [CropAspectRatioPreset.square],
        ),
      ],
    );
  }

  void chooseImageFromCameraOrGallery() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: UiUtils.bottomSheetTopRadius,
      ),
      builder: (_) {
        final size = MediaQuery.sizeOf(context);
        final isTablet = size.width >= 600;

        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: UiUtils.bottomSheetTopRadius,
          ),
          padding: EdgeInsets.symmetric(
            vertical: size.height * .02,
            horizontal: size.width * UiUtils.hzMarginPct,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.tr('profilePhotoLbl')!,
                style: TextStyle(
                  fontSize: isTablet ? 20 : 18,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onTertiary,
                ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildOptionButton(
                    icon: Icons.camera_alt,
                    label: context.tr('cameraLbl')!,
                    onTap: () {
                      _getFromCamera(context);
                      Navigator.pop(context);
                    },
                  ),
                  _buildOptionButton(
                    icon: Icons.photo_library_rounded,
                    label: context.tr('photoLibraryLbl')!,
                    onTap: () {
                      _getFromGallery(context);
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
              SizedBox(height: size.height * .02),
            ],
          ),
        );
      },
    );
  }

  Widget _buildOptionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final isTablet = MediaQuery.of(context).size.width >= 600;
    final size = isTablet ? 60.0 : 50.0;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(size / 2),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.2),
              ),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: isTablet ? 32 : 28,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            color: Theme.of(context).colorScheme.onTertiary,
          ),
        ),
      ],
    );
  }

  Widget _buildSelectAvatarText() {
    return Center(
      child: Text(
        "${toBeginningOfSentenceCase(context.tr("orLbl"))} ${context.tr("selectProfilePhotoLbl")!}",
        style: TextStyle(
          color: Theme.of(context).colorScheme.onTertiary,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDefaultAvtarImage(int index, String imageName) {
    final colorScheme = Theme.of(context).colorScheme;
    final isTablet = MediaQuery.of(context).size.width >= 600;
    final isSelected = selectedAvatar == imageName;
    final size = isTablet ? 80.0 : 65.0;

    return GestureDetector(
      onTap: () => setState(() {
        selectedAvatar = imageName;
        selectedImage = null;
      }),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: EdgeInsets.symmetric(horizontal: isTablet ? 10 : 8),
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.2),
            width: isSelected ? 3 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(isSelected ? 0.2 : 0.1),
              blurRadius: isSelected ? 15 : 8,
              spreadRadius: isSelected ? 2 : 0,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size / 2),
          child: Image.asset(
            Assets.profile(imageName),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: colorScheme.primary.withOpacity(0.1),
                child: Icon(
                  Icons.person_outline,
                  color: colorScheme.primary,
                  size: size * 0.5,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultAvtarImages() {
    final defaultProfileImages =
        (context.read<SystemConfigCubit>().state as SystemConfigFetchSuccess)
            .defaultProfileImages;
    final isTablet = MediaQuery.of(context).size.width >= 600;

    if (widget.isNewUser) {
      return Container(
        height: MediaQuery.of(context).size.height * (isTablet ? 0.2 : 0.23),
        margin: EdgeInsets.symmetric(horizontal: isTablet ? 40 : 20),
        child: GridView.builder(
          scrollDirection: Axis.horizontal,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: isTablet ? 20 : 15,
            crossAxisSpacing: isTablet ? 20 : 15,
          ),
          itemCount: defaultProfileImages.length,
          itemBuilder: (_, i) => _buildDefaultAvtarImage(
            i,
            defaultProfileImages[i],
          ),
        ),
      );
    }

    return Container(
      height: MediaQuery.of(context).size.height * (isTablet ? 0.15 : 0.13),
      margin: EdgeInsets.symmetric(horizontal: isTablet ? 40 : 20),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        scrollDirection: Axis.horizontal,
        itemCount: defaultProfileImages.length,
        itemBuilder: (_, i) => _buildDefaultAvtarImage(
          i,
          defaultProfileImages[i],
        ),
      ),
    );
  }

  //continue button will listen to two cubit one is for changing name and other is
  //for uploading profile picture
  Widget _buildContinueButton(UserProfile userProfile) {
    final isTablet = MediaQuery.of(context).size.width >= 600;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
      child: BlocConsumer<UploadProfileCubit, UploadProfileState>(
        bloc: context.read<UploadProfileCubit>(),
        listener: (context, state) {
          if (state is UploadProfileFailure) {
            UiUtils.showSnackBar(
              context.tr(
                convertErrorCodeToLanguageKey(state.errorMessage),
              )!,
              context,
            );
          } else if (state is UploadProfileSuccess) {
            context.read<UserDetailsCubit>().updateUserProfileUrl(state.imageUrl);
          }
        },
        builder: (context, state) {
          /// for updating name,email, number
          return BlocConsumer<ReferAndEarnCubit, ReferAndEarnState>(
            listener: (_, referState) {
              if (referState is ReferAndEarnFailure) {
                UiUtils.showSnackBar(
                  context.tr(
                    convertErrorCodeToLanguageKey(referState.errorMessage),
                  )!,
                  context,
                );
              }
              if (referState is ReferAndEarnSuccess) {
                context.read<UserDetailsCubit>().updateUserProfile(
                      name: referState.userProfile.name,
                      email: referState.userProfile.email,
                      mobile: referState.userProfile.mobileNumber,
                      coins: referState.userProfile.coins,
                    );

                context.read<UpdateUserDetailCubit>().updateProfile(
                      email: emailController!.text,
                      name: nameController!.text,
                      mobile: phoneController!.text,
                    );

                Navigator.of(context).pushNamedAndRemoveUntil(
                  Routes.home,
                  (_) => false,
                  arguments: false,
                );
              }
            },
            builder: (context, referState) {
              return BlocConsumer<UpdateUserDetailCubit, UpdateUserDetailState>(
                listener: (_, state) {
                  if (state is UpdateUserDetailSuccess ||
                      state is UpdateUserDetailFailure) {
                    context.shouldPop();
                  }
                },
                builder: (updateContext, updateState) {
                  final isLoading = updateState is UpdateUserDetailInProgress ||
                      context.read<UploadProfileCubit>().state is UploadProfileInProgress;

                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.white,
                          Colors.white.withOpacity(0.9),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: TextButton(
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          vertical: isTablet ? 20 : 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                      ),
                      onPressed: isLoading ? null : () async {
                        //if upload profile is in progress
                        if (state is UploadProfileInProgress) {
                          return;
                        }

                        if (context.read<UpdateUserDetailCubit>().state
                            is UpdateUserDetailInProgress) {
                          return;
                        }

                        //if update name is in progress
                        if (referState is ReferAndEarnProgress) {
                          return;
                        }

                        //if profile is empty
                        if (selectedAvatar == null &&
                            selectedImage == null &&
                            userProfile.profileUrl!.isEmpty) {
                          UiUtils.showSnackBar(
                            context.tr('selectProfileLbl')!,
                            context,
                          );
                          return;
                        }
                        //if use has not enter the name then so enter name snack bar
                        if (nameController!.text.isEmpty) {
                          UiUtils.showSnackBar(
                            context.tr('enterValidNameMsg')!,
                            context,
                          );
                          return;
                        }

                        if (selectedAvatar != null) {
                          await uploadProfileImage(selectedAvatar ?? '');
                        } else if (selectedImage != null) {
                          // عرض مؤشر التحميل
                          UiUtils.showSnackBar(
                            'جاري رفع الصورة...',
                            context,
                          );

                          // التحقق من صحة الصورة وضغطها قبل رفعها
                          final result = await ImageCompressionService.validateAndCompressImage(selectedImage!);

                          // إذا كانت الصورة غير صالحة، نعرض رسالة خطأ
                          if (!result['isValid']) {
                            if (mounted) {
                              UiUtils.showSnackBar(
                                result['message'],
                                context,
                              );
                            }
                            return;
                          }

                          // استخدام الصورة المضغوطة
                          final compressedImage = result['file'] as File;

                          if (mounted) {
                            await context
                                .read<UploadProfileCubit>()
                                .uploadProfilePicture(compressedImage);
                          }
                        }

                        if (widget.isNewUser) {
                          if (iHaveInviteCode) {
                            context.read<ReferAndEarnCubit>().getReward(
                                  name: nameController!.text.trim(),
                                  userProfile: userProfile,
                                  friendReferralCode:
                                      inviteTextEditingController.text.trim(),
                                  authType:
                                      context.read<AuthCubit>().getAuthProvider(),
                                );
                          }

                          /// ----
                          if (emailController!.text.isNotEmpty ||
                              phoneController!.text.isNotEmpty) {
                            context.read<UserDetailsCubit>().updateUserProfile(
                                  email: emailController!.text.trim(),
                                  name: nameController!.text.trim(),
                                  mobile: phoneController!.text.trim(),
                                );

                            await context
                                .read<UpdateUserDetailCubit>()
                                .updateProfile(
                                  email: emailController!.text,
                                  name: nameController!.text,
                                  mobile: phoneController!.text,
                                );
                          }

                          await Navigator.of(context).pushNamedAndRemoveUntil(
                            Routes.home,
                            (_) => false,
                            arguments: false,
                          );
                        } else {
                          context.read<UserDetailsCubit>().updateUserProfile(
                                email: emailController!.text.trim(),
                                name: nameController!.text.trim(),
                                mobile: phoneController!.text.trim(),
                              );

                          await context.read<UpdateUserDetailCubit>().updateProfile(
                                email: emailController!.text,
                                name: nameController!.text,
                                mobile: phoneController!.text,
                              );
                        }
                      },
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: isLoading
                            ? SizedBox(
                                height: isTablet ? 28 : 24,
                                width: isTablet ? 28 : 24,
                                child: CircularProgressIndicator(
                                  color: Theme.of(context).primaryColor,
                                  strokeWidth: 3,
                                ),
                              )
                            : Text(
                                context.tr(widget.isNewUser ? 'continueLbl' : 'updateLbl')!,
                                style: TextStyle(
                                  fontSize: isTablet ? 18 : 16,
                                  fontWeight: FontWeight.w600,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildNameTextFieldContainer() {
    final isTablet = MediaQuery.of(context).size.width >= 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!widget.isNewUser) ...[
          Text(
            context.tr('profileName')!,
            style: TextStyle(
              fontSize: isTablet ? 16 : 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 10),
        ],
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white.withOpacity(0.1),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: TextFormField(
                validator: (_) => null,
                cursorColor: Colors.white,
                controller: nameController,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                  fontSize: isTablet ? 18 : 16,
                ),
                decoration: InputDecoration(
                  hintText: context.tr('enterNameLbl'),
                  prefixIcon: Icon(
                    Icons.person_outline,
                    color: Colors.white.withOpacity(0.7),
                    size: isTablet ? 24 : 22,
                  ),
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                    color: Colors.white.withOpacity(.6),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: isTablet ? 20 : 16,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmailTextFieldContainer() {
    final isTablet = MediaQuery.of(context).size.width >= 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('emailAddress')!,
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 10),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isEmailTextFieldEnabled
                ? Colors.white.withOpacity(0.1)
                : Colors.white.withOpacity(0.05),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: TextFormField(
                cursorColor: Colors.white,
                readOnly: !isEmailTextFieldEnabled,
                enabled: isEmailTextFieldEnabled,
                controller: emailController,
                keyboardType: TextInputType.emailAddress,
                validator: (val) {
                  return Validators.validateEmail(
                    val!,
                    context.tr('emailRequiredMsg'),
                    context.tr('enterValidEmailMsg'),
                  );
                },
                style: TextStyle(
                  color: isEmailTextFieldEnabled ? Colors.white : Colors.white.withOpacity(0.5),
                  fontWeight: FontWeight.w500,
                  fontSize: isTablet ? 18 : 16,
                ),
                decoration: InputDecoration(
                  hintText: context.tr('enterEmailLbl'),
                  prefixIcon: Icon(
                    Icons.email_outlined,
                    color: Colors.white.withOpacity(0.7),
                    size: isTablet ? 24 : 22,
                  ),
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                    color: Colors.white.withOpacity(.6),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: isTablet ? 20 : 16,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneTextFieldContainer() {
    final isTablet = MediaQuery.of(context).size.width >= 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('phoneNumber')!,
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 10),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isPhoneTextFieldEnabled
                ? Colors.white.withOpacity(0.1)
                : Colors.white.withOpacity(0.05),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: TextFormField(
                cursorColor: Colors.white,
                validator: (_) => null,
                readOnly: !isPhoneTextFieldEnabled,
                enabled: isPhoneTextFieldEnabled,
                controller: phoneController,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(maxPhoneNumberLength),
                  FilteringTextInputFormatter.digitsOnly,
                ],
                keyboardType: TextInputType.phone,
                style: TextStyle(
                  color: isPhoneTextFieldEnabled
                      ? Colors.white
                      : Colors.white.withOpacity(0.5),
                  fontWeight: FontWeight.w500,
                  fontSize: isTablet ? 18 : 16,
                ),
                decoration: InputDecoration(
                  hintText: '-',
                  prefixIcon: Icon(
                    Icons.phone_outlined,
                    color: Colors.white.withOpacity(0.7),
                    size: isTablet ? 24 : 22,
                  ),
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                    color: Colors.white.withOpacity(.6),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: isTablet ? 20 : 16,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !widget.isNewUser,
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          title: Text(
            context.tr('editProfile')!,
            style: const TextStyle(color: Colors.white),
          ),
          centerTitle: true,
        ),
        body: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
              stops: const [0.0, 0.3, 1.0],
            ),
          ),
          child: Stack(
            children: [
              // خلفية زخرفية
              Positioned(
                top: -100,
                right: -100,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),
              Positioned(
                top: 150,
                left: -50,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),
              // المحتوى الرئيسي
              BlocConsumer<UserDetailsCubit, UserDetailsState>(
                listener: (context, state) {
                  //when user register first time then set this listener
                  if (state is UserDetailsFetchSuccess && widget.isNewUser) {
                    UiUtils.fetchBookmarkAndBadges(
                      context: context,
                      userId: state.userProfile.userId!,
                    );
                  }
                },
                bloc: context.read<UserDetailsCubit>(),
                builder: (context, state) {
                  if (state is UserDetailsFetchInProgress ||
                      state is UserDetailsInitial) {
                    return const Center(
                      child: CircularProgressContainer(),
                    );
                  }
                  if (state is UserDetailsFetchFailure) {
                    return ErrorContainer(
                      showBackButton: true,
                      errorMessage:
                          convertErrorCodeToLanguageKey(state.errorMessage),
                      onTapRetry: () {
                        context.read<UserDetailsCubit>().fetchUserDetails();
                      },
                      showErrorImage: true,
                    );
                  }

                  final userProfile =
                      (state as UserDetailsFetchSuccess).userProfile;

                  nameController ??=
                      TextEditingController(text: userProfile.name);
                  emailController ??=
                      TextEditingController(text: userProfile.email);
                  phoneController ??=
                      TextEditingController(text: userProfile.mobileNumber);

                  final size = MediaQuery.of(context).size;

                  return SafeArea(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.only(
                        top: widget.isNewUser ? 20 : 10,
                        bottom: 20,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          if (widget.isNewUser)
                            SizedBox(height: size.height * .02),
                          Center(
                            child: _buildCurrentProfilePictureContainer(
                              image: selectedAvatar != null
                                  ? selectedAvatar!
                                  : selectedImage != null
                                      ? selectedImage!.path
                                      : userProfile.profileUrl ?? '',
                              isFile: selectedImage != null,
                              isAsset: selectedAvatar != null,
                            ),
                          ),
                          const SizedBox(height: 15),
                          _buildSelectAvatarText(),
                          SizedBox(height: size.height * .025),
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: size.width * UiUtils.hzMarginPct,
                            ),
                            child: _buildDefaultAvtarImages(),
                          ),
                          if (widget.isNewUser)
                            const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 15),
                              child: Divider(color: Color(0xFF707070)),
                            )
                          else
                            const Divider(color: Colors.white24),
                          SizedBox(height: size.height * .02),
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: size.width * UiUtils.hzMarginPct,
                            ),
                            child: _buildNameTextFieldContainer(),
                          ),
                          SizedBox(height: size.height * .03),
                          if (!widget.isNewUser) ...[
                            Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: size.width * UiUtils.hzMarginPct,
                              ),
                              child: _buildEmailTextFieldContainer(),
                            ),
                            SizedBox(height: size.height * .03),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: size.width * UiUtils.hzMarginPct,
                              ),
                              child: _buildPhoneTextFieldContainer(),
                            ),
                            SizedBox(height: size.height * .03),
                          ],
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: size.width * UiUtils.hzMarginPct,
                            ),
                            child: _buildContinueButton(userProfile),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
