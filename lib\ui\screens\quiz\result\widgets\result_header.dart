import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';

/// ويدجت رأس النتائج مع الأيقونة والرسائل
class ResultHeader extends StatelessWidget {
  final ResultStateData resultState;

  const ResultHeader({
    super.key,
    required this.resultState,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 1000),
      curve: Curves.elasticOut,
      child: Column(
        children: [
          // أيقونة النتيجة المحسنة مع تأثيرات
          _buildResultIcon(context),
          const SizedBox(height: 20),

          // رسالة النتيجة المحسنة مع انتقالات
          _buildResultMessage(context),
        ],
      ),
    );
  }

  /// بناء أيقونة النتيجة
  Widget _buildResultIcon(BuildContext context) {
    return Container(
      width: 90,
      height: 90,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: resultState.isWinner
              ? [
                  const Color(0xFFFFD700),
                  const Color(0xFFFFA500),
                  const Color(0xFFFF8C00),
                ]
              : [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withOpacity(0.7),
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: resultState.isWinner
                ? const Color(0xFFFFD700).withOpacity(0.4)
                : Theme.of(context).primaryColor.withOpacity(0.4),
            blurRadius: 15,
            spreadRadius: 3,
            offset: const Offset(0, 5),
          ),
          BoxShadow(
            color: Theme.of(context).colorScheme.surface.withOpacity(
                Theme.of(context).brightness == Brightness.dark ? 0.8 : 0.6),
            blurRadius: 8,
            spreadRadius: -3,
            offset: const Offset(-3, -3),
          ),
        ],
      ),
      child: Icon(
        resultState.isWinner
            ? Icons.emoji_events
            : Icons.sentiment_satisfied_alt,
        color: Theme.of(context).colorScheme.onPrimary,
        size: 45,
      ),
    );
  }

  /// بناء رسالة النتيجة
  Widget _buildResultMessage(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 25),
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.surface.withOpacity(
                Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.4),
            Theme.of(context).colorScheme.surface.withOpacity(
                Theme.of(context).brightness == Brightness.dark ? 0.1 : 0.2),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.surface.withOpacity(
              Theme.of(context).brightness == Brightness.dark ? 0.3 : 0.5),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            resultState.mainMessage,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimary,
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            resultState.subMessage,
            style: TextStyle(
              fontSize: 15,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.9),
              height: 1.2,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
