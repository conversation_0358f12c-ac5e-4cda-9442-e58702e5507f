import 'package:flutter/material.dart';
import '../colors.dart';

/// 🎨 Extension للوصول السهل للألوان المخصصة
/// يوفر طريقة سهلة للوصول للألوان حسب الثيم الحالي
extension CustomColors on ThemeData {
  // ========================================
  // 🌟 ألوان الخلفيات
  // ========================================
  
  Color get pageBackground => brightness == Brightness.light 
      ? klPageBackgroundColor 
      : kdPageBackgroundColor;
  
  Color get cardBackground => brightness == Brightness.light 
      ? klCardColor 
      : kdCardColor;
  
  Color get surfaceBackground => brightness == Brightness.light 
      ? klSurfaceColor 
      : kdSurfaceColor;
  
  // ========================================
  // 🎯 ألوان النصوص
  // ========================================
  
  Color get primaryText => brightness == Brightness.light 
      ? klPrimaryTextColor 
      : kdPrimaryTextColor;
  
  Color get secondaryText => brightness == Brightness.light 
      ? klSecondaryTextColor 
      : kdSecondaryTextColor;
  
  Color get hintText => brightness == Brightness.light 
      ? klHintTextColor 
      : kdHintTextColor;
  
  // ========================================
  // 🎨 ألوان الحدود والفواصل
  // ========================================
  
  Color get borderColor => brightness == Brightness.light 
      ? klBorderColor 
      : kdBorderColor;
  
  Color get dividerColor => brightness == Brightness.light 
      ? klDividerColor 
      : kdDividerColor;
  
  // ========================================
  // ✨ ألوان التدرجات
  // ========================================
  
  LinearGradient get primaryGradient => LinearGradient(
    colors: brightness == Brightness.light 
        ? [klPrimaryColor, klSecondaryColor]
        : [kdPrimaryColor, kdSecondaryColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  LinearGradient get accentGradient => LinearGradient(
    colors: brightness == Brightness.light 
        ? [klAccentColor, klSecondaryColor]
        : [kdAccentColor, kdSecondaryColor],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  LinearGradient get cardGradient => LinearGradient(
    colors: brightness == Brightness.light 
        ? [klCardColor, klSurfaceColor]
        : [kdCardColor, kdSurfaceColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // ========================================
  // 🎯 ألوان الحالات
  // ========================================
  
  Color get correctAnswer => kCorrectAnswerColor;
  Color get wrongAnswer => kWrongAnswerColor;
  Color get pendingAnswer => kPendingColor;
  Color get hurryUpTimer => kHurryUpTimerColor;
  Color get badgeLocked => kBadgeLockedColor;
  
  // ========================================
  // 💫 ألوان شفافة للتأثيرات
  // ========================================
  
  Color get primaryTransparent10 => brightness == Brightness.light 
      ? klPrimaryTransparent10 
      : kdPrimaryTransparent10;
  
  Color get primaryTransparent20 => brightness == Brightness.light 
      ? klPrimaryTransparent20 
      : kdPrimaryTransparent20;
  
  Color get primaryTransparent30 => brightness == Brightness.light 
      ? klPrimaryTransparent30 
      : kdPrimaryTransparent30;
  
  Color get whiteTransparent10 => kWhiteTransparent10;
  Color get whiteTransparent20 => kWhiteTransparent20;
  Color get whiteTransparent30 => kWhiteTransparent30;
  
  Color get blackTransparent10 => kBlackTransparent10;
  Color get blackTransparent20 => kBlackTransparent20;
  Color get blackTransparent30 => kBlackTransparent30;
  
  // ========================================
  // 🎨 ألوان مخصصة للكويز
  // ========================================
  
  /// لون خلفية أزرار الإجابات
  Color get quizOptionBackground => brightness == Brightness.light 
      ? Colors.white 
      : kdCardColor;
  
  /// لون حدود أزرار الإجابات
  Color get quizOptionBorder => brightness == Brightness.light 
      ? klPrimaryColor.withOpacity(0.2) 
      : kdPrimaryColor.withOpacity(0.3);
  
  /// لون الظل للأزرار
  Color get quizButtonShadow => brightness == Brightness.light 
      ? klPrimaryColor.withOpacity(0.3) 
      : kdPrimaryColor.withOpacity(0.4);
  
  /// تدرج أزرار الكويز
  LinearGradient get quizButtonGradient => LinearGradient(
    colors: brightness == Brightness.light 
        ? [klPrimaryColor, klPrimaryColor.withOpacity(0.8)]
        : [kdPrimaryColor, kdPrimaryColor.withOpacity(0.8)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  /// لون الدائرة الزمنية
  Color get timerCircle => brightness == Brightness.light 
      ? Colors.white.withOpacity(0.2) 
      : kdPrimaryColor.withOpacity(0.3);
  
  /// لون خلفية الكارت الرئيسي للكويز
  Color get quizCardBackground => brightness == Brightness.light 
      ? Colors.grey.shade50 
      : kdSurfaceColor;
}

/// 🎨 Extension إضافي للألوان الثابتة
extension StaticColors on BuildContext {
  /// الوصول السريع للألوان المخصصة
  ThemeData get colors => Theme.of(this);
  
  /// التحقق من الثيم الحالي
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;
  bool get isLightMode => Theme.of(this).brightness == Brightness.light;
}