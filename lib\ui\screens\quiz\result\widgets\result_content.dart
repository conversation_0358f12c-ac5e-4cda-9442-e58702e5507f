import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/factories/result_widget_factory.dart';

/// ويدجت محتوى النتائج الرئيسي
class ResultContent extends StatelessWidget {
  final ResultData resultData;
  final ResultStateData resultState;

  const ResultContent({
    super.key,
    required this.resultData,
    required this.resultState,
  });

  @override
  Widget build(BuildContext context) {
    final userProfileUrl =
        context.read<UserDetailsCubit>().getUserProfile().profileUrl ?? '';

    // استخدام Factory لإنشاء widget النتائج المناسب
    return ResultWidgetFactory.createResultWidget(
      resultData: resultData,
      resultState: resultState,
      userProfileUrl: userProfileUrl,
    );
  }
}
