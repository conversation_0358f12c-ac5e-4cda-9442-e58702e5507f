import 'package:flutter/material.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:intl/intl.dart';

class UserAchievements extends StatelessWidget {
  const UserAchievements({
    super.key,
    this.userRank = '0',
    this.userScore = '0',
  });

  final String userRank;
  final String userScore;

  @override
  Widget build(BuildContext context) {
    final rank = context.tr('rankLbl')!;
    final score = context.tr('scoreLbl')!;
    final size = MediaQuery.of(context).size;
    final numberFormat = NumberFormat.compact();
    final primaryColor = Theme.of(context).primaryColor;

    return Container(
      key: ValueKey(Theme.of(context).brightness), // إضافة key لإجبار إعادة البناء عند تغيير الثيم
      margin: EdgeInsets.symmetric(
        vertical: size.height * 0.01,
        horizontal: size.width * 0.03,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(size.width * 0.06),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              primaryColor,
              primaryColor.withOpacity(0.8),
              primaryColor.withOpacity(0.9),
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: primaryColor.withOpacity(0.4),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 15,
              offset: const Offset(0, 6),
              spreadRadius: 1,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size.width * 0.06),
          child: Stack(
            children: [
              // نمط خلفية هندسي
              Positioned.fill(
                child: CustomPaint(
                  painter: _GeometricPatternPainter(
                    lineColor: Theme.of(context).colorScheme.onPrimary.withOpacity(0.05),
                    dotColor: Theme.of(context).colorScheme.onPrimary.withOpacity(0.03),
                  ),
                ),
              ),
              // تأثير الضوء المتحرك
              Positioned(
                top: -40,
                right: -40,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
                        Theme.of(context).colorScheme.onPrimary.withOpacity(0.05),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: -20,
                left: -20,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Theme.of(context).colorScheme.onPrimary.withOpacity(0.15),
                        Theme.of(context).colorScheme.onPrimary.withOpacity(0.03),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              // تأثير الانعكاس العلوي
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                height: 60,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(size.width * 0.06),
                      topRight: Radius.circular(size.width * 0.06),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
                        Theme.of(context).colorScheme.onPrimary.withOpacity(0.05),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              // المحتوى الرئيسي
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: size.height * 0.02,
                  horizontal: size.width * 0.05,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _ModernAchievement(
                        title: rank,
                        value: numberFormat.format(double.parse(userRank)),
                        icon: Icons.emoji_events,
                        primaryColor: Colors.amber.shade400,
                        secondaryColor: Colors.amber.shade600,
                        glowColor: Colors.amber.shade400,
                      ),
                    ),
                    Container(
                      width: 2,
                      height: size.height * 0.06,
                      margin:
                          EdgeInsets.symmetric(horizontal: size.width * 0.04),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(1),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Theme.of(context).colorScheme.onPrimary.withOpacity(0.4),
                            Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
                            Theme.of(context).colorScheme.onPrimary.withOpacity(0.4),
                            Colors.transparent,
                          ],
                          stops: const [0.0, 0.2, 0.5, 0.8, 1.0],
                        ),
                      ),
                    ),
                    Expanded(
                      child: _ModernAchievement(
                        title: score,
                        value: numberFormat.format(double.parse(userScore)),
                        icon: Icons.stars,
                        primaryColor: primaryColor,
                        secondaryColor: primaryColor.withOpacity(0.7),
                        glowColor: primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ModernAchievement extends StatelessWidget {
  const _ModernAchievement({
    required this.title,
    required this.value,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
    required this.glowColor,
  });

  final String title;
  final String value;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;
  final Color glowColor;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // أيقونة مع تأثيرات متقدمة
        Container(
          padding: EdgeInsets.all(size.width * 0.03),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.onPrimary.withOpacity(0.25),
                Theme.of(context).colorScheme.onPrimary.withOpacity(0.1),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: glowColor.withOpacity(0.4),
                blurRadius: 15,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 1,
              ),
            ],
            border: Border.all(
              color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: ShaderMask(
            shaderCallback: (bounds) => LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [primaryColor, secondaryColor],
            ).createShader(bounds),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.onPrimary,
              size: size.width * 0.055,
            ),
          ),
        ),
        SizedBox(height: size.height * 0.012),
        // العنوان مع تأثير
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.02,
            vertical: size.height * 0.002,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.1),
          ),
          child: Text(
            title,
            style: TextStyle(
              fontSize: size.width * 0.028,
              fontWeight: FontWeights.semiBold,
              color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.9),
              letterSpacing: 1.0,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: size.height * 0.008),
        // القيمة مع تأثيرات متقدمة
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.03,
            vertical: size.height * 0.006,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
                Theme.of(context).colorScheme.onPrimary.withOpacity(0.1),
              ],
            ),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: size.width * 0.055,
              fontWeight: FontWeights.bold,
              color: Theme.of(context).colorScheme.onPrimary,
              letterSpacing: 0.5,
              shadows: [
                Shadow(
                  color: glowColor.withOpacity(0.5),
                  offset: const Offset(0, 1),
                  blurRadius: 3,
                ),
                Shadow(
                  color: Colors.black.withOpacity(0.3),
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}

// رسام النمط الهندسي للخلفية
class _GeometricPatternPainter extends CustomPainter {
  final Color lineColor;
  final Color dotColor;

  const _GeometricPatternPainter({
    required this.lineColor,
    required this.dotColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = lineColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // رسم شبكة من الخطوط المتقاطعة
    const spacing = 30.0;

    // خطوط أفقية
    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // خطوط عمودية
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // رسم دوائر في نقاط التقاطع
    paint.style = PaintingStyle.fill;
    paint.color = dotColor;

    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        canvas.drawCircle(
          Offset(x, y),
          2,
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// import 'package:flutter/material.dart';
// import 'package:flutterquiz/utils/constants/fonts.dart';
// import 'package:flutterquiz/utils/extensions.dart';
// import 'package:flutterquiz/utils/ui_utils.dart';
// import 'package:intl/intl.dart';
//
// class UserAchievements extends StatelessWidget {
//   const UserAchievements({
//     super.key,
//     this.userRank = '0',
//     this.userCoins = '0',
//     this.userScore = '0',
//   });
//
//   final String userRank;
//   final String userCoins;
//   final String userScore;
//
//   static const _verticalDivider = VerticalDivider(
//     color: Color(0x99FFFFFF),
//     indent: 12,
//     endIndent: 14,
//     thickness: 2,
//   );
//
//   @override
//   Widget build(BuildContext context) {
//     final rank = context.tr('rankLbl')!;
//     final coins = context.tr('coinsLbl')!;
//     final score = context.tr('scoreLbl')!;
//
//     return LayoutBuilder(
//       builder: (_, constraints) {
//         final size = MediaQuery.of(context).size;
//         final numberFormat = NumberFormat.compact();
//
//         return Stack(
//           children: [
//             Positioned(
//               top: 0,
//               left: constraints.maxWidth * (0.05),
//               right: constraints.maxWidth * (0.05),
//               child: Container(
//                 decoration: BoxDecoration(
//                   color: Colors.transparent,
//                   boxShadow: [
//                     BoxShadow(
//                       offset: const Offset(0, 25),
//                       blurRadius: 30,
//                       spreadRadius: 3,
//                       color: Theme.of(context).primaryColor.withOpacity(0.5),
//                     ),
//                   ],
//                   borderRadius: BorderRadius.vertical(
//                     bottom: Radius.circular(constraints.maxWidth * (0.525)),
//                   ),
//                 ),
//                 width: constraints.maxWidth,
//                 height: 100,
//               ),
//             ),
//             Container(
//               height: 100,
//               decoration: BoxDecoration(
//                 color: Theme.of(context).primaryColor,
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               padding: const EdgeInsets.symmetric(
//                 vertical: 12.5,
//                 horizontal: 20,
//               ),
//               margin: EdgeInsets.symmetric(
//                 vertical: size.height * UiUtils.vtMarginPct,
//                 horizontal: size.width * UiUtils.hzMarginPct,
//               ),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceAround,
//                 children: [
//                   _Achievement(
//                     title: rank,
//                     value: numberFormat.format(double.parse(userRank)),
//                   ),
//                   _verticalDivider,
//                   _Achievement(
//                     title: coins,
//                     value: numberFormat.format(double.parse(userCoins)),
//                   ),
//                   _verticalDivider,
//                   _Achievement(
//                     title: score,
//                     value: numberFormat.format(double.parse(userScore)),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         );
//       },
//     );
//   }
// }
//
// class _Achievement extends StatelessWidget {
//   const _Achievement({required this.title, required this.value});
//
//   final String title;
//   final String value;
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Text(
//           title,
//           style: TextStyle(
//             fontSize: 18,
//             fontWeight: FontWeights.bold,
//             color: Colors.white.withOpacity(0.7),
//           ),
//         ),
//         Text(
//           value,
//           style: const TextStyle(
//             fontSize: 24,
//             fontWeight: FontWeights.bold,
//             color: Colors.white,
//           ),
//         ),
//       ],
//     );
//   }
// }
