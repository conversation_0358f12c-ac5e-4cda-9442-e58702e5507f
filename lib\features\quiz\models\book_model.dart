class FileModel {
  // Constructor
  FileModel({
    required this.fileUrl,
    required this.categoryId,
    required this.imageUrl,
    required this.fileName,
  });

  // Factory method لتحويل الـ Map إلى Object من الـ Model
  factory FileModel.fromMap(Map<String, dynamic> map) {
    try {
      return FileModel(
        fileUrl: map['file_url']?.toString() ?? '',
        categoryId: map['category_id']?.toString() ?? '',
        imageUrl: map['image_url']?.toString() ?? '',
        fileName: map['file_name']?.toString() ?? 'ملف بدون اسم',
      );
    } catch (e) {
      print('Error creating FileModel from map: $e');
      print('Map data: $map');
      rethrow;
    }
  }

  final String fileUrl;
  final String categoryId;
  final String imageUrl;
  final String fileName;

  Map<String, dynamic> toMap() {
    return {
      'file_url': fileUrl,
      'category_id': categoryId,
      'image_url': imageUrl,
      'file_name': fileName,
    };
  }

  @override
  String toString() {
    return 'FileModel(fileUrl: $fileUrl, categoryId: $categoryId, fileName: $fileName)';
  }
}
