import 'package:flutterquiz/utils/constants/string_labels.dart';

enum QuizTypes {
  dailyQuiz,
  contest,
  groupPlay,
  practiceSection,
  oneVsOneBattle,
  funAndLearn,
  selfChallenge,
  quizZone,
  bookmarkQuiz,
  audioQuestions,
  exam,
  randomBattle,
}

QuizTypes getQuizTypeEnumFromTitle(String? title) {
  if (title == 'contest') {
    return QuizTypes.contest;
  } else if (title == 'dailyQuiz') {
    return QuizTypes.dailyQuiz;
  } else if (title == 'groupPlay') {
    return QuizTypes.groupPlay;
  } else if (title == 'battleQuiz') {
    return QuizTypes.oneVsOneBattle;
  } else if (title == 'funAndLearn') {
    return QuizTypes.funAndLearn;
  } else if (title == 'selfChallenge') {
    return QuizTypes.selfChallenge;
  } else if (title == 'quizZone') {
    return QuizTypes.quizZone;
  } else if (title == audioQuestionsKey) {
    return QuizTypes.audioQuestions;
  } else if (title == examKey) {
    return QuizTypes.exam;
  } else if (title == 'guessTheWord' ||
      title == 'trueAndFalse' ||
      title == mathManiaKey) {
    // الميزات المعطلة - إعادة توجيه إلى quizZone للتوافق مع الإصدارات السابقة
    return QuizTypes.quizZone;
  }

  return QuizTypes.practiceSection;
}
