import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_tex/flutter_tex.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/features/systemConfig/model/answer_mode.dart';

import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:just_audio/just_audio.dart';

// const _borderRadius = TeXViewBorderRadius.all(10);

// const _textAlign = TeXViewTextAlign.center;
// const _padding = TeXViewPadding.only(
//   top: 16,
//   bottom: 16,
//   left: 4,
//   right: 4,
// );

class LatexAnswerOptions extends StatefulWidget {
  const LatexAnswerOptions({
    required this.hasSubmittedAnswerForCurrentQuestion,
    required this.submitAnswer,
    required this.answerMode,
    required this.constraints,
    required this.correctAnswerId,
    required this.showAudiencePoll,
    required this.audiencePollPercentages,
    required this.answerOptions,
    required this.submittedAnswerId,
    super.key,
  });

  final List<AnswerOption> answerOptions;
  final String submittedAnswerId;
  final String correctAnswerId;
  final BoxConstraints constraints;

  final bool Function() hasSubmittedAnswerForCurrentQuestion;
  final void Function(String) submitAnswer;

  final AnswerMode answerMode;
  final bool showAudiencePoll;
  final List<int> audiencePollPercentages;

  @override
  State<LatexAnswerOptions> createState() => _LatexAnswerOptionsState();
}

class _LatexAnswerOptionsState extends State<LatexAnswerOptions> {
  late final _audioPlayer = AudioPlayer();

  // late final _margin =
  //     TeXViewMargin.only(bottom: (widget.constraints.maxHeight * .015).toInt());

  // TeXViewStyle _teXViewStyle(String id, {required bool isLast}) => TeXViewStyle(
  //       borderRadius: _borderRadius,
  //       fontStyle: TeXViewFontStyle(
  //         fontSize: 19,
  //         fontFamily: 'IBMPlexSansArabic-Regular',
  //       ),
  //       padding: widget.showAudiencePoll ? null : _padding,
  //       contentColor: Colors.white,
  //       backgroundColor: _optionBackgroundColor(id),
  //       margin: isLast ? null : _margin,
  //       height: 60,
  //     );

  // TeXViewStyle _teXViewStyle(String id, {required bool isLast}) {
  //   // Get the current screen size
  //   final screenSize = MediaQuery.of(context).size;
  //
  //   // Set the font size and height based on screen width
  //   double fontSize = screenSize.width * 0.05; // Adjust the multiplier as needed
  //   int height = (screenSize.height * 0.075).toInt(); // Convert height to int
  //
  //   return TeXViewStyle(
  //     borderRadius: _borderRadius,
  //     fontStyle: TeXViewFontStyle(
  //       fontSize: fontSize.toInt(), // Convert fontSize to int
  //       fontFamily: 'IBMPlexSansArabic-Regular',
  //     ),
  //     padding: widget.showAudiencePoll ? null : _padding,
  //     contentColor: Colors.white,
  //     backgroundColor: _optionBackgroundColor(id),
  //     margin: isLast ? null : _margin,
  //     height: height,
  //   );
  // }
  int radius = 10;

  // TeXViewStyle _teXViewStyle(String id, {required bool isLast}) {
  //   // Get the current screen size
  //   // final screenSize = MediaQuery.of(context).size;

  //   // Set the font size based on screen width
  //   // final fontSize = screenSize.width * 0.04; // Adjust the multiplier as needed

  //   // set State for radius to 9 after 1 second
  //   Future.delayed(const Duration(milliseconds: 600), () {
  //     setState(() {
  //       radius = 9;
  //     });
  //   });

  //   return TeXViewStyle(
  //     borderRadius: TeXViewBorderRadius.all(radius),
  //     fontStyle: TeXViewFontStyle(
  //       // fontSize: fontSize.toInt(), // Convert fontSize to int
  //       fontFamily: 'IBMPlexSansArabic-Regular',
  //     ),
  //     padding: widget.showAudiencePoll ? null : _padding,
  //     // contentColor: Colors.white,
  //     contentColor: _optionTextColor(id),
  //     backgroundColor: _optionBackgroundColor(id),
  //     margin: isLast ? null : _margin,
  //   );
  // }

  bool isCorrectAnswer(String id) => id == widget.correctAnswerId;

  bool isSubmittedAnswer(String id) => id == widget.submittedAnswerId;

  // TODO(J): Investigate the issue where the sound stops playing after some time.
  // If related to the just_audio package, perform a thorough check.
  // Ensure that the sound continues playing for subsequent levels.
  Future<void> playSound(String trackName) async {
    if (context.read<SettingsCubit>().getSettings().sound) {
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }
      await _audioPlayer.setAsset(trackName);
      await _audioPlayer.play();
    }
  }

  Future<void> playVibrate() async {
    if (context.read<SettingsCubit>().getSettings().vibration) {
      UiUtils.vibrate();
    }
  }

  @override
  Widget build(BuildContext context) {
    // final options = widget.answerOptions;
    return Text(
      '${widget.answerOptions.length} Options',
      textAlign: TextAlign.center,
      style: TextStyle(
        color: Colors.white,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
    // TeXView(
    //   // renderingEngine: const TeXViewRenderingEngine.katex(),
    //   fonts: const [
    //     TeXViewFont(
    //       fontFamily: 'IBMPlexSansArabic-Regular',
    //       src: 'assets/fonts/Cairo-Regular.ttf',
    //     ),
    //     TeXViewFont(
    //       fontFamily: 'IBMPlexSansArabic-Bold',
    //       src: 'assets/fonts/IBMPlexSansArabic-Regular-Bold.ttf',
    //     ),
    //   ],
    //   child: TeXViewColumn(
    //     children: List.generate(
    //       options.length,
    //       (i) => TeXViewInkWell(
    //         id: options[i].id!,
    //         onTap: _onTapOption,
    //         child: !widget.showAudiencePoll
    //             ? TeXViewDocument(
    //                 options[i].title!,
    //                 style: _teXViewStyle(
    //                   options[i].id!,
    //                   isLast: i == options.length - 1,
    //                 ),
    //               )
    //             : TeXViewColumn(
    //                 children: [
    //                   if (widget.showAudiencePoll) ...[
    //                     TeXViewDocument(
    //                       '${widget.audiencePollPercentages[i]}%',
    //                       style: TeXViewStyle(
    //                         fontStyle: TeXViewFontStyle(
    //                           fontWeight: TeXViewFontWeight.w500,
    //                         ),
    //                         textAlign: TeXViewTextAlign.left,
    //                       ),
    //                     ),
    //                   ],
    //                   TeXViewDocument(
    //                     options[i].title!,
    //                     style: _teXViewStyle(
    //                       options[i].id!,
    //                       isLast: i == options.length - 1,
    //                     ),
    //                   ),
    //                 ],
    //                 style: TeXViewStyle(
    //                   borderRadius: _borderRadius,
    //                   fontStyle: TeXViewFontStyle(
    //                     fontSize: 21,
    //                     fontFamily: 'IBMPlexSansArabic-Regular',
    //                   ),
    //                   padding: const TeXViewPadding.only(
    //                     top: 16,
    //                     bottom: 16,
    //                     left: 8,
    //                     right: 8,
    //                   ),
    //                   contentColor: _optionTextColor(options[i].id!),
    //                   backgroundColor: _optionBackgroundColor(options[i].id!),
    //                   margin: i == options.length - 1 ? null : _margin,
    //                 ),
    //               ),
    //       ),
    //       growable: false,
    //     ),
    //   ),
    //   style: const TeXViewStyle(
    //     textAlign: _textAlign,
    //     sizeUnit: TeXViewSizeUnit.pixels,
    //   ),
    // );
  }
}
