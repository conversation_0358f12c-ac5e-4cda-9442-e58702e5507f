import 'package:flutter/material.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة لإدارة تقييم التطبيق
/// تعرض نافذة التقييم الأصلية للنظام عند تحقق شروط معينة
class RatingService {
  static final RatingService _instance = RatingService._internal();
  factory RatingService() => _instance;
  RatingService._internal();

  final InAppReview _inAppReview = InAppReview.instance;
  
  // مفاتيح التخزين المحلي
  static const String _hasRatedKey = 'has_rated_app';
  static const String _lastPromptDateKey = 'last_rating_prompt_date';
  static const String _promptCountKey = 'rating_prompt_count';
  
  // الحد الأقصى لعدد مرات طلب التقييم
  static const int _maxPromptCount = 3;
  
  // الفترة الزمنية بين طلبات التقييم (بالأيام)
  static const int _minDaysBetweenPrompts = 7;

  /// التحقق مما إذا كان يجب عرض نافذة التقييم
  /// يتم عرض النافذة إذا:
  /// 1. لم يقم المستخدم بتقييم التطبيق من قبل
  /// 2. لم يتم عرض النافذة أكثر من الحد الأقصى
  /// 3. مر وقت كافٍ منذ آخر عرض للنافذة
  Future<bool> shouldShowRatingPrompt() async {
    final prefs = await SharedPreferences.getInstance();
    
    // التحقق مما إذا كان المستخدم قد قيم التطبيق بالفعل
    final hasRated = prefs.getBool(_hasRatedKey) ?? false;
    if (hasRated) return false;
    
    // التحقق من عدد مرات عرض النافذة
    final promptCount = prefs.getInt(_promptCountKey) ?? 0;
    if (promptCount >= _maxPromptCount) return false;
    
    // التحقق من الوقت المنقضي منذ آخر عرض للنافذة
    final lastPromptDateString = prefs.getString(_lastPromptDateKey);
    if (lastPromptDateString != null) {
      final lastPromptDate = DateTime.parse(lastPromptDateString);
      final daysSinceLastPrompt = DateTime.now().difference(lastPromptDate).inDays;
      if (daysSinceLastPrompt < _minDaysBetweenPrompts) return false;
    }
    
    return true;
  }

  /// عرض نافذة التقييم الأصلية للنظام
  /// يتم استدعاء هذه الدالة عند تحقق شروط العرض
  /// وتحديث بيانات التخزين المحلي
  Future<void> showRatingPrompt() async {
    final prefs = await SharedPreferences.getInstance();
    
    try {
      // التحقق من إمكانية عرض نافذة التقييم
      final isAvailable = await _inAppReview.isAvailable();
      
      if (isAvailable) {
        // تحديث بيانات التخزين المحلي
        final promptCount = prefs.getInt(_promptCountKey) ?? 0;
        await prefs.setInt(_promptCountKey, promptCount + 1);
        await prefs.setString(_lastPromptDateKey, DateTime.now().toIso8601String());
        
        // عرض نافذة التقييم
        await _inAppReview.requestReview();
        
        // ملاحظة: لا يمكننا معرفة ما إذا كان المستخدم قد قيم التطبيق بالفعل
        // لأن واجهة برمجة التطبيقات لا توفر هذه المعلومات
      }
    } catch (e) {
      debugPrint('Error showing rating prompt: $e');
    }
  }

  /// تسجيل أن المستخدم قد قيم التطبيق
  /// يتم استدعاء هذه الدالة عندما يقوم المستخدم بالتقييم
  Future<void> markAsRated() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasRatedKey, true);
  }

  /// عرض نافذة التقييم إذا حقق المستخدم نتيجة جيدة
  /// يتم استدعاء هذه الدالة من صفحة النتائج
  Future<void> promptRatingIfNeeded(double percentage) async {
    // التحقق مما إذا كانت النتيجة أكبر من 70%
    if (percentage >= 70) {
      final shouldShow = await shouldShowRatingPrompt();
      if (shouldShow) {
        // تأخير قصير للسماح بعرض صفحة النتائج أولاً
        await Future.delayed(const Duration(seconds: 1));
        await showRatingPrompt();
      }
    }
  }
}
