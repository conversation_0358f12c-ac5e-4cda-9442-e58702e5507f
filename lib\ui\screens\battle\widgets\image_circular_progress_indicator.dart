import 'package:flutter/material.dart';
import 'package:flutterquiz/features/quiz/models/userBattleRoomDetails.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';

class ImageCircularProgressIndicator extends StatelessWidget {
  const ImageCircularProgressIndicator({
    required this.userBattleRoomDetails,
    required this.animationController,
    required this.totalQues,
    super.key,
  });

  final UserBattleRoomDetails userBattleRoomDetails;
  final AnimationController animationController;
  final String totalQues;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 75,
      width: 75,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 50,
              height: 55,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.topCenter,
                    child: QImage.circular(
                      imageUrl: userBattleRoomDetails.profileUrl,
                      height: 48,
                      width: 48,
                    ),
                  ),

                  /// Circle
                  Align(
                    alignment: Alignment.topCenter,
                    child: SizedB<PERSON>(
                      width: 50,
                      height: 50,
                      child: CustomPaint(
                        painter: _CircleCustomPainter(
                          color: Theme.of(context).colorScheme.surface,
                          strokeWidth: 4,
                        ),
                      ),
                    ),
                  ),

                  /// Arc
                  Align(
                    alignment: Alignment.topCenter,
                    child: AnimatedBuilder(
                      animation: animationController,
                      builder: (_, __) {
                        return SizedBox(
                          width: 50,
                          height: 50,
                          child: CustomPaint(
                            painter: _ArcCustomPainter(
                              color: Theme.of(context).primaryColor,
                              strokeWidth: 4,
                              sweepDegree: 360 * animationController.value,
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  ///
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      height: 15,
                      width: 30,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        '${userBattleRoomDetails.correctAnswers}/$totalQues',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.surface,
                          fontSize: 10,
                          fontWeight: FontWeights.regular,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // const SizedBox(height: 8),
            Text(
              userBattleRoomDetails.name,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontWeight: FontWeights.bold,
                fontSize: 12,
                color: Theme.of(context).colorScheme.onTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _CircleCustomPainter extends CustomPainter {
  const _CircleCustomPainter({required this.color, required this.strokeWidth});

  final Color color;
  final double strokeWidth;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width * 0.5, size.height * 0.5);
    final p = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(center, size.width * 0.5, p);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _ArcCustomPainter extends CustomPainter {
  const _ArcCustomPainter({
    required this.color,
    required this.strokeWidth,
    required this.sweepDegree,
  });

  final Color color;
  final double strokeWidth;
  final double sweepDegree;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width * 0.5, size.height * 0.5);
    final p = Paint()
      ..strokeWidth = strokeWidth
      ..color = color
      ..strokeCap = StrokeCap.square
      ..style = PaintingStyle.stroke;

    /// The PI constant.
    const pi = 3.1415926535897932;

    const startAngle = 3 * (pi / 2);
    final sweepAngle = (sweepDegree * pi) / 180.0;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: size.width * 0.5),
      startAngle,
      sweepAngle,
      false,
      p,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
