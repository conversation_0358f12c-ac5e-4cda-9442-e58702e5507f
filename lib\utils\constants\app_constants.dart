// ملف الثوابت الخاصة بالتطبيق
// يحتوي على البيانات الثابتة التي يمكن تحديثها بسهولة

class AppConstants {
  // معلومات التواصل
  static const String whatsappNumber = '9660581105341';
  static const String whatsappUrl = 'https://wa.me/$whatsappNumber';
  static const String whatsappFallbackUrl = 'https://api.whatsapp.com/send?phone=$whatsappNumber';
  
  // رسائل المشاركة
  static const String shareMessage = 
      'تطبيق مجتهد - أفضل تطبيق للاختبارات والتدريب! 📚\n\n'
      'دورات واختبارات متنوعة تساعدك على التفوق في المذاكرة 🎓\n'
      'حمل التطبيق الآن! 📱\n\n'
      'https://mujtahidacademy.com';
  
  // رسائل الأخطاء
  static const String whatsappNotInstalledError = 'لا يمكن فتح واتساب. الرجاء التأكد من تثبيت التطبيق.';
  static const String whatsappOpenError = 'حدث خطأ أثناء محاولة فتح واتساب.';
  
  // روابط التطبيق
  static const String appWebsite = 'https://mujtahidacademy.com';
  
  // نصوص واجهة المستخدم
  static const String contactUsTitle = 'تواصل معنا';
  static const String contactUsSubtitle = 'لأي استفسارات أو الإقتراحات';
  static const String shareAppTitle = 'شارك التطبيق';
  static const String shareAppSubtitle = 'مع أصدقائك';
  
  // نصوص الاشتراك
  static const String premiumTitle = 'مميز';
  static const String upgradeTitle = 'ترقية';
  static const String premiumContentTitle = 'محتوى تعليمي مميز';
  static const String premiumContentMessage = 'هذا المحتوى متاح فقط للمشتركين في الباقة المميزة';
  
  // نصوص المسابقات
  static const String contestBadgeTitle = 'مسابقة';
  static const String participateNowTitle = 'شارك الآن';
  
  // نصوص الأقسام
  static const String coursesAndTrainingTitle = 'الدورات والتدريبات';
  static const String coursesTitle = 'الدورات';
  static const String noCoursesAvailable = 'لا توجد دورات متاحة حالياً';
}