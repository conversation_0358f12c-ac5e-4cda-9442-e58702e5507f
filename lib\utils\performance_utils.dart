import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/painting.dart';

/// أدوات تحسين الأداء للتطبيق
class PerformanceUtils {
  
  /// تحسين أداء الـ main thread
  static Future<void> optimizeMainThread() async {
    try {
      // تقليل عدد الإطارات المفقودة
      if (!kIsWeb) {
        await SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.edgeToEdge,
        );
      }
      
      log('✅ تم تحسين أداء الـ main thread');
    } catch (e) {
      log('❌ خطأ في تحسين الأداء: $e');
    }
  }

  /// تحسين استخدام الذاكرة
  static Future<void> optimizeMemory() async {
    try {
      // تنظيف الذاكرة
      if (!kIsWeb) {
        // تشغيل garbage collector
        await Future.delayed(const Duration(milliseconds: 100));
      }

      log('✅ تم تحسين استخدام الذاكرة');
    } catch (e) {
      log('❌ خطأ في تحسين الذاكرة: $e');
    }
  }

  /// تحسين تحميل الصور
  static Future<void> optimizeImageLoading() async {
    try {
      // تحسين cache للصور
      PaintingBinding.instance.imageCache.maximumSize = 100;
      PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50 MB
      
      log('✅ تم تحسين تحميل الصور');
    } catch (e) {
      log('❌ خطأ في تحسين تحميل الصور: $e');
    }
  }

  /// تطبيق جميع التحسينات
  static Future<void> applyAllOptimizations() async {
    await Future.wait([
      optimizeMainThread(),
      optimizeMemory(),
      optimizeImageLoading(),
    ]);
    
    log('🚀 تم تطبيق جميع تحسينات الأداء');
  }
}
