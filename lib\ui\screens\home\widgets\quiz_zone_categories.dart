import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/quiz/cubits/quizzone_category_cubit.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/login_dialog.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/ui_utils.dart';

class QuizZoneCategories extends StatelessWidget {
  final bool isGuest;

  Future<void> _showLoginDialog(BuildContext context) {
    return showLoginDialog(
      context,
      onTapYes: () {
        Navigator.of(context).pop(); // إغلاق مربع الحوار
        Navigator.of(context)
            .pushNamed(Routes.login); // الانتقال إلى صفحة تسجيل الدخول
      },
    );
  }

  const QuizZoneCategories({super.key, required this.isGuest});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<QuizoneCategoryCubit, QuizoneCategoryState>(
      bloc: context.read<QuizoneCategoryCubit>(),
      listener: (context, state) {
        if (state is QuizoneCategoryFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      builder: (context, state) {
        if (state is QuizoneCategoryFailure) {
          return ErrorContainer(
            showRTryButton: false,
            showBackButton: false,
            showErrorImage: false,
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
            onTapRetry: () {
              context.read<QuizoneCategoryCubit>().getQuizCategoryWithUserId(
                    languageId: UiUtils.getCurrentQuizLanguageId(context),
                  );
            },
          );
        }

        if (state is QuizoneCategorySuccess) {
          final categories = state.categories;

          return SizedBox(
            height: 200, // ارتفاع محسن للقائمة
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                return CategoryCard(
                  category: category,
                  onTap: () async {
                    if (isGuest) {
                      _showLoginDialog(context);
                      return;
                    }

                    if (category.noOf == '0') {
                      if (category.maxLevel == '0') {
                        Navigator.of(context).pushNamed(
                          Routes.quiz,
                          arguments: {
                            'numberOfPlayer': 1,
                            'quizType': QuizTypes.quizZone,
                            'categoryId': category.id,
                            'subcategoryId': '',
                            'level': '0',
                            'subcategoryMaxLevel': '0',
                            'unlockedLevel': 0,
                            'contestId': '',
                            'comprehensionId': '',
                            'quizName': 'Quiz Zone',
                            'showRetryButton': category.noOfQues! != '0',
                          },
                        );
                      } else {
                        Navigator.of(context).pushNamed(
                          Routes.levels,
                          arguments: {
                            'Category': category,
                          },
                        );
                      }
                    } else {
                      Navigator.of(context).pushNamed(
                        Routes.subcategoryAndLevel,
                        arguments: {
                          'category_id': category.id,
                          'category_name': category.categoryName,
                          'isPremiumCategory': category.isPremium,
                        },
                      );
                    }
                  },
                );
              },
            ),
          );
        }

        return const Center(child: CircularProgressContainer());
      },
    );
  }
}

class CategoryCard extends StatefulWidget {
  final dynamic category;
  final VoidCallback onTap;

  const CategoryCard({
    super.key,
    required this.category,
    required this.onTap,
  });

  @override
  State<CategoryCard> createState() => _CategoryCardState();
}

class _CategoryCardState extends State<CategoryCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final primaryColor = Theme.of(context).primaryColor;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: GestureDetector(
              onTap: widget.onTap,
              child: MouseRegion(
                onEnter: (_) => _onHover(true),
                onExit: (_) => _onHover(false),
                child: Container(
                  width: size.width * 0.5,
                  margin: EdgeInsets.only(
                    right: size.width * 0.04,
                    bottom: 10,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: primaryColor.withOpacity(0.3),
                        blurRadius: _isHovered ? 25 : 15,
                        offset: Offset(0, _isHovered ? 15 : 8),
                        spreadRadius: _isHovered ? 2 : 0,
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(25),
                    child: Stack(
                      children: [
                        // صورة الخلفية مع تأثير التكبير
                        Positioned.fill(
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            transform: Matrix4.identity()
                              ..scale(_isHovered ? 1.1 : 1.0),
                            child: QImage(
                              imageUrl: widget.category.image!,
                              fit: BoxFit.cover,
                              padding: EdgeInsets.zero,
                            ),
                          ),
                        ),

                        // نمط هندسي متحرك
                        Positioned.fill(
                          child: CustomPaint(
                            painter: _GeometricOverlayPainter(
                              primaryColor: primaryColor,
                              animationValue: _animationController.value,
                              lineColor: Theme.of(context).colorScheme.onPrimary,
                              triangleColor: Theme.of(context).colorScheme.onPrimary,
                            ),
                          ),
                        ),

                        // تدرج لوني محسن
                        Positioned.fill(
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  primaryColor.withOpacity(0.1),
                                  Colors.black.withOpacity(0.7),
                                ],
                                stops: const [0.0, 0.6, 1.0],
                              ),
                            ),
                          ),
                        ),

                        // تأثير الضوء المتحرك
                        Positioned(
                          top: -50,
                          right: -50,
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            width: _isHovered ? 120 : 80,
                            height: _isHovered ? 120 : 80,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: RadialGradient(
                                colors: [
                                  Theme.of(context).colorScheme.onPrimary
                                      .withOpacity(_isHovered ? 0.3 : 0.2),
                                  Theme.of(context).colorScheme.onPrimary.withOpacity(0.05),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                          ),
                        ),

                        // المحتوى المحسن
                        Positioned(
                          bottom: 15,
                          left: 15,
                          right: 15,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // أيقونة الفئة
                              // Container(
                              //   padding: const EdgeInsets.all(8),
                              //   decoration: BoxDecoration(
                              //     color: Colors.white.withOpacity(0.2),
                              //     borderRadius: BorderRadius.circular(12),
                              //     border: Border.all(
                              //       color: Colors.white.withOpacity(0.3),
                              //       width: 1,
                              //     ),
                              //   ),
                              //   child: Icon(
                              //     Icons.school_rounded,
                              //     color: Colors.white,
                              //     size: 20,
                              //   ),
                              // ),
                              // const SizedBox(height: 12),

                              // اسم الفئة
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                // decoration: BoxDecoration(
                                //   color: Colors.black.withOpacity(0.6),
                                //   borderRadius: BorderRadius.circular(8),
                                // ),
                                child: Text(
                                  widget.category.categoryName!,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: Theme.of(context).colorScheme.onPrimary,
                                    fontSize: size.width * 0.04,
                                    fontWeight: FontWeight.bold,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.5),
                                        offset: const Offset(0, 1),
                                        blurRadius: 3,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(height: 2),

                              // معلومات الأسئلة مع تحسينات
                              Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: size.width * 0.025,
                                        vertical: size.height * 0.008,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            primaryColor.withOpacity(0.9),
                                            primaryColor.withOpacity(0.7),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                primaryColor.withOpacity(0.3),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.quiz_rounded,
                                            color: Theme.of(context).colorScheme.onPrimary,
                                            size: 14,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '${widget.category.noOfQues} سؤال',
                                            style: TextStyle(
                                              color: Theme.of(context).colorScheme.onPrimary,
                                              fontSize: size.width * 0.028,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),

                                  // زر البدء
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.4),
                                        width: 1,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.play_arrow_rounded,
                                      color: Theme.of(context).colorScheme.onPrimary,
                                      size: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// رسام النمط الهندسي للخلفية
class _GeometricOverlayPainter extends CustomPainter {
  final Color primaryColor;
  final double animationValue;
  final Color lineColor;
  final Color triangleColor;

  _GeometricOverlayPainter({
    required this.primaryColor,
    required this.animationValue,
    required this.lineColor,
    required this.triangleColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = lineColor.withOpacity(0.1 * animationValue)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // رسم خطوط متقاطعة متحركة
    final spacing = 25.0 + (animationValue * 5);

    // خطوط قطرية
    for (double i = -size.width; i < size.width * 2; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }

    // دوائر متحركة
    paint.style = PaintingStyle.fill;
    paint.color = primaryColor.withOpacity(0.1 * animationValue);

    final centerX = size.width * 0.8;
    final centerY = size.height * 0.3;
    final radius = 15 + (animationValue * 10);

    canvas.drawCircle(
      Offset(centerX, centerY),
      radius,
      paint,
    );

    // مثلثات صغيرة
    paint.color = triangleColor.withOpacity(0.15 * animationValue);
    final trianglePath = Path();
    final triangleSize = 8 + (animationValue * 4);

    trianglePath.moveTo(size.width * 0.2, size.height * 0.7);
    trianglePath.lineTo(size.width * 0.2 + triangleSize, size.height * 0.7);
    trianglePath.lineTo(
        size.width * 0.2 + triangleSize / 2, size.height * 0.7 - triangleSize);
    trianglePath.close();

    canvas.drawPath(trianglePath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
