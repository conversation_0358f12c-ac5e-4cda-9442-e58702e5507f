const capturedQuestionIdsKey = 'captured_question_ids';
const categoryKey = 'category';
const coinUsedKey = 'coin_used';
const coinsKey = 'coins';
const contestIdKey = 'contest_id';
const correctAnswersKey = 'correct_answers';
const destroyRoomKey = 'destroy_match';
const detailsKey = 'details';
const emailKey = 'email';
const examModuleIdKey = 'exam_module_id';
const fcmIdKey = 'fcm_id';
const firebaseIdKey = 'firebase_id';
const friendCodeKey = 'friends_code';
const funAndLearnKey = 'fun_n_learn_id';
const idKey = 'id';
const imageKey = 'image';
const isDrawnKey = 'is_drawn';
const languageIdKey = 'language_id';
const levelKey = 'level';
const limitKey = 'limit';
const matchIdKey = 'match_id';
const messageKey = 'message';
const mobileKey = 'mobile';
const nameKey = 'name';
const numberOfQuestionsKey = 'no_of_que';
const obtainedMarksKey = 'obtained_marks';
const offsetKey = 'offset';
const paymentAddressKey = 'payment_address';
const paymentAmountKey = 'payment_amount';
const paymentTypeKey = 'payment_type';
const profileKey = 'profile';
const questionAttendedKey = 'questions_attended';
const questionIdKey = 'question_id';
const removeAdsKey = 'remove_ads';
const roomIdKey = 'room_id';
const roomTypeKey = 'room_type';
const rulesViolatedKey = 'rules_violated';
const scoreKey = 'score';
const statisticsKey = 'statistics';
const statusKey = 'status';
const subCategoryKey = 'subcategory';
const subTypeKey = 'sub_type';
const titleKey = 'title';
const totalDurationKey = 'total_duration';
const typeIdKey = 'type_id';
const typeKey = 'type';
const userId1Key = 'user_id1';
const userId2Key = 'user_id2';
const userRankKey = 'user_rank';
const winnerIdKey = 'winner_id';
const paymentIdKey = 'payment_id';
const productIdKey = 'product_id';
const purchaseTokenKey = 'purchase_token';
const payFromKey = 'pay_from';
