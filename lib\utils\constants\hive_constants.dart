const authBox = 'auth';
const settingsBox = 'settings';
const bookmarkBox = 'bookmark';
const guessTheWordBookmarkBox = 'guessTheWordBookmarkBox';
const audioBookmarkBox = 'audioBookmarkBox';
const userDetailsBox = 'userdetails';
const examBox = 'exam';

/// [authBox] Keys
const isLoginKey = 'isLogin';
const jwtTokenKey = 'jwtToken';
const firebaseIdBoxKey = 'firebaseId';
const authTypeKey = 'authType';
const isNewUserKey = 'isNewUser';

/// [userDetailsBox] Keys
const nameBoxKey = 'name';
const userUIdBoxKey = 'userUID';
const emailBoxKey = 'email';
const mobileNumberBoxKey = 'mobile';
const rankBoxKey = 'rank';
const coinsBoxKey = 'coins';
const scoreBoxKey = 'score';
const profileUrlBoxKey = 'profileUrl';
const statusBoxKey = 'status';
const referCodeBoxKey = 'referCode';

/// [settingsBox] keys
const showIntroSliderKey = 'showIntroSlider';
const vibrationKey = 'vibration';
const soundKey = 'sound';
const languageCodeKey = 'language';
const fontSizeKey = 'fontSize';
const rewardEarnedKey = 'rewardEarned';
const fcmTokenBoxKey = 'fcmToken';
const settingsThemeKey = 'theme';
