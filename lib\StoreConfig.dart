import 'package:purchases_flutter/purchases_flutter.dart';

class StoreConfig {
  final Store store;
  final String apiKey;
  static StoreConfig? _instance;

  factory StoreConfig({required Store store, required String apiKey}) {
    // إذا لم يكن هناك instance، أنشئ واحد جديد
    // إذا كان هناك instance، تحديث القيم
    _instance = StoreConfig._internal(store, apiKey);
    return _instance!;
  }

  StoreConfig._internal(this.store, this.apiKey);

  static StoreConfig get instance {
    if (_instance == null) {
      throw Exception(
          'StoreConfig لم يتم تهيئته بعد. يجب استدعاء StoreConfig() أولاً.');
    }
    return _instance!;
  }

  // التحقق من وجود instance
  static bool get isInitialized => _instance != null;

  static bool isForAppleStore() => instance.store == Store.appStore;

  static bool isForGooglePlay() => instance.store == Store.playStore;

  static bool isForAmazonAppstore() => instance.store == Store.amazon;
}
