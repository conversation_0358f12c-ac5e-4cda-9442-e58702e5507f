import 'dart:developer';
import 'dart:math' hide log;

import 'package:flutter/material.dart';
import 'package:flutterquiz/utils/awesome_notification_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ReminderManager {
  final String isReminderEnabledKey = 'enableReminder';
  final String reminderTimeKey = 'reminderTime';

  final List<String> messages = [
    'يلا افتح مجتهد، خلي مهاراتك تطير في السماء! 🦅✨',
    'بغيت التفوق؟ افتح مجتهد وخلِّك دايمًا في القمة! 🚀🔥',
    'اللي ما يفتح مجتهد، يظل بعيد عن القمة! يلا، افتح وابدأ الاختبار!',
    'مراجعة اليوم، نجاح بكرة! افتح مجتهد وزيّن يومك بالاختبار.',
    'يا سلام عليك، لو فتحت مجتهد اليوم! خلي مهاراتك تتحدّى الجميع.',
    'تحب تكون الأفضل؟ افتح مجتهد وخلِّك الأول في كل اختبار!',
    'ما عليك، افتح مجتهد الآن! وخلِّ مهاراتك في أيدي محترفة 👏😉',
    'مستني إيه؟ افتح مجتهد، وخلِّ نفسك تُفاجئك بكل اختبار جديد!',
    'هواك النجاح؟ افتح مجتهد وابدأ الاختبار! كل خطوة تقربك للأعلى!',
    'يلا مع مجتهد، كل يوم اختبار! فتحته؟ خلِّ مهاراتك تتألق 🌟🎤!',
  ];

  String getRandomMessage() {
    return messages[Random().nextInt(messages.length)];
  }

  String timeToString(TimeOfDay time) {
    return '${time.hour}:${time.minute}';
  }

  DateTime stringToTime(String time) {
    final parts = time.split(':');
    return DateTime.now().copyWith(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
      second: 0,
    );
  }

  Future<void> setReminder({required bool value, DateTime? time}) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(isReminderEnabledKey, value);

    if (time != null) {
      await prefs.setString(
        reminderTimeKey,
        timeToString(
          TimeOfDay.fromDateTime(time),
        ),
      );
    }
  }

  Future<void> logReminderData() async {
    final prefs = await SharedPreferences.getInstance();
    log('Reminder is ${prefs.getBool(isReminderEnabledKey)}');
    log('Reminder time is ${prefs.getString(reminderTimeKey)}');
  }

  Future<bool?> getReminderStatus() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(isReminderEnabledKey);
  }

  Future<DateTime?> getReminderTime() async {
    final prefs = await SharedPreferences.getInstance();
    final time = prefs.getString(reminderTimeKey);
    return time != null ? stringToTime(time) : null;
  }

  Future<void> showTimePickerDialog(
    BuildContext context,
    Function(DateTime) onTimeSelected,
  ) async {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final scale = size.width / 400; // قياس مرجعي للتجاوب

    final timeOfDay = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      confirmText: 'تأكيد',
      cancelText: 'إلغاء',
      helpText: 'اختر وقت التذكير',
      hourLabelText: 'الساعة',
      minuteLabelText: 'الدقيقة',
      builder: (context, child) {
        return Theme(
          data: theme.copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: theme.colorScheme.surface,
              hourMinuteShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12 * scale),
                side: BorderSide(
                  color: theme.primaryColor,
                  width: 1 * scale,
                ),
              ),
              dayPeriodBorderSide: BorderSide(
                color: theme.primaryColor,
                width: 1 * scale,
              ),
              dayPeriodShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12 * scale),
                side: BorderSide(
                  color: theme.primaryColor,
                  width: 1 * scale,
                ),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24 * scale),
              ),
              hourMinuteTextStyle: TextStyle(
                fontSize: 42 * scale,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
              dayPeriodTextStyle: TextStyle(
                fontSize: 16 * scale,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
              helpTextStyle: TextStyle(
                fontSize: 14 * scale,
                fontWeight: FontWeight.bold,
                color: theme.primaryColor,
              ),
              dialHandColor: theme.primaryColor,
              dialBackgroundColor: theme.primaryColor.withOpacity(0.1),
              hourMinuteColor: theme.primaryColor.withOpacity(0.1),
              dayPeriodColor: theme.primaryColor.withOpacity(0.1),
              entryModeIconColor: theme.primaryColor,
            ),
          ),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(
              textScaleFactor: scale,
            ),
            child: child!,
          ),
        );
      },
    );

    if (timeOfDay != null) {
      final selectedDate = DateTime.now().copyWith(
        hour: timeOfDay.hour,
        minute: timeOfDay.minute,
        second: 0,
      );
      onTimeSelected(selectedDate);
    }
  }

  Future<void> handleReminderSetup(BuildContext context) async {
    final isReminderEnabled = await getReminderStatus();

    if (isReminderEnabled == null) {
      // تعطيل التذكير تلقائياً عند أول تشغيل
      await setReminder(value: false);
    }
  }

  Future<DateTime?> selectReminderTime(
    BuildContext context, {
    bool popAfter = true,
  }) async {
    DateTime? date;

    await showTimePickerDialog(
      context,
      (selectedDate) async {
        date = selectedDate;
        await awesomeNotificationManager.addCustomDailyReminder(
          id: 393,
          title: 'التذكير اليومي',
          time: Time(
            hour: selectedDate.hour,
            minute: selectedDate.minute,
          ),
          body: getRandomMessage(),
          payload: '',
        );

        await setReminder(value: true, time: selectedDate);
        if (popAfter) Navigator.of(context).pop();
      },
    );
    return date;
  }
}
