# آخر مجتهد (LastMogtahed)

تطبيق مسابقات تعليمية شامل مبني بـ Flutter مع ميزات متقدمة للمحادثة الصوتية والتفاعل المباشر.

## 🚀 الميزات الرئيسية

- 🎯 مسابقات تفاعلية متنوعة
- 🎙️ محادثة صوتية مباشرة باستخدام Agora
- ⚔️ معارك مباشرة بين المستخدمين
- 🏆 نظام نقاط ولوحة متصدرين
- 💰 نظام محفظة ومشتريات داخل التطبيق
- 🔔 إشعارات ذكية
- 🌙 دعم الوضع الليلي والنهاري

## 📋 متطلبات الإعداد

### 1. إعداد متغيرات البيئة

```bash
# انسخ ملف المثال
cp .env.example .env

# حرر الملف وأضف المفاتيح الحقيقية
nano .env
```

### 2. الحصول على Agora App ID

1. اذهب إلى [Agora Console](https://console.agora.io/)
2. أنشئ مشروع جديد أو استخدم مشروع موجود
3. انسخ App ID وأضفه إلى ملف `.env`:
   ```
   AGORA_APP_ID=your_actual_app_id_here
   ```

### 3. تشغيل التطبيق

```bash
# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق مع متغيرات البيئة
flutter run --dart-define-from-file=.env
```

## 🛠️ التطوير

### تشغيل التطبيق في وضع التطوير

```bash
flutter run --dart-define-from-file=.env --debug
```

### بناء التطبيق للإنتاج

```bash
# Android
flutter build apk --dart-define-from-file=.env --release

# iOS
flutter build ios --dart-define-from-file=.env --release
```

## 📚 الموارد المفيدة

- [Flutter Documentation](https://docs.flutter.dev/)
- [Agora Flutter SDK](https://docs.agora.io/en/video-calling/get-started/get-started-sdk?platform=flutter)
- [Firebase Setup](https://firebase.google.com/docs/flutter/setup)
