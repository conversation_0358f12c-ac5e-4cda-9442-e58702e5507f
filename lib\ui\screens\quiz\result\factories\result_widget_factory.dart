import 'package:flutter/material.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/widgets/battle_result_widget.dart';
import 'package:flutterquiz/ui/screens/quiz/result/widgets/individual_result_widget.dart';
import 'package:flutterquiz/ui/screens/quiz/result/widgets/exam_result_widget.dart';

/// Factory لإنتاج widgets النتائج المناسبة حسب نوع الاختبار
abstract class ResultWidgetFactory {
  /// إنشاء widget النتائج المناسب حسب نوع الاختبار
  static Widget createResultWidget({
    required ResultData resultData,
    required ResultStateData resultState,
    required String userProfileUrl,
  }) {
    // اختيار نوع العرض حسب عدد اللاعبين أولاً
    if (resultData.numberOfPlayers == 2) {
      return BattleResultWidget(
        resultData: resultData,
        resultState: resultState,
      );
    }

    // ثم حسب نوع الاختبار للاعب الواحد
    return switch (resultData.quizType) {
      QuizTypes.exam => ExamResultWidget(
          resultData: resultData,
          resultState: resultState,
          userProfileUrl: userProfileUrl,
        ),
      QuizTypes.oneVsOneBattle => BattleResultWidget(
          resultData: resultData,
          resultState: resultState,
        ),
      _ => IndividualResultWidget(
          resultData: resultData,
          resultState: resultState,
          userProfileUrl: userProfileUrl,
        ),
    };
  }

  /// إنشاء عنوان مخصص حسب نوع الاختبار
  static String createCustomTitle(QuizTypes quizType) {
    return switch (quizType) {
      QuizTypes.selfChallenge => 'نتيجة الإختبار',
      QuizTypes.exam => ' نتيجة الاختبار',
      QuizTypes.dailyQuiz => 'نتيجة الإختبار',
      QuizTypes.oneVsOneBattle => 'نتيجة الإختبار',
      QuizTypes.funAndLearn => 'نتيجة الإختبار',
      QuizTypes.bookmarkQuiz => 'نتيجة الإختبار',
      QuizTypes.contest => 'نتيجة الإختبار',
      QuizTypes.randomBattle => 'نتيجة الإختبار',
      QuizTypes.groupPlay => 'نتيجة الإختبار',
      QuizTypes.practiceSection => 'نتيجة الإختبار',
      _ => ' نتيجة الاختبار',
    };
  }

  /// إنشاء أيقونة مخصصة حسب نوع الاختبار
  static IconData createCustomIcon(QuizTypes quizType, bool isWinner) {
    if (isWinner) {
      return switch (quizType) {
        QuizTypes.exam => Icons.school,
        QuizTypes.oneVsOneBattle => Icons.emoji_events,
        QuizTypes.contest => Icons.emoji_events, // استخدام أيقونة موجودة
        QuizTypes.selfChallenge => Icons.gps_fixed, // استخدام أيقونة موجودة
        _ => Icons.emoji_events,
      };
    } else {
      return switch (quizType) {
        QuizTypes.exam => Icons.assignment_late,
        QuizTypes.oneVsOneBattle => Icons.sports_esports,
        QuizTypes.contest => Icons.leaderboard,
        QuizTypes.selfChallenge => Icons.psychology,
        _ => Icons.sentiment_satisfied_alt,
      };
    }
  }

  /// إنشاء ألوان مخصصة حسب نوع الاختبار
  static List<Color> createCustomColors(QuizTypes quizType, bool isWinner) {
    if (isWinner) {
      return switch (quizType) {
        QuizTypes.exam => [
            const Color(0xFF4CAF50),
            const Color(0xFF66BB6A),
            const Color(0xFF81C784),
          ],
        QuizTypes.oneVsOneBattle => [
            const Color(0xFFFFD700),
            const Color(0xFFFFA500),
            const Color(0xFFFF8C00),
          ],
        QuizTypes.contest => [
            const Color(0xFFE91E63),
            const Color(0xFFF06292),
            const Color(0xFFF48FB1),
          ],
        QuizTypes.selfChallenge => [
            const Color(0xFF9C27B0),
            const Color(0xFFBA68C8),
            const Color(0xFFCE93D8),
          ],
        _ => [
            const Color(0xFF2196F3),
            const Color(0xFF64B5F6),
            const Color(0xFF90CAF9),
          ],
      };
    } else {
      return [
        const Color(0xFF757575),
        const Color(0xFF9E9E9E),
        const Color(0xFFBDBDBD),
      ];
    }
  }

  /// إنشاء رسالة مخصصة حسب نوع الاختبار
  static String createCustomMessage(QuizTypes quizType, bool isWinner) {
    if (isWinner) {
      return switch (quizType) {
        QuizTypes.exam => 'ممتاز! لقد نجحت في الامتحان بتفوق',
        QuizTypes.oneVsOneBattle => 'رائع! لقد فزت في المعركة',
        QuizTypes.contest => 'مبروك! أداء متميز في المسابقة',
        QuizTypes.selfChallenge => 'أحسنت! تحدي ذاتي ناجح',
        QuizTypes.dailyQuiz => 'ممتاز! يوم موفق في التعلم',
        _ => 'أحسنت! لقد أكملت الاختبار بنجاح',
      };
    } else {
      return switch (quizType) {
        QuizTypes.exam => 'لا بأس، راجع المواد وحاول مرة أخرى',
        QuizTypes.oneVsOneBattle => 'معركة جيدة! استمر في التدريب',
        QuizTypes.contest => 'مشاركة رائعة، حاول مرة أخرى',
        QuizTypes.selfChallenge => 'تحدي جيد، يمكنك تحسين أدائك',
        QuizTypes.dailyQuiz => 'يوم جيد للتعلم، استمر في المحاولة',
        _ => 'حاول مرة أخرى، أنت قادر على تحقيق نتيجة أفضل',
      };
    }
  }

  /// تحديد ما إذا كان يجب عرض تفاصيل إضافية
  static bool shouldShowExtraDetails(QuizTypes quizType) {
    return switch (quizType) {
      QuizTypes.exam => true,
      QuizTypes.oneVsOneBattle => true,
      QuizTypes.contest => true,
      _ => false,
    };
  }

  /// تحديد ما إذا كان يجب عرض الوقت المستغرق
  static bool shouldShowTimeTaken(QuizTypes quizType) {
    return switch (quizType) {
      QuizTypes.exam => true,
      QuizTypes.selfChallenge => true,
      QuizTypes.contest => true,
      QuizTypes.dailyQuiz => true,
      _ => false,
    };
  }

  /// تحديد ما إذا كان يجب عرض نسبة النجاح
  static bool shouldShowPercentage(QuizTypes quizType) {
    return switch (quizType) {
      QuizTypes.oneVsOneBattle =>
        false, // في المعارك نعرض النقاط بدلاً من النسبة
      _ => true,
    };
  }
}
