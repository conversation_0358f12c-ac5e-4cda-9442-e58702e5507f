import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/systemConfig/model/answer_mode.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/styles/theme/themeCubit.dart' as theme_cubit;

class QuickSettingsDialog extends StatefulWidget {
  const QuickSettingsDialog({Key? key}) : super(key: key);

  @override
  State<QuickSettingsDialog> createState() => _QuickSettingsDialogState();
}

class _QuickSettingsDialogState extends State<QuickSettingsDialog> {
  late String answerMode;
  late bool sound;
  late bool originalSound; // إضافة متغير لتخزين حالة الصوت الأصلية
  late AnswerMode selectedAnswerMode;
  late String selectedAnswerModeValue;

  @override
  void initState() {
    super.initState();
    final settings = context.read<SettingsCubit>().getSettings();
    sound = settings.sound;
    originalSound = settings.sound; // حفظ حالة الصوت الأصلية
    selectedAnswerMode = context.read<SystemConfigCubit>().answerMode;
    answerMode = _convertAnswerModeToString(selectedAnswerMode);

    // تحديد قيمة النص للإعداد المختار
    if (selectedAnswerMode == AnswerMode.noAnswerCorrectness) {
      selectedAnswerModeValue = 'noAnswerCorrectness';
    } else if (selectedAnswerMode == AnswerMode.showAnswerCorrectness) {
      selectedAnswerModeValue = 'showAnswerCorrectness';
    } else {
      selectedAnswerModeValue = 'showAnswerCorrectnessAndCorrectAnswer';
    }
  }

  String _convertAnswerModeToString(answerMode) {
    if (answerMode == AnswerMode.noAnswerCorrectness) {
      return 'بدون عرض الاجابات';
    } else if (answerMode == AnswerMode.showAnswerCorrectness) {
      return 'بدون تصحيح الاجابة';
    } else {
      return 'تصحيح الاجابة';
    }
  }

  // حفظ الإعدادات عند الضغط على زر الحفظ
  Future<void> _saveSettings() async {
    final shared = await SharedPreferences.getInstance();
    await shared.setString('answerMode', selectedAnswerModeValue);
    await context.read<SystemConfigCubit>().getSystemConfig();

    // حفظ إعدادات الصوت إذا تغيرت
    if (sound != originalSound) {
      context.read<SettingsCubit>().sound = sound;
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).primaryColor;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      elevation: 8,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(0),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with gradient
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.settings,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    "إعدادات سريعة",
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // وضع الإجابة
                  Text(
                    "وضع الإجابة",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // خيارات وضع الإجابة - تم تعديلها لتكون أسرع
                  _buildFastAnswerModeOptions(context),

                  const SizedBox(height: 24),

                  // إعدادات الصوت
                  Text(
                    "إعدادات أخرى",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // الصوت
                  _buildSoundToggle(
                    context: context,
                    value: sound,
                    onChanged: (value) {
                      setState(() {
                        sound = value;
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  // خيار تبديل الثيم
                  _buildThemeToggle(context),
                ],
              ),
            ),

            // زر حفظ
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: ElevatedButton(
                onPressed: _saveSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  "حفظ",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeToggle(BuildContext context) {
    final primaryColor = Theme.of(context).primaryColor;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: () {
        context.read<theme_cubit.ThemeCubit>().changeTheme(
              isDarkMode
                  ? theme_cubit.ThemeMode.light
                  : theme_cubit.ThemeMode.dark,
            );
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: 1.5,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: primaryColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                isDarkMode ? "الوضع الفاتح" : "الوضع الداكن",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onBackground,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  // دالة جديدة لعرض خيارات وضع الإجابة بشكل أسرع
  Widget _buildFastAnswerModeOptions(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        children: [
          _buildFastAnswerModeOption(
            context: context,
            title: "إظهار الإجابة الصحيحة",
            subtitle: "عرض الإجابة الصحيحة عند الخطأ",
            isSelected: selectedAnswerMode ==
                AnswerMode.showAnswerCorrectnessAndCorrectAnswer,
            answerModeValue: 'showAnswerCorrectnessAndCorrectAnswer',
            answerModeEnum: AnswerMode.showAnswerCorrectnessAndCorrectAnswer,
          ),
          Divider(
              height: 1,
              thickness: 1,
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3)),
          _buildFastAnswerModeOption(
            context: context,
            title: "إظهار صحة الإجابة فقط",
            subtitle: "عرض إذا كانت إجابتك صحيحة أم خاطئة",
            isSelected: selectedAnswerMode == AnswerMode.showAnswerCorrectness,
            answerModeValue: 'showAnswerCorrectness',
            answerModeEnum: AnswerMode.showAnswerCorrectness,
          ),
          Divider(height: 1, thickness: 1, color: Colors.grey.withOpacity(0.2)),
          _buildFastAnswerModeOption(
            context: context,
            title: "عدم إظهار صحة الإجابة",
            subtitle: "الانتقال للسؤال التالي مباشرة",
            isSelected: selectedAnswerMode == AnswerMode.noAnswerCorrectness,
            answerModeValue: 'noAnswerCorrectness',
            answerModeEnum: AnswerMode.noAnswerCorrectness,
          ),
        ],
      ),
    );
  }

  // دالة جديدة لعرض خيار وضع الإجابة بشكل أسرع
  Widget _buildFastAnswerModeOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required bool isSelected,
    required String answerModeValue,
    required AnswerMode answerModeEnum,
  }) {
    final primaryColor = Theme.of(context).primaryColor;

    return InkWell(
      onTap: () {
        // تحديث الإعدادات فوراً في واجهة المستخدم فقط
        setState(() {
          selectedAnswerMode = answerModeEnum;
          selectedAnswerModeValue = answerModeValue;
          answerMode = _convertAnswerModeToString(answerModeEnum);
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        color: isSelected
            ? primaryColor.withOpacity(0.1)
            : Theme.of(context).colorScheme.surface,
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? primaryColor : Colors.transparent,
                border: Border.all(
                  color: isSelected
                      ? primaryColor
                      : Theme.of(context).colorScheme.outline,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: 16,
                      color: Theme.of(context).colorScheme.onPrimary,
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected
                          ? primaryColor
                          : Theme.of(context).colorScheme.onBackground,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      color: Theme.of(context)
                          .colorScheme
                          .onBackground
                          .withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSoundToggle({
    required BuildContext context,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final primaryColor = Theme.of(context).primaryColor;

    return InkWell(
      onTap: () {
        onChanged(!value); // تبديل حالة الصوت عند النقر على الصف بأكمله
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: 1.5,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.music_note_rounded,
              color: value
                  ? primaryColor
                  : Theme.of(context).colorScheme.onSurfaceVariant,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                "الصوت",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onBackground,
                ),
              ),
            ),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: primaryColor,
            ),
          ],
        ),
      ),
    );
  }
}

// دالة لعرض نافذة الإعدادات السريعة
Future<void> showQuickSettingsDialog(BuildContext context) async {
  return showDialog(
    context: context,
    builder: (context) => const QuickSettingsDialog(),
  );
}
