<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500"><g id="freepik--background-simple--inject-99"><g><path d="M462.01,369.56c27.74-55.46,29.61-129.36,10.46-188.6-16.49-51.01-53.18-89.91-95.56-113.08-49.17-26.88-107.78-34.88-160.55-17.91-13.88,4.46-27.4,8.55-40.48,15.88-53.65,30.05-43.3,104.1-62.12,129.85-15.84,21.67-46.12,15.28-65.21,31.89C-2.97,272.4-3.11,385.67,56.34,421.07c63.06,37.56,336.96,85.85,405.67-51.51Z" style="fill:#1602FF;"></path><path d="M462.01,369.56c27.74-55.46,29.61-129.36,10.46-188.6-16.49-51.01-53.18-89.91-95.56-113.08-49.17-26.88-107.78-34.88-160.55-17.91-13.88,4.46-27.4,8.55-40.48,15.88-53.65,30.05-43.3,104.1-62.12,129.85-15.84,21.67-46.12,15.28-65.21,31.89C-2.97,272.4-3.11,385.67,56.34,421.07c63.06,37.56,336.96,85.85,405.67-51.51Z" style="fill:#fff; opacity:.8;"></path></g></g><g id="freepik--Clock--inject-99"><g style="opacity:.3;"><circle cx="405.97" cy="152.47" r="46.6" transform="translate(-27.58 161.98) rotate(-21.84)" style="fill:none; stroke:#000; stroke-linejoin:round;"></circle><circle cx="405.97" cy="152.47" r="40.87" transform="translate(189.65 528.32) rotate(-80.66)" style="fill:none; stroke:#000; stroke-linejoin:round;"></circle><circle cx="405.97" cy="152.47" r="2.8" style="fill:none; stroke:#000; stroke-linejoin:round;"></circle><line x1="405.97" y1="155.27" x2="405.97" y2="183.45" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="394.83" y1="146.83" x2="403.25" y2="151.19" style="fill:none; stroke:#000; stroke-linejoin:round;"></line></g></g><g id="freepik--Window--inject-99"><g><path d="M327.49,64.26c-2.42,0-4.45,1.57-5.18,3.74H53.63c-.74-2.17-2.77-3.74-5.19-3.74-3.04,0-5.5,2.46-5.5,5.5s2.46,5.5,5.5,5.5c2.42,0,4.45-1.57,5.19-3.74H322.3c.74,2.17,2.77,3.74,5.18,3.74,3.04,0,5.5-2.46,5.5-5.5s-2.46-5.5-5.5-5.5Z" style="opacity:.2;"></path><g style="opacity:.2;"><path d="M117.18,72.51c3.83,93.15,7.18,187.18,11.19,279.99l-.4,18.19s-17.22,0-26.28,.91c-9.06,.91-23.11,.91-33.08,.91s-15.41-3.63-15.41-3.63l.65-16.4c2.01-92.76,4.18-186.8,5.6-279.96" style="fill:none; stroke:#000; stroke-linejoin:round;"></path><line x1="74.25" y1="337.1" x2="74.2" y2="348.06" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="75.19" y1="123.41" x2="74.29" y2="327.36" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="75.42" y1="70.44" x2="75.21" y2="117.32" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="90.86" y1="177.58" x2="88.81" y2="313.36" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="92.46" y1="71.66" x2="90.99" y2="169.09" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="105.29" y1="215.34" x2="105.86" y2="341.37" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="104.88" y1="125.85" x2="105.24" y2="205" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="104.64" y1="72.88" x2="104.84" y2="116.11" style="fill:none; stroke:#000; stroke-linejoin:round;"></line></g><g style="opacity:.2;"><path d="M256.18,72.51c-3.68,98.62-8.37,198.24-11.9,296.34,0,0,11.73,1.77,19.27,1.77h38.49c8.3,0,17.82-1.84,17.82-1.84-1.74-98.03-2.91-197.65-5.95-296.27" style="fill:none; stroke:#000; stroke-linejoin:round;"></path><line x1="299.1" y1="337.1" x2="299.15" y2="348.06" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="298.17" y1="123.41" x2="299.06" y2="327.36" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="297.93" y1="70.44" x2="298.14" y2="117.32" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="282.49" y1="177.58" x2="284.54" y2="313.36" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="280.89" y1="71.66" x2="282.36" y2="169.09" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="268.06" y1="215.34" x2="267.49" y2="341.37" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="268.47" y1="125.85" x2="268.11" y2="205" style="fill:none; stroke:#000; stroke-linejoin:round;"></line><line x1="268.71" y1="72.88" x2="268.51" y2="116.11" style="fill:none; stroke:#000; stroke-linejoin:round;"></line></g><g style="opacity:.2;"><polyline points="118.89 98.98 145.93 98.98 145.93 130.52 120.69 130.52" style="fill:none; stroke:#000; stroke-linejoin:round;"></polyline><rect x="150.64" y="99.06" width="28.26" height="31.64" style="fill:none; stroke:#000; stroke-linejoin:round;"></rect></g><g style="opacity:.2;"><polyline points="254.83 98.98 227.79 98.98 227.79 130.52 253.03 130.52" style="fill:none; stroke:#000; stroke-linejoin:round;"></polyline><rect x="194.81" y="99.06" width="28.26" height="31.64" transform="translate(417.89 229.75) rotate(180)" style="fill:none; stroke:#000; stroke-linejoin:round;"></rect></g><polyline points="123.35 242.32 179.63 242.32 179.63 147.75 120.88 147.75" style="fill:none; opacity:.2; stroke:#000; stroke-linejoin:round;"></polyline><polyline points="251.98 146.95 194.47 146.95 194.47 242.32 247.65 242.32" style="fill:none; opacity:.2; stroke:#000; stroke-linejoin:round;"></polyline><polyline points="125.2 264.76 179.63 264.76 179.63 359.83 129.33 359.83" style="fill:none; opacity:.2; stroke:#000; stroke-linejoin:round;"></polyline><polyline points="248.27 263.96 194.47 263.96 194.47 360.71 243.52 360.71" style="fill:none; opacity:.2; stroke:#000; stroke-linejoin:round;"></polyline><line x1="129.08" y1="366.75" x2="244.27" y2="366.75" style="fill:none; opacity:.2; stroke:#000; stroke-linejoin:round;"></line><line x1="118.66" y1="88.03" x2="254.76" y2="88.03" style="fill:none; opacity:.2; stroke:#000; stroke-linejoin:round;"></line><line x1="187.04" y1="88.68" x2="187.04" y2="365.45" style="fill:none; opacity:.2; stroke:#000; stroke-linejoin:round;"></line><line x1="120.39" y1="140.43" x2="252.78" y2="140.43" style="fill:none; opacity:.2; stroke:#000; stroke-linejoin:round;"></line></g></g><g id="freepik--Table--inject-99"><line x1="17.34" y1="415.93" x2="482.2" y2="415.93" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></line></g><g id="freepik--Character--inject-99"><g><g><path d="M347.59,324.12c0-9.85-6.65-29.54-15.34-36.57-8.68-7.03-31.22-13.44-31.22-13.44l-29.4-3.17v-.42l-2.06,.2-1.81-.2v.37l-33.87,3.22s-25.56,6.41-35.41,13.44c-9.85,7.03-18.29,23.56-18.29,33.41s-.7,55.21-.7,55.21l29.54,2.81,3.87-9.85,4.22,46.77h98.7l3.72-46.77,3.41,9.85,28.9,.14s-4.27-45.15-4.27-54.99Z" style="fill:#1602FF; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M206.9,357.73s9.44-27.57,9.82-22.66c.38,4.91-3.81,34.06-3.81,34.06l-6.38,11.26,.38-22.66Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M217.12,415.9s94.2-46.64,90.93-39.11c-3.27,7.53-8.18,13.42-8.18,13.42l15.71-8.51,5.24-38.29-5,72.5h-98.7Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><g><path d="M233.89,274.11s-.24,36.3,6.44,49.31c6.68,13.01,27.78,28.13,31.3,26.02,3.52-2.11,24.62-28.48,26.02-35.17,1.41-6.68-2.17-40.17-2.17-40.17h-61.59Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M246.99,303l14.9-7.01-28.27-17.73s1.11,30,2.86,33.51l10.52-8.76Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M239.28,283.33s8.09,24.62,14.07,31.65" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M289.92,284.73s-3.87,20.04-9.49,30.59" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g></g><g><path d="M201.99,303.99c-.7,3.13-2.92,5.29-4.97,4.83-2.04-.46-3.13-3.36-2.44-6.49,.7-3.13,2.92-5.29,4.97-4.83,2.04,.46,3.13,3.36,2.44,6.49Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M199.08,295.39s3.59-13.54,4.42-15.75c.83-2.21,13.26-29.84,13.82-33.99,.55-4.14,1.38-8.57,2.21-8.29s11.61,18.79,11.61,18.79c0,0-2.21,7.74-4.7,13.82-2.49,6.08-2.76,7.46-2.76,7.46l9.4-10.5s2.49,3.04,.83,7.18-1.66,1.93-5.25,6.91-5.25,8.57-7.46,9.95c-2.21,1.38-8.57,6.63-8.57,6.63,0,0-3.32,12.16-3.32,17.96s.83,71.57,1.38,80.69c.55,9.12-7.46,21.83-12.43,23.21-4.97,1.38-16.58,.28-21.28-8.01-4.7-8.29-.83-27.36,.55-32.33,1.38-4.97,21.55-83.73,21.55-83.73Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M221.97,242.96s-.77,14.46-2.07,22.47" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M227.39,246.06s-.77,13.43-3.1,19.37" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M205.43,276.6s5.8-14.92,6.91-21.83c1.11-6.91,1.66-9.95,4.14-6.91,2.49,3.04,.83,12.44,0,17.41s0,9.67-.83,16.3c-.83,6.63-2.76,9.4-3.87,9.95" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><g><path d="M330.3,295.39s-3.59-13.54-4.42-15.75c-.83-2.21-15.44-28.46-16-32.61-.55-4.14-5.53-11.33-6.36-11.05-.83,.28-5.28,20.17-5.28,20.17,0,0,2.21,7.74,4.7,13.82s2.76,7.46,2.76,7.46l-9.4-10.5s-2.49,3.04-.83,7.18,1.66,1.93,5.25,6.91,5.25,8.57,7.46,9.95c2.21,1.38,8.57,6.63,8.57,6.63,0,0,3.32,12.16,3.32,17.96s-.83,71.57-1.38,80.69c-.55,9.12,7.46,21.83,12.43,23.21,4.97,1.38,16.58,.28,21.28-8.01,4.7-8.29,.83-27.36-.55-32.33s-21.55-83.73-21.55-83.73Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M302.28,241.15s5.94,13.94,7.23,21.95" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M301.12,246.06s.77,13.43,3.1,19.37" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M322.32,277.42s-6.08-12.16-8.57-14.92-9.12-13.54-11.33-15.47c-2.21-1.93-2.49,4.42-.28,8.57,2.21,4.15,6.36,13.54,6.91,14.37,.55,.83,2.49,9.4,3.04,14.92,.55,5.53,2.49,9.12,5.8,10.5s4.7,2.21,4.7,2.21" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><g><path d="M286.18,209.11s9.72-9.34,5.59-19.68c-4.13-10.33-25.49-18.26-37.2-15.5-11.71,2.76-15.84,5.51-15.84,5.51l10.33-1.38s-13.09,2.76-17.91,11.37-3.44,12.74-3.44,12.74l5.51-4.82s-1.38,6.54,1.72,11.02c3.1,4.48,14.47,1.72,18.94-1.72,4.48-3.44,9.3-6.54,17.22-2.41,7.92,4.13,12.93,6.63,15.08,4.86Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M231.23,263.25s-5.41-4.6-5.68-9.2c-.27-4.6,0-5.95,0-5.95,0,0-5.41-4.87-7.31-10.56-1.89-5.68,2.17-17.32,11.64-24.9,9.47-7.58,30.04-19.22,42.49-15.43,12.45,3.79,13.8,11.91,13.8,11.91,0,0,7.58-3.25,14.62,7.31,7.04,10.56,7.04,15.43,5.14,19.49s-4.33,7.31-4.33,7.31c0,0,1.62,1.08,0,6.77s-2.98,8.93-2.98,8.93l-7.85-19.22-15.43-15.97-41.14,24.36-2.98,15.16Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><g><path d="M276.97,219.4s-30.31,16.24-38.98,21.38l-8.66,5.14s2.17,31.4,4.87,34.65c2.71,3.25,20.84,16.78,24.9,19.22,4.06,2.44,11.37,2.98,14.35,.81,2.98-2.17,23.01-18.95,24.9-23.55,1.89-4.6-2.71-39.79-2.71-39.79,0,0-13.8-10.56-18.68-17.86Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M261,270.01s3.52,4.87,7.04,2.98c3.52-1.89,3.79-3.52,3.79-3.52" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M267.77,281.65s.81-1.89,4.33-4.06" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M239.89,252.15s6.5,6.23,15.97,0" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round; stroke-width:2px;"></path><path d="M272.64,252.15s6.5,6.23,15.97,0" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round; stroke-width:2px;"></path><path d="M239.35,245.65s12.18-7.58,16.24-7.31c4.06,.27,1.89,3.25,1.89,3.25,0,0-10.56,3.25-12.72,4.33-2.17,1.08-6.77,3.25-5.41-.27Z" style="fill:#263238;"></path><path d="M290.57,245.65s-12.18-7.58-16.24-7.31c-4.06,.27-1.89,3.25-1.89,3.25,0,0,10.56,3.25,12.72,4.33,2.17,1.08,6.77,3.25,5.41-.27Z" style="fill:#263238;"></path><g><path d="M230.96,248.09l.81,10.83s7.85,4.6,17.32,3.52c9.47-1.08,12.45-2.98,12.45-2.98l.81-12.99-31.4,1.62Z" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round; stroke-width:2px;"></path><path d="M299.44,248.09l-.81,10.83s-7.85,4.6-17.32,3.52c-9.47-1.08-12.45-2.98-12.45-2.98l-.81-12.99,31.4,1.62Z" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round; stroke-width:2px;"></path><line x1="262.36" y1="246.46" x2="268.04" y2="246.46" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round; stroke-width:2px;"></line><path d="M262.63,250.52s2.44-2.17,5.41,0" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round; stroke-width:2px;"></path></g></g></g><path d="M198.27,298.53c-.47,1.83-1.12,4.39-1.92,7.48l13.93,1.65c.54-2.81,1.19-5.59,1.67-7.51l-13.69-1.62Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g></g><g id="freepik--Lamp--inject-99"><g><path d="M76.9,425.39c.46,0,.93-.09,1.38-.28,1.81-.76,2.65-2.84,1.89-4.65l-44.2-104.54,60.32-68.38c1.3-1.47,1.16-3.71-.31-5.01-1.47-1.3-3.71-1.16-5.01,.31l-61.8,70.06c-.9,1.02-1.14,2.47-.61,3.72l45.07,106.59c.57,1.36,1.89,2.17,3.27,2.17Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><g><path d="M106.81,299.33c-.23-.22-.45-.43-.68-.65-15.56-15.56-19-37.35-7.68-48.67,11.32-11.32,33.11-7.88,48.67,7.68,.22,.22,.44,.45,.65,.68l-40.96,40.96Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M82.9,234.8h21.47v17.11c0,2.03-1.65,3.67-3.67,3.67h-14.12c-2.03,0-3.67-1.65-3.67-3.67v-17.11h0Z" transform="translate(333.22 352.37) rotate(135)" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><path d="M27.81,315.47c0-3.75,3.04-6.8,6.8-6.8s6.8,3.04,6.8,6.8-3.04,6.8-6.8,6.8-6.8-3.04-6.8-6.8Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M34.71,420.01H111.06c4.95,0,8.95,4.01,8.95,8.95H25.76c0-4.95,4.01-8.95,8.95-8.95Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g></g><g id="freepik--Books_2--inject-99"><g><g><g><path d="M468.06,417.44s-3.77,4.35,0,9.57h-68.57l-2.03-5.22,2.61-4.64,67.99,.29Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M399.49,426.77v-8.75h64.39v-2.19h-85.66c-3.62,0-6.56,2.94-6.56,6.56s2.94,6.56,6.56,6.56h85.66v-2.19h-64.39Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M399.49,422.4h0c0-2.42,1.96-4.38,4.38-4.38h66.8v-2.19h-68.04c-3.48,0-6.29,2.82-6.29,6.29h0c0,3.77,3.06,6.83,6.83,6.83h67.5v-2.19h-66.8c-2.42,0-4.38-1.96-4.38-4.38Z" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><g><path d="M468.91,390.48s-3.77,4.35,0,9.57h-68.57l-2.03-5.22,2.61-4.64,67.99,.29Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M400.34,399.82v-8.75h64.39v-2.19h-85.66c-3.62,0-6.56,2.94-6.56,6.56s2.94,6.56,6.56,6.56h85.66v-2.19h-64.39Z" style="fill:#1602FF; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M400.34,395.44h0c0-2.42,1.96-4.38,4.38-4.38h66.8v-2.19h-68.04c-3.48,0-6.29,2.82-6.29,6.29h0c0,3.77,3.06,6.83,6.83,6.83h67.5v-2.19h-66.8c-2.42,0-4.38-1.96-4.38-4.38Z" style="fill:#1602FF; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><g><path d="M463.42,404.39s-3.77,4.35,0,9.57h-68.57l-2.03-5.22,2.61-4.64,67.99,.29Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M394.85,413.72v-8.75h64.39v-2.19h-85.66c-3.62,0-6.56,2.94-6.56,6.56s2.94,6.56,6.56,6.56h85.66v-2.19h-64.39Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M394.85,409.34h0c0-2.42,1.96-4.38,4.38-4.38h66.8v-2.19h-68.04c-3.48,0-6.29,2.82-6.29,6.29h0c0,3.77,3.06,6.83,6.83,6.83h67.5v-2.19h-66.8c-2.42,0-4.38-1.96-4.38-4.38Z" style="fill:#1602FF; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g></g><g><path d="M369.25,377.9s3.77,4.35,0,9.57h68.57l2.03-5.22-2.61-4.64-67.99,.29Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M437.83,387.23v-8.75h-64.39v-2.19h85.66c3.62,0,6.56,2.94,6.56,6.56s-2.94,6.56-6.56,6.56h-85.66v-2.19h64.39Z" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M437.83,382.86h0c0-2.42-1.96-4.38-4.38-4.38h-66.8v-2.19h68.04c3.48,0,6.29,2.82,6.29,6.29h0c0,3.77-3.06,6.83-6.83,6.83h-67.5v-2.19h66.8c2.42,0,4.38-1.96,4.38-4.38Z" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><g><path d="M373.47,365.24s3.77,4.35,0,9.57h68.57l2.03-5.22-2.61-4.64-67.99,.29Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M442.05,374.58v-8.75h-64.39v-2.19h85.66c3.62,0,6.56,2.94,6.56,6.56s-2.94,6.56-6.56,6.56h-85.66v-2.19h64.39Z" style="fill:#1602FF; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M442.05,370.2h0c0-2.42-1.96-4.38-4.38-4.38h-66.8v-2.19h68.04c3.48,0,6.29,2.82,6.29,6.29h0c0,3.77-3.06,6.83-6.83,6.83h-67.5v-2.19h66.8c2.42,0,4.38-1.96,4.38-4.38Z" style="fill:#1602FF; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><g><path d="M363.35,352.16s3.77,4.35,0,9.57h68.57l2.03-5.22-2.61-4.64-67.99,.29Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M431.92,361.5v-8.75h-64.39v-2.19h85.66c3.62,0,6.56,2.94,6.56,6.56s-2.94,6.56-6.56,6.56h-85.66v-2.19h64.39Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M431.92,357.12h0c0-2.42-1.96-4.38-4.38-4.38h-66.8v-2.19h68.04c3.48,0,6.29,2.82,6.29,6.29h0c0,3.77-3.06,6.83-6.83,6.83h-67.5v-2.19h66.8c2.42,0,4.38-1.96,4.38-4.38Z" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><g><path d="M375.08,339.3s3.77,4.35,0,9.57h68.57l2.03-5.22-2.61-4.64-67.99,.29Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M443.65,348.63v-8.75h-64.39v-2.19h85.66c3.62,0,6.56,2.94,6.56,6.56s-2.94,6.56-6.56,6.56h-85.66v-2.19h64.39Z" style="fill:#1602FF; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M443.65,344.25h0c0-2.42-1.96-4.38-4.38-4.38h-66.8v-2.19h68.04c3.48,0,6.29,2.82,6.29,6.29h0c0,3.77-3.06,6.83-6.83,6.83h-67.5v-2.19h66.8c2.42,0,4.38-1.96,4.38-4.38Z" style="fill:#1602FF; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g><g><path d="M364.95,326.22s3.77,4.35,0,9.57h68.57l2.03-5.22-2.61-4.64-67.99,.29Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M433.52,335.55v-8.75h-64.39v-2.19h85.66c3.62,0,6.56,2.94,6.56,6.56s-2.94,6.56-6.56,6.56h-85.66v-2.19h64.39Z" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M433.52,331.17h0c0-2.42-1.96-4.38-4.38-4.38h-66.8v-2.19h68.04c3.48,0,6.29,2.82,6.29,6.29h0c0,3.77-3.06,6.83-6.83,6.83h-67.5v-2.19h66.8c2.42,0,4.38-1.96,4.38-4.38Z" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path></g></g></g><g id="freepik--Book_1--inject-99"><g><path d="M332.35,423.8c-18.6-10.77-72.32-15.69-72.32-15.69,0,0-53.72,4.92-72.32,15.69,0,0-2.59,1.21-1.7,1.88,.89,.67,.44,.67,3.11-.22,2.67-.89,35.12-10,41.57-10.23s17.34,7.78,19.12,9.34c1.78,1.56-.22,3.11,1.78,4,2,.89,8.45,0,8.45,0,0,0,6.45,.89,8.45,0,2-.89,0-2.45,1.78-4,1.78-1.56,12.67-9.56,19.12-9.34,6.45,.22,38.9,9.34,41.57,10.23,2.67,.89,2.22,.89,3.11,.22,.89-.67-1.7-1.88-1.7-1.88Z" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><polygon points="252.29 424.35 268.02 424.35 260.26 413.67 252.29 424.35" style="fill:#6e6e6e; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></polygon><g><g><path d="M260.01,403.38s-8.55-10.21-22.07-9.38c-13.52,.83-34.21,9.66-41.11,12.97-6.9,3.31-12.97,4.41-12.97,4.41l3.86,12.42s26.76-8.55,40.01-10.21c13.24-1.66,24.56,10.76,24.56,10.76l7.73-11.59v-9.38Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M254.5,421.04s-7.17-9.93-15.18-12.14c-8-2.21-12.14-.83-12.14-.83" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M236.01,404.49s6.9,.83,12.97,4.69" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><line x1="252.84" y1="411.38" x2="257.53" y2="416.35" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></line><path d="M244.84,401.73s10.21,2.76,15.18,11.04" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M238.22,397.59s6.9-1.1,11.86,2.76" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><line x1="186.34" y1="416.35" x2="199.31" y2="411.94" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></line><line x1="193.52" y1="416.9" x2="207.59" y2="412.49" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></line></g><g><path d="M260.29,403.38s8.55-10.21,22.07-9.38c13.52,.83,34.21,9.66,41.11,12.97,6.9,3.31,12.97,4.41,12.97,4.41l-3.86,12.42s-26.76-8.55-40.01-10.21c-13.24-1.66-24.56,10.76-24.56,10.76l-7.73-11.59v-9.38Z" style="fill:#fff; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M265.81,421.04s7.17-9.93,15.18-12.14c8-2.21,12.14-.83,12.14-.83" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M284.29,404.49s-6.9,.83-12.97,4.69" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><line x1="267.46" y1="411.38" x2="262.77" y2="416.35" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></line><path d="M275.47,401.73s-10.21,2.76-15.18,11.04" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><path d="M282.09,397.59s-6.9-1.1-11.86,2.76" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></path><line x1="333.96" y1="416.35" x2="320.99" y2="411.94" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></line><line x1="326.79" y1="416.9" x2="312.71" y2="412.49" style="fill:none; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></line></g></g></g></g><g id="freepik--Device--inject-99"><g><polygon points="170.44 428.96 49.25 428.96 33.99 342.21 155.18 342.21 170.44 428.96" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></polygon><polygon points="170.44 428.96 49.25 428.96 33.99 342.21 155.18 342.21 170.44 428.96" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></polygon><polygon points="166.58 428.96 45.39 428.96 30.14 342.21 151.33 342.21 166.58 428.96" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></polygon><polygon points="48.53 424.86 49.25 428.96 170.44 428.96 169.72 424.86 48.53 424.86" style="fill:#263238; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></polygon><rect x="163.09" y="425.35" width="72.58" height="3.61" style="fill:#c9c9c9; stroke:#263238; stroke-linecap:round; stroke-linejoin:round;"></rect></g></g></svg>















<!-- <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="335" height="220" viewBox="0 0 335 220">
  <defs>
    <linearGradient id="linear-gradient" x1="-1.223" y1="1.008" x2="-1.22" y2="1.008" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3851a2"/>
      <stop offset="1" stop-color="#71b5e3"/>
    </linearGradient>
  </defs>
  <g id="onboadin_a" transform="translate(4.338 21.253)">
    <rect id="safe_area" data-name="safe area" width="335" height="220" transform="translate(-4.338 -21.253)" fill="none"/>
    <g id="ground" transform="translate(-4.677)">
      <path id="ground-2" data-name="ground" d="M125.177,771.495H396.062a.732.732,0,0,1,0,1.455H125.177a.732.732,0,0,1,0-1.455" transform="translate(-92.779 -574.203)" fill="#092978" fill-rule="evenodd"/>
    </g>
    <g id="leafs" transform="translate(-4.677)">
      <g id="leaf">
        <path id="Path_13203" data-name="Path 13203" d="M303.772,510.316s12.035-5.968,10.248-15.211-11.571-8.271-10.248-15.568,7.749-18.662,1.5-26.932-16.641-3.313-17.945-16.138c-1.051-10.336-5.432-17.656-13.922-15.51-7.856,1.988-13.716,8.619-13.862,24.393-.157,16.795-11.007,12.076-11.143,24.449-.135,12.461,8.97,12.461,12.526,20.821,1.68,3.948-2.7,17.828,10.246,19.7Z" transform="translate(-185.073 -313.027)" fill="#f7a4c9"/>
        <path id="Path_13204" data-name="Path 13204" d="M346.386,446s-.032.282-.076.833c-.137,1.654-.413,5.682-.413,11.3,0,16.427,2.375,46.433,17.374,70.381a.636.636,0,0,1,.065.128h-1.571c-14.863-24.248-17.178-54.092-17.182-70.51,0-7.555.495-12.27.5-12.293a.674.674,0,0,1,.728-.63.707.707,0,0,1,.578.794" transform="translate(-256.735 -331.354)" fill="#fff"/>
      </g>
      <g id="leaf-2" data-name="leaf">
        <path id="Path_13205" data-name="Path 13205" d="M254.938,602.112s.384-11.406-6.719-16.049-14.355-8.26-13.567-15.743-1.149-16.313-12.53-16.48c-7.034-.1-4.392-8.185-11.541-9.8-4.344-.98-7.21,3.963-7.764,8.817-.634,5.528,2.238,10.964-1.054,15.466-4.274,5.835-6.009,16.713,3.175,19.81s1,12.575,13.508,13.976Z" transform="translate(-147.737 -404.823)" fill="#ef6aa0" fill-rule="evenodd"/>
        <path id="Path_13206" data-name="Path 13206" d="M238.818,563.591c.239,7.265,3.2,17.608,8.257,27.477s12.208,19.288,20.751,24.835a.7.7,0,0,1,.27.338H265.84c-8.232-5.807-15.029-14.944-19.912-24.474-5.139-10.042-8.16-20.507-8.423-28.123a.694.694,0,0,1,.634-.742.684.684,0,0,1,.68.689" transform="translate(-176.955 -418.953)" fill="#fff"/>
      </g>
      <g id="leaf-3" data-name="leaf">
        <path id="Path_13207" data-name="Path 13207" d="M685.248,439.64s-15.241-7.557-12.978-19.26,14.651-10.475,12.978-19.716-9.814-23.63-1.9-34.1,21.072-4.2,22.72-20.435c1.332-13.088,6.879-22.357,17.628-19.639,9.951,2.517,17.372,10.914,17.555,30.888.2,21.267,13.938,15.291,14.108,30.959C755.53,404.112,744,404.112,739.5,414.7c-2.127,5,3.419,22.571-12.975,24.941Z" transform="translate(-500.71 -242.642)" fill="#f7a4c9"/>
        <path id="Path_13208" data-name="Path 13208" d="M805.394,358.2s.035.358.095,1.055c.174,2.094.521,7.2.521,14.3.006,20.8-3.005,58.8-22,89.121a1.192,1.192,0,0,0-.082.163h1.989c18.821-30.7,21.751-68.5,21.757-89.285,0-9.567-.625-15.537-.628-15.566a.852.852,0,0,0-.922-.8.892.892,0,0,0-.729,1" transform="translate(-584.073 -265.849)" fill="#fff"/>
      </g>
      <g id="leaf-4" data-name="leaf">
        <path id="Path_13209" data-name="Path 13209" d="M787.6,555.879s-2.4-14.444,6.595-20.325,18.181-10.456,17.183-19.933,1.455-20.658,15.863-20.868c8.909-.127,5.566-10.366,14.616-12.406,5.5-1.241,9.127,5.019,9.831,11.165.8,7-2.835,13.882,1.335,19.583,5.414,7.388,7.608,21.165-4.019,25.084s-1.266,15.925-17.1,17.7Z" transform="translate(-586.594 -358.88)" fill="#ef6aa0" fill-rule="evenodd"/>
        <path id="Path_13210" data-name="Path 13210" d="M897.789,507.1c-.3,9.2-4.047,22.3-10.453,34.793s-15.463,24.426-26.278,31.45a.873.873,0,0,0-.344.427h2.86c10.424-7.355,19.03-18.924,25.214-30.991,6.5-12.716,10.329-25.968,10.664-35.612a.879.879,0,0,0-.8-.939.866.866,0,0,0-.862.872" transform="translate(-641.283 -376.771)" fill="#fff"/>
      </g>
    </g>
    <g id="man" transform="translate(-4.677)">
      <g id="legs">
        <path id="Path_13215" data-name="Path 13215" d="M876.62,737.609h13.723a1.285,1.285,0,0,0,1.259-1.137,1.339,1.339,0,0,0,.006-.275l-.555-7.437a7.754,7.754,0,0,0-1.373-.468,1.4,1.4,0,0,1-1.016-.952l-.029-.107c-.313-1.432-2.314-1.886-2.683-.489-.047.181-.088.357-.126.525-.006.042-.016.086-.022.126-.06.3-.1.577-.136.82v.015a8.078,8.078,0,0,0-.07,1.011c-.18.237-.357.472-.527.7-.085.115-.171.229-.253.342-.183.241-.357.476-.527.7-.079.107-.161.21-.24.312-.189.245-.379.478-.568.7-.082.1-.167.2-.252.287a3.947,3.947,0,0,1-2.267,1.414c-1.487.21-3.918.379-5.092,2.632a.927.927,0,0,0-.06.144.869.869,0,0,0,.808,1.137" transform="translate(-652.501 -540.266)" fill="#f9a719" fill-rule="evenodd"/>
        <path id="Path_13216" data-name="Path 13216" d="M876.62,768.38h13.723a1.283,1.283,0,0,0,1.259-1.135H875.811a.867.867,0,0,0,.808,1.135" transform="translate(-652.501 -571.04)" fill="#f2c686"/>
        <path id="Path_13217" data-name="Path 13217" d="M930.288,466.563v8.471c0,9.393-1.654,30.019-2.251,41.417l-6.285-.123-5.158-41.111-8.811,1.266-.436-11.218c-.1-38.782,19.7-29.751,17.969-7.763-.193,2.42,5.894,6.4,4.972,9.06" transform="translate(-676.027 -326.434)" fill="#111243" fill-rule="evenodd"/>
        <path id="Path_13218" data-name="Path 13218" d="M906.865,556.894h11.662l-4.18,47.654-6.623.171Z" transform="translate(-675.668 -414.481)" fill="#111243" fill-rule="evenodd"/>
        <path id="Path_13219" data-name="Path 13219" d="M955.229,745.173c.114,2.066,7.211,1.355,8.344.734a1.422,1.422,0,0,0,.66-1.26,9.143,9.143,0,0,0-.546-2.926c-.455-1.455-.395-1.428-.71-2.422,0,0,.082-1.619-2.241-2.237s-3.179,3.41-3.577,4.184c-.363.711-1.777,1.682-1.922,3.429a3.694,3.694,0,0,0-.006.5" transform="translate(-711.698 -548.528)" fill="#f9a719" fill-rule="evenodd"/>
        <path id="Path_13220" data-name="Path 13220" d="M955.229,767.435c.114,2.066,7.211,1.355,8.344.734a1.422,1.422,0,0,0,.66-1.26,9,9,0,0,1-4.025.9,10.586,10.586,0,0,1-4.972-.874,3.694,3.694,0,0,0-.006.5" transform="translate(-711.698 -570.79)" fill="#f2c686"/>
        <path id="Path_13221" data-name="Path 13221" d="M935.175,581.814l-2.889,40.856.963-.076,2.945-32.652Z" transform="translate(-694.609 -433.028)" fill-rule="evenodd"/>
      </g>
      <g id="body">
        <path id="Path_13222" data-name="Path 13222" d="M857.961,404.843c-2.153,4.745-3.558,6.252-9.727,7.174-5,.748-14.528-10.54-15.247-12.08s2.014-5.566,2.9-5.1,11.147,8.335,11.848,8.285,3.132-1.161,8.625-4.738c.521-.335,3.123,4.168,1.6,6.46" transform="translate(-620.536 -293.838)" fill="#da7a27" fill-rule="evenodd"/>
        <path id="Path_13223" data-name="Path 13223" d="M929.621,405.9c2.055,3.072-4.022,22.119-2.308,37.728-9.609,1.656-19.377,1.473-25.116-.359.012-11.812.647-28.036,3.087-36.856.574-1.431,1.708-2.45,4.514-3.239,6.838-1.921,17.009-1.484,19.822,2.726" transform="translate(-672.191 -299.26)" fill="#f9a719" fill-rule="evenodd"/>
        <path id="Path_13224" data-name="Path 13224" d="M926.055,402.86c-3.766,1.928-4.707,18.177-5.73,40.858a47.253,47.253,0,0,0,9.647,0c.395-22.132,4.092-37.314,5.518-41.083a38.332,38.332,0,0,0-9.436.229" transform="translate(-685.697 -299.531)" fill="#f9a719" fill-rule="evenodd"/>
        <path id="Path_13227" data-name="Path 13227" d="M948.7,493.077l16.056,9.461-6.563,11.209-16.059-9.461Z" transform="translate(-701.944 -366.983)" fill="#5b285b" fill-rule="evenodd"/>
        <path id="Path_13228" data-name="Path 13228" d="M966.24,542.4l-2.131,2.13-.691-.406a5.627,5.627,0,0,0,.612,1.219,1.437,1.437,0,0,0,.754.254,5.081,5.081,0,0,0,.322.609c.063.048.492.254.707.151s.392.462.392.462a3.107,3.107,0,0,0,.729.061c.224-.036.23.343.23.343a2.408,2.408,0,0,0,1.392-1.022c.417-.774,1.083-1.848,1.083-1.848a24.918,24.918,0,0,0-2.106-1.64,5.167,5.167,0,0,0-1.294-.313" transform="translate(-717.804 -403.693)" fill="#f4b1b0" fill-rule="evenodd"/>
        <path id="Path_13229" data-name="Path 13229" d="M978.3,416.392c-2.421-5.3.571-7.666,3.495-7.2,5.294,1.353,10.68,17.519,9.284,21.719-.975,2.839-11.491,14.748-13.076,14.322-.369-.1-3.618-1.164-2.885-3.325.23-.678,8.1-11.875,8.315-12.885.354-1.681-4.35-11.115-5.133-12.628" transform="translate(-726.44 -304.506)" fill="#da7a27" fill-rule="evenodd"/>
        <path id="Path_13230" data-name="Path 13230" d="M833.055,392.7a4.987,4.987,0,0,1-2.15,4.222c-.117-.205-.8-1.094-1.26-1.777-.975-1.467,1.332-4.534,2.008-3.853Z" transform="translate(-617.959 -291.153)" fill="#da7a27" fill-rule="evenodd"/>
      </g>
      <g id="head">
        <path id="Path_13225" data-name="Path 13225" d="M938.512,353.8a7.2,7.2,0,0,0,2.75,3.261l-.171,1.953-.3.223c-1.193,1.118-.293,4.022.793,5.082,1.9-.091,6.146-3.507,5.281-5l-.385-.5.095-2.775c.164,0,1.414-.6,1.777-.687a1.862,1.862,0,0,0,1.023-1.469c.085-.728-1.054-.568-1.054-.568,1.487-1.777,3.135-3.2,1.247-5.018s-6.412-3.117-6.412-3.117l-3.207-1.069c.287.981-.126,3.59-.969,5.535-.758,1.745-1.247,2.274-.467,4.144" transform="translate(-698.953 -256.122)" fill="#f4b1b0" fill-rule="evenodd"/>
        <path id="Path_13226" data-name="Path 13226" d="M953.939,391.921s-1.746,2.583-3.047,2.739-.634.021-1.108-.069l.17-1.954c.875.2,1.746.192,3.984-.717" transform="translate(-707.646 -291.696)" fill="#e78284" fill-rule="evenodd"/>
        <path id="Path_13231" data-name="Path 13231" d="M945.095,337.459c.767.683.859,1.813.369,3.649-.145.541-.382.958.42.436.369-.239.982-.588,1.228-.44a1.038,1.038,0,0,1,.066,1.532c-.158.146-.464.374-.464.374a2.415,2.415,0,0,0,1.98-.559c.931-.8,3.605-3.209,3-6.551-1.111-6.113-6.219-4.545-6.115-6.644.032-.614.319-1.062-.107-1.243-4.83-2.051-9.338,1.157-9.01,4.378.48,4.733,6.427,3.11,8.634,5.068" transform="translate(-697.707 -243.65)" fill-rule="evenodd"/>
        <path id="Path_13232" data-name="Path 13232" d="M972.136,369.2a3.105,3.105,0,0,1-.527,3.944c-1.124,1.17-2.709,1.3-3.536.293a3.1,3.1,0,0,1,.531-3.944c1.121-1.17,2.705-1.3,3.532-.293" transform="translate(-720.903 -274.283)" fill="#e41f26" fill-rule="evenodd"/>
        <path id="Path_13233" data-name="Path 13233" d="M973.356,346.4a3.106,3.106,0,0,1-.53,3.944c-1.124,1.171-2.705,1.3-3.536.293a3.106,3.106,0,0,1,.53-3.944,2.766,2.766,0,0,1,2.469-.931h0l-.174-.292a5.822,5.822,0,0,0,.957-3.737c-.212-2.2-.894-2.816-2.532-4.043l.218-.24c4.246.735,5.215,5.816,2.285,8.625l-.079-.034a1.865,1.865,0,0,1,.392.358" transform="translate(-721.81 -251.154)" fill="#ee4a72" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="screen" transform="translate(-4.677)">
      <g id="phone">
        <path id="Path_13211" data-name="Path 13211" d="M535.531,71.844l-75.9-2.012a12.4,12.4,0,0,0-12.278,12.445V236.82a12.4,12.4,0,0,0,12.278,12.446H533.3A12.4,12.4,0,0,0,545.58,236.82V82.277c0-6.846-3.3-10.433-10.049-10.433" transform="translate(-333.303 -51.974)" fill="#284a9e" fill-rule="evenodd"/>
        <path id="Path_13234" data-name="Path 13234" d="M483.639,69.832a12.114,12.114,0,0,0-1.689.118v0h-.011l-.211.031h0l-.186.031h0l-.157.029v0h-.015q-.539.1-1.062.252h0q-.254.073-.5.157v0h-.006l-.089.029v0h-.007l-.088.031h0q-.517.183-1.013.409v0h-.006q-.534.244-1.039.538h0l-.157.094v0h0l-.155.094h0q-.34.21-.664.442h0q-.257.184-.505.379h0q-.193.153-.38.314v0h0q-.127.109-.251.221h0q-.447.406-.854.854h0c-.075.082-.149.165-.221.249v0h0q-.16.186-.313.38-.2.247-.38.5v0h0a12.539,12.539,0,0,0-.979,1.614v0h0l-.031.062h0q-.133.265-.253.537h0q-.117.264-.221.533v.006h0q-.175.451-.315.918h0c-.022.072-.043.144-.063.216v.006h0q-.086.309-.157.624v.01h0a12.489,12.489,0,0,0-.283,2.057v0q-.018.334-.018.672V236.82q0,.551.048,1.091h0v.02c.016.184.036.368.061.55h0v.019c.026.2.057.391.093.583h0v.012q.043.233.094.463h0v0c.009.042.019.083.029.124h0v.013q.044.185.093.367h0v.007a12.421,12.421,0,0,0,.664,1.861h0v0a12.493,12.493,0,0,0,.852,1.58h0v0q.152.239.315.471h0v0c.052.074.1.147.158.219h0l.094.126h0q.109.144.221.284h0q.154.192.315.379h0q.139.16.283.315h0v0a12.547,12.547,0,0,0,1.863,1.645h.005v0c.1.074.209.147.315.218h0l.093.062h0l.094.062h0q.2.131.41.253h0q.279.165.566.316h0v0l.058.03h.006v0q.156.08.314.155h0v0q.51.244,1.045.442.326.12.661.221h0v0q.28.085.565.157h0l.123.031h0q.292.07.589.126h.012v0q.559.1,1.133.156h.007c.358.032.721.049,1.088.049h73.9q.544,0,1.077-.049v0h.017c.1-.009.2-.019.3-.031h.009c.082-.009.163-.019.245-.031h.007q.2-.028.4-.063h0q.317-.055.629-.125h.006q.252-.058.5-.126h0c.073-.02.146-.04.219-.062v0h.005q.431-.127.85-.285h0l.158-.061v0h.006l.152-.061v0h.005c.148-.061.294-.123.439-.189h0l.061-.029v0h.007a12.279,12.279,0,0,0,1.133-.6v0h0q.422-.252.821-.536v0h0q.257-.183.5-.378v0h0l.155-.126h0q.1-.078.189-.158h0c.085-.073.17-.146.253-.221h0a12.553,12.553,0,0,0,1.836-2.058h0q.2-.277.379-.565v0h0q.146-.231.282-.468v-.007h0q.063-.11.122-.22h0q.2-.371.379-.756v0h0c.1-.218.2-.44.283-.664h0q.216-.551.38-1.127v-.013h0c.021-.073.04-.147.059-.22h0q.033-.125.063-.25v0h0q.09-.372.158-.752v-.008h0c.011-.061.02-.122.03-.184V238.8h0c.023-.145.044-.291.062-.438v-.006h0q.04-.323.063-.651v-.046h0c.018-.277.029-.556.029-.838V82.277q0-.435-.03-.862h0v-.022q-.023-.324-.062-.643h0v-.013q-.039-.313-.093-.621h0V80.1q-.055-.312-.125-.619h0v-.007c-.009-.04-.02-.08-.029-.12h0v-.01q-.072-.3-.158-.591h0q-.152-.516-.345-1.013h0v-.008q-.118-.3-.251-.594h0v-.006q-.2-.45-.442-.881h0v0q-.061-.11-.124-.22h0v0q-.167-.288-.348-.565h0c-.041-.063-.083-.127-.126-.19h0c-.072-.107-.146-.211-.222-.316h0q-.093-.128-.188-.253h0v0q-.185-.241-.38-.473h0q-.3-.359-.633-.7h0l-.094-.095h0l-.031-.031h0l-.031-.031h0q-.232-.227-.474-.443h0q-.2-.178-.409-.347h0v0l-.156-.125h0v0q-.471-.371-.978-.695h0v0c-.135-.086-.271-.17-.409-.251h0v0a12.232,12.232,0,0,0-1.234-.631h0l-.219-.095h0l-.15-.062h-.008v0l-.154-.06h0v0a12.157,12.157,0,0,0-1.511-.473h-.009v0l-.123-.029h0q-.308-.071-.621-.126h-.012v0q-.313-.054-.631-.093h0q-.342-.041-.69-.063H558.3c-.251-.016-.5-.025-.76-.025Z" transform="translate(-351.318 -51.974)" fill="url(#linear-gradient)"/>
      </g>
      <path id="button" d="M458.027,172.671c-.9,0-1.641-.987-1.641-2.194V144.406c0-1.205.738-2.193,1.641-2.193Zm0,21.209c-.9,0-1.641-.987-1.641-2.192V180.509c0-1.206.738-2.193,1.641-2.193Z" transform="translate(-340.035 -105.845)" fill="#fff" fill-rule="evenodd"/>
      <path id="Path_13235" data-name="Path 13235" d="M493.646,267.7v0h-.015l-.143-.028v0h-.019q-.183-.04-.36-.095h0l-.093-.03v0h-.005q-.125-.042-.247-.092v0h-.008q-.108-.044-.213-.093v0h0c-.063-.03-.125-.06-.186-.093v0h0q-.11-.058-.217-.122v0h-.007q-.191-.115-.372-.249v0h-.005c-.106-.079-.21-.162-.31-.25v0h0l-.028-.025v-.007H491.4a5.275,5.275,0,0,1-.624-.663v0h0c-.032-.04-.063-.08-.094-.121V265.8h0q-.079-.106-.153-.217v-.005h0l-.06-.091v0h0q-.1-.151-.187-.31v-.006h0c-.09-.161-.173-.327-.249-.5v-.008h0q-.048-.109-.092-.22v0h0a6.183,6.183,0,0,1-.252-.79v0h0c-.011-.044-.021-.088-.031-.132v-.026h-.006c-.009-.041-.018-.082-.026-.124v0h0c-.024-.123-.045-.248-.063-.374v-.037h-.005a6.576,6.576,0,0,1-.056-.856V110.627a6.588,6.588,0,0,1,.055-.851h.006v-.042c.017-.125.038-.248.062-.37h0v-.007q.028-.14.062-.278h0v-.006c.036-.148.079-.294.125-.437h0v0c.01-.031.02-.061.031-.092h0v0q.044-.127.092-.25h0v-.006q.03-.077.062-.152h0v0a5.911,5.911,0,0,1,.408-.79h0v0c.019-.03.038-.061.058-.091h.006v-.009q.146-.226.311-.434h0v-.006q.061-.077.125-.152h0a5.232,5.232,0,0,1,.529-.537h.008V106.1l.028-.024h0v0a5,5,0,0,1,.406-.314h0v0q.093-.064.188-.124h0q.078-.049.158-.094h0l.056-.031h.007v0q.152-.083.309-.154h.007v0q.108-.049.219-.092h0q.133-.051.269-.094h.015v0l.091-.027h0q.122-.035.247-.062h.006c.05-.011.1-.021.151-.03h.039v-.007a4.151,4.151,0,0,1,.54-.057h.092v0h79.365v0h.1q.187.007.371.031h0c.063.008.125.017.187.028v0h.018l.14.028v0h.017q.119.026.236.059v0h.014a4.328,4.328,0,0,1,.522.185v0h.01a4.462,4.462,0,0,1,.432.215v.007h.012l.052.03v0h0q.127.075.25.158h0q.112.076.22.158h0c.053.04.106.082.157.124v0h0c.085.071.168.145.25.222l.095.092v0h0l.029.028v0h0l.028.029v0h0l.029.03v0h0l.03.032q.114.121.221.251v0h0q.064.077.124.156v0h0c.031.041.062.082.093.125v0h0q.1.135.188.276v.008h.005l.058.093v0h0q.064.106.125.216v.006h0l.029.052v.011h.006q.1.183.184.375v.005h0c.032.072.063.145.092.219v0h0q.049.124.094.25v0h0l.03.089v.006h0l.03.091v0h0q.071.226.125.46v.015h0q.054.236.091.48v.026h0q.015.106.028.213v.04h0q.018.172.027.346v.129h.005c0,.073,0,.145,0,.218V262.091c0,.075,0,.149,0,.223h-.005v.124q-.009.177-.028.351h0v.035c-.008.073-.018.146-.029.218h0v.022q-.025.165-.058.327h-.005v.026q-.014.067-.029.133h0v.01c-.009.039-.018.078-.028.116h0v.014q-.056.219-.126.43h0v0q-.069.209-.152.41h-.005v.013q-.031.073-.063.145h0q-.072.161-.152.316h-.005v.011l-.028.053h0v.006l-.031.057h0v0q-.089.161-.187.315h0v0l-.059.091h0v.006l-.06.089h0v0q-.089.128-.183.25h-.006v.008a5.251,5.251,0,0,1-.754.784h0v0q-.137.115-.28.219h0v0l-.085.06h-.01v.007q-.168.115-.343.215h0v0l-.053.029h-.01v.006q-.151.082-.307.153h-.008v0l-.063.028h0c-.052.022-.1.043-.156.063h0q-.134.051-.272.094h-.013v0l-.095.028a4.241,4.241,0,0,1-.416.1h-.026v0q-.093.016-.187.028h0a4.177,4.177,0,0,1-.508.031H494.355a4.2,4.2,0,0,1-.709-.061" transform="translate(-364.693 -78.118)" fill="#ecf4ff" fill-rule="evenodd"/>
      <path id="Path_13236" data-name="Path 13236" d="M573.575,96.193H494.354a4.9,4.9,0,0,0-4.874,4.888v9.439h88.969v-9.439a4.9,4.9,0,0,0-4.874-4.888" transform="translate(-364.692 -71.594)" fill="#c3dcfa" fill-rule="evenodd"/>
      <g id="mobile_contains" data-name="mobile contains">
        <g id="lines">
          <path id="Path_13265" data-name="Path 13265" d="M529.159,330.422h66.461a1.964,1.964,0,0,1,1.956,1.962h0a1.965,1.965,0,0,1-1.956,1.962H529.159a1.965,1.965,0,0,1-1.956-1.962h0a1.964,1.964,0,0,1,1.956-1.962" transform="translate(-392.798 -245.924)" fill="#e8b3ce" fill-rule="evenodd"/>
          <path id="Path_13266" data-name="Path 13266" d="M529.159,365.5h66.461a1.965,1.965,0,0,1,1.956,1.962h0a1.965,1.965,0,0,1-1.956,1.962H529.159a1.965,1.965,0,0,1-1.956-1.962h0a1.965,1.965,0,0,1,1.956-1.962" transform="translate(-392.798 -272.033)" fill="#e8b3ce" fill-rule="evenodd"/>
          <path id="Path_13267" data-name="Path 13267" d="M529.159,400.583h66.461a1.964,1.964,0,0,1,1.956,1.962h0a1.965,1.965,0,0,1-1.956,1.962H529.159a1.965,1.965,0,0,1-1.956-1.962h0a1.964,1.964,0,0,1,1.956-1.962" transform="translate(-392.798 -298.144)" fill="#e8b3ce" fill-rule="evenodd"/>
          <path id="Path_13268" data-name="Path 13268" d="M529.159,435.663h66.461a1.962,1.962,0,0,1,0,3.924H529.159a1.962,1.962,0,0,1,0-3.924" transform="translate(-392.798 -324.252)" fill="#e8b3ce" fill-rule="evenodd"/>
          <path id="Path_13269" data-name="Path 13269" d="M529.159,470.743h28.235a1.964,1.964,0,0,1,1.956,1.962h0a1.964,1.964,0,0,1-1.956,1.962H529.159A1.964,1.964,0,0,1,527.2,472.7h0a1.965,1.965,0,0,1,1.956-1.962" transform="translate(-392.798 -350.361)" fill="#e8b3ce" fill-rule="evenodd"/>
        </g>
        <g id="descriptions">
          <path id="Path_13270" data-name="Path 13270" d="M529.159,556.894h66.461a1.965,1.965,0,0,1,1.956,1.962h0a1.965,1.965,0,0,1-1.956,1.962H529.159a1.965,1.965,0,0,1-1.956-1.962h0a1.965,1.965,0,0,1,1.956-1.962" transform="translate(-392.798 -414.481)" fill="#e85da9" fill-rule="evenodd"/>
          <path id="Path_13271" data-name="Path 13271" d="M529.159,591.974h28.235a1.964,1.964,0,0,1,1.956,1.962h0a1.964,1.964,0,0,1-1.956,1.962H529.159a1.965,1.965,0,0,1-1.956-1.962h0a1.965,1.965,0,0,1,1.956-1.962" transform="translate(-392.798 -440.59)" fill="#e85da9" fill-rule="evenodd"/>
          <path id="Path_13272" data-name="Path 13272" d="M530.636,620.872h6.849a3.448,3.448,0,0,1,3.433,3.444v7.09a3.448,3.448,0,0,1-3.433,3.444h-6.849a3.448,3.448,0,0,1-3.433-3.444v-7.09a3.448,3.448,0,0,1,3.433-3.444" transform="translate(-392.798 -462.099)" fill="#ea96bc" fill-rule="evenodd"/>
          <path id="Path_13273" data-name="Path 13273" d="M594.005,620.872h6.849a3.449,3.449,0,0,1,3.433,3.444v7.09a3.448,3.448,0,0,1-3.433,3.444h-6.849a3.448,3.448,0,0,1-3.433-3.444v-7.09a3.448,3.448,0,0,1,3.433-3.444" transform="translate(-440.011 -462.099)" fill="#ea96bc" fill-rule="evenodd"/>
          <path id="Path_13274" data-name="Path 13274" d="M660.69,620.872h6.848a3.449,3.449,0,0,1,3.433,3.444v7.09a3.448,3.448,0,0,1-3.433,3.444H660.69a3.448,3.448,0,0,1-3.433-3.444v-7.09a3.449,3.449,0,0,1,3.433-3.444" transform="translate(-489.696 -462.099)" fill="#ea96bc" fill-rule="evenodd"/>
          <path id="Path_13275" data-name="Path 13275" d="M724.059,620.872h6.849a3.448,3.448,0,0,1,3.433,3.444v7.09a3.448,3.448,0,0,1-3.433,3.444h-6.849a3.448,3.448,0,0,1-3.433-3.444v-7.09a3.448,3.448,0,0,1,3.433-3.444" transform="translate(-536.909 -462.099)" fill="#ea96bc" fill-rule="evenodd"/>
        </g>
        <rect id="divider" width="75.506" height="0.667" transform="translate(134.405 132.569)" fill="#b1d2fc"/>
      </g>
      <g id="searchbar">
        <path id="Path_13238" data-name="Path 13238" d="M581.212,107.839h53.735A3.571,3.571,0,0,1,638.5,111.4h0a3.571,3.571,0,0,1-3.555,3.565H581.212a3.569,3.569,0,0,1-3.555-3.565h0a3.57,3.57,0,0,1,3.555-3.565" transform="translate(-430.389 -80.262)" fill="#fff" fill-rule="evenodd"/>
        <path id="Path_13239" data-name="Path 13239" d="M510.615,107.839h0a3.538,3.538,0,0,1,3.521,3.532v.067a3.521,3.521,0,1,1-7.043,0v-.067a3.537,3.537,0,0,1,3.521-3.532" transform="translate(-377.815 -80.262)" fill="#fff" fill-rule="evenodd"/>
        <path id="Path_13240" data-name="Path 13240" d="M545.812,107.839h0a3.536,3.536,0,0,1,3.52,3.532v.067a3.536,3.536,0,0,1-3.52,3.531h0a3.536,3.536,0,0,1-3.52-3.531v-.067a3.537,3.537,0,0,1,3.52-3.532" transform="translate(-404.04 -80.262)" fill="#fff" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="man-2" data-name="man" transform="translate(-4.677)">
      <g id="legs-2" data-name="legs">
        <path id="Path_13244" data-name="Path 13244" d="M375.438,738.213H361.856a1.267,1.267,0,0,1-1.247-1.116,1.3,1.3,0,0,1-.008-.272l.552-7.315a7.894,7.894,0,0,1,1.356-.458,1.391,1.391,0,0,0,1.008-.937c.009-.034.019-.07.026-.105.313-1.409,2.291-1.856,2.658-.481.046.177.087.351.124.517.008.042.017.085.024.125.059.294.1.567.132.805l0,.015a7.8,7.8,0,0,1,.067,1q.269.351.522.69c.087.113.169.224.25.336.179.238.352.47.524.692.078.1.158.205.235.306.189.241.374.472.561.685.083.1.167.192.252.283a3.9,3.9,0,0,0,2.241,1.39c1.475.208,3.879.374,5.042,2.589a.946.946,0,0,1,.058.141.856.856,0,0,1-.8,1.116" transform="translate(-268.667 -540.861)" fill="#269dd8" fill-rule="evenodd"/>
        <path id="Path_13245" data-name="Path 13245" d="M375.472,768.481H361.89a1.267,1.267,0,0,1-1.247-1.116H376.27a.856.856,0,0,1-.8,1.116" transform="translate(-268.701 -571.129)" fill="#263c86"/>
        <path id="Path_13246" data-name="Path 13246" d="M358.13,558.24H346.058l4.135,47.5,6.558.173Z" transform="translate(-257.834 -415.482)" fill="#273373" fill-rule="evenodd"/>
        <path id="Path_13247" data-name="Path 13247" d="M301.516,468.273v8.445c0,9.362,1.635,29.921,2.228,41.284l6.219-.123,5.693-45.955,9.177.008.126-4.845c-.435-38.735-20.236-29.763-18.521-7.845.187,2.412-5.837,6.384-4.922,9.031" transform="translate(-224.577 -327.791)" fill="#273373" fill-rule="evenodd"/>
        <path id="Path_13248" data-name="Path 13248" d="M317.391,745.659c-.113,2.033-7.138,1.331-8.26.722a1.392,1.392,0,0,1-.65-1.239,8.829,8.829,0,0,1,.541-2.877c.45-1.431.387-1.4.7-2.383,0,0-.081-1.59,2.221-2.2s3.146,3.353,3.538,4.114c.359.7,1.758,1.652,1.9,3.372a3.259,3.259,0,0,1,.008.488" transform="translate(-229.832 -548.991)" fill="#269dd8" fill-rule="evenodd"/>
        <path id="Path_13249" data-name="Path 13249" d="M317.409,767.549c-.113,2.033-7.138,1.331-8.26.722a1.392,1.392,0,0,1-.65-1.239,8.954,8.954,0,0,0,3.981.886,10.567,10.567,0,0,0,4.922-.858,3.259,3.259,0,0,1,.008.488" transform="translate(-229.85 -570.881)" fill="#263c86"/>
      </g>
      <g id="body-2" data-name="body">
        <path id="Path_13250" data-name="Path 13250" d="M364.335,423.167c2.132,4.729,4.778,10.149,10.883,11.068,4.952.745,7.567-10.688,8.281-12.224s-.5-6.053-2.868-5.084-5.487,8.3-6.141,8.253c-.767-.05-1.314-8.664-6.056-11.428-.739-.431-5.016,7.381-4.1,9.416" transform="translate(-271.356 -307.931)" fill="#269dd8" fill-rule="evenodd"/>
        <path id="Path_13251" data-name="Path 13251" d="M295.6,404.89a9.235,9.235,0,0,0-7.356,3.53c-2.034,3.062,3.981,22.047,2.286,37.607h25.344a336.088,336.088,0,0,0-2.08-36.872c-3.155-5.017-13.342-4.266-18.193-4.266" transform="translate(-214.438 -301.318)" fill="#269dd8" fill-rule="evenodd"/>
        <path id="Path_13252" data-name="Path 13252" d="M328.468,404.7c3.733,2.039,4.592,17.674,5.621,40.916h-9.476c-.407-22.824-4.068-37.249-5.483-41.145a37.7,37.7,0,0,1,9.338.229" transform="translate(-237.771 -300.902)" fill="#fff" fill-rule="evenodd"/>
        <path id="Path_13255" data-name="Path 13255" d="M267.385,418.421c1.1-2.287.889-4.336-1.221-5.757-4.073-2.667-11.139,9.433-13.516,15.906-.963,2.628,2.516,15.351,3.414,16.529.472.621,1.973,3.766,3.775.5.033-.063-1.712-12.72-1.521-14.028.248-1.695,8.3-11.544,9.068-13.146" transform="translate(-188.114 -306.854)" fill="#269dd8" fill-rule="evenodd"/>
        <path id="Path_13256" data-name="Path 13256" d="M424.9,424.921c.191,1.631,2.334,3.738,3.553,3.872.061-.255.528-1.416.807-2.286.6-1.869-2.775-4.454-3.292-3.52Z" transform="translate(-316.58 -314.671)" fill="#269dd8" fill-rule="evenodd"/>
        <path id="Path_13257" data-name="Path 13257" d="M431.277,393.913c-.048-.761-.195-3.779.2-4.405.2-.32.293-.227.454-.107a1.337,1.337,0,0,1,.374.707,11.292,11.292,0,0,1,.178,1.34c1.351-1.289,2.318-3.683,3.1-5.356.318-.68,1.082-.835.9.268a20.948,20.948,0,0,1-1.112,3.1l2.262-.213c.469-.461.658.229.476.593a8.674,8.674,0,0,1-2.142,1.7l1.491-.674c.5-.364.4.413.289.576A8.821,8.821,0,0,1,435.8,392.6l1.176-.253c.687-.469,0,.8-.2.927a5.693,5.693,0,0,0-1.475.842,17.083,17.083,0,0,0-2.215,4.184,5.41,5.41,0,0,1-2.588-2.732Z" transform="translate(-320.743 -286.959)" fill="#f4b1b0" fill-rule="evenodd"/>
        <path id="Path_13258" data-name="Path 13258" d="M271.423,540.121l.394,1.61,1.373,1.766s.869.674.776.956c-.018.053-1.567-.312-1.606-.251-.332.516.391,3.6.335,3.588-5.063-1.094-2.736-3.8-4.142-7.281-.48-1.19,1.9-1.143,2.87-.388" transform="translate(-200.041 -401.595)" fill="#f4b1b0" fill-rule="evenodd"/>
      </g>
      <g id="head-2" data-name="head">
        <path id="Path_13253" data-name="Path 13253" d="M332.873,363.016a6.379,6.379,0,0,1-3.184,2.343V366.6l.221.3c.8,1.414.367,4.258-.967,4.951-2.088-.756-4.909-2.621-5.476-5.241l.372-.348.508-.36.248-3.1c-.157-.047-1.163-.986-1.484-1.175a1.864,1.864,0,0,1-.545-1.7c.13-.718,1.163-.232,1.163-.232-.9-2.129-2.044-3.965.267-5.149s6.967-1.094,6.967-1.094l2.888-.307c-.556.851-.462,3.684-.223,5.785.213,1.884.524,2.532-.756,4.085" transform="translate(-240.311 -262.837)" fill="#f4b1b0" fill-rule="evenodd"/>
        <path id="Path_13254" data-name="Path 13254" d="M335.2,393.53s.908,2.973,2.093,3.5a4.122,4.122,0,0,0,1.828.515v-2.131a6.779,6.779,0,0,1-3.922-1.886" transform="translate(-249.746 -292.894)" fill="#e78284" fill-rule="evenodd"/>
        <path id="Path_13259" data-name="Path 13259" d="M323.417,329.246c6.828,2.135,4.229,8.759.526,9.193-1.917.322-4.168-1.63-5.663-.211-.741.7-.613,2.586-.076,4.4.16.534.4.943-.4.447-.37-.228-.987-.557-1.226-.4a1.037,1.037,0,0,0-.022,1.528c.161.141.47.359.47.359a1.914,1.914,0,0,1-1.973-.5c-4.383-4.889-3.183-11.3,5.318-13.054,1.373-.283,1.517-2.239,3.049-1.76" transform="translate(-232.879 -244.993)" fill="#273373" fill-rule="evenodd"/>
      </g>
      <g id="phone-2" data-name="phone">
        <path id="Path_13260" data-name="Path 13260" d="M437.337,380.846c-.4,1.127-4.337,9.367-4.337,9.367l2.262,3.294.446.094,5.035-9.461-2.858-3.2Z" transform="translate(-322.611 -283.453)" fill="#2e2f80" fill-rule="evenodd"/>
        <path id="Path_13261" data-name="Path 13261" d="M439.759,387.008l-3.438,7.256,1.546,2.252,3.875-7.394Z" transform="translate(-325.085 -288.04)" fill="#fff" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="floating_elements" data-name="floating elements" transform="translate(-4.677)">
      <g id="calculator">
        <path id="Path_13276" data-name="Path 13276" d="M978.488,212.9a6.8,6.8,0,1,1-6.775,6.795,6.784,6.784,0,0,1,6.775-6.795" transform="translate(-723.984 -158.453)" fill="#5b285b" fill-rule="evenodd"/>
        <path id="Path_13278" data-name="Path 13278" d="M991.433,225.207h-5.04a.7.7,0,0,0-.687.684v5.926a.7.7,0,0,0,.687.684h5.04a.7.7,0,0,0,.687-.684v-5.926a.7.7,0,0,0-.687-.684m-3.894,6.2a.2.2,0,0,1-.183.182h-.549a.2.2,0,0,1-.183-.182v-.547a.2.2,0,0,1,.183-.182h.549a.2.2,0,0,1,.183.182Zm0-1.823a.2.2,0,0,1-.183.182h-.549a.2.2,0,0,1-.183-.182v-.547a.2.2,0,0,1,.183-.182h.549a.2.2,0,0,1,.183.182Zm1.833,1.823a.2.2,0,0,1-.183.182h-.55a.2.2,0,0,1-.183-.182v-.547a.2.2,0,0,1,.183-.182h.55a.2.2,0,0,1,.183.182Zm0-1.823a.2.2,0,0,1-.183.182h-.55a.2.2,0,0,1-.183-.182v-.547a.2.2,0,0,1,.183-.182h.55a.2.2,0,0,1,.183.182Zm1.833,1.823a.2.2,0,0,1-.183.182h-.55a.2.2,0,0,1-.183-.182v-2.37a.2.2,0,0,1,.183-.182h.55a.2.2,0,0,1,.183.182Zm0-3.647a.2.2,0,0,1-.183.182h-4.214a.2.2,0,0,1-.183-.182V226.3a.2.2,0,0,1,.183-.182h4.215a.2.2,0,0,1,.183.182Z" transform="translate(-734.41 -167.615)" fill="#fff"/>
      </g>
      <g id="playbutton">
        <path id="Path_13241" data-name="Path 13241" d="M803.1,159.837c20.854,0,20.96,28.339.542,28.339-8.524,0-15.433-6.344-15.433-14.169s6.368-14.169,14.891-14.169" transform="translate(-587.264 -118.962)" fill="#823d84" fill-rule="evenodd"/>
        <path id="Path_13242" data-name="Path 13242" d="M812.761,159.837c20.851,0,20.963,28.339.544,28.339-8.524,0-15.433-6.344-15.433-14.169s6.368-14.169,14.888-14.169" transform="translate(-594.463 -118.962)" fill="#5b285b" fill-rule="evenodd"/>
        <path id="Path_13243" data-name="Path 13243" d="M842.174,193.031l11.043,5.538-11.043,5.538Z" transform="translate(-627.47 -143.668)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="degree">
        <path id="Path_13264" data-name="Path 13264" d="M637.394,214.608h11.3a5.688,5.688,0,0,1,5.663,5.68v11.7a5.689,5.689,0,0,1-5.663,5.681h-11.3a5.689,5.689,0,0,1-5.663-5.681v-11.7a5.688,5.688,0,0,1,5.663-5.68" transform="translate(-470.677 -159.727)" fill="#ea96bc" fill-rule="evenodd"/>
        <path id="Path_13284" data-name="Path 13284" d="M652.1,245.331a1.653,1.653,0,0,1-.891-.24l-4.495-2.512v1.994c.04,0,.04.04.08.04l4.5,2.552a1.673,1.673,0,0,0,1.62,0l4.5-2.553a1.6,1.6,0,0,0,.81-1.4v-1.076L653,245.093a1.652,1.652,0,0,1-.891.239m7.816-6.659-7.006-3.987a1.744,1.744,0,0,0-1.62,0l-7.006,3.987a.779.779,0,0,0,0,1.356l7.413,4.187a.694.694,0,0,0,.405.12.792.792,0,0,0,.405-.12l7.409-4.187a.779.779,0,0,0,0-1.356" transform="translate(-479.739 -174.521)" fill="#fff"/>
      </g>
      <g id="search">
        <path id="Path_13263" data-name="Path 13263" d="M532.866,214.608h11.3a5.688,5.688,0,0,1,5.663,5.68v11.7a5.689,5.689,0,0,1-5.663,5.681h-11.3a5.689,5.689,0,0,1-5.663-5.681v-11.7a5.689,5.689,0,0,1,5.663-5.68" transform="translate(-392.798 -159.727)" fill="#ea96bc" fill-rule="evenodd"/>
        <path id="Path_13282" data-name="Path 13282" d="M556.256,236.029a5.2,5.2,0,0,0-7.141,0,4.723,4.723,0,0,0-.637,6.124l-.922.887-.151-.129a.2.2,0,0,0-.28,0l0,0-3.287,3.167a.39.39,0,0,0,0,.549l1.142,1.1a.427.427,0,0,0,.57,0l3.287-3.166a.183.183,0,0,0,0-.275l-.151-.146.888-.856a5.206,5.206,0,0,0,6.689-.388,4.741,4.741,0,0,0,.174-6.689q-.086-.091-.176-.177m-1.14,5.784a3.533,3.533,0,0,1-4.862,0,3.231,3.231,0,0,1-.127-4.557q.062-.066.127-.128a3.533,3.533,0,0,1,4.862,0,3.214,3.214,0,0,1,1.007,2.342,3.28,3.28,0,0,1-1.007,2.343" transform="translate(-405.104 -174.612)" fill="#fff"/>
      </g>
      <g id="question">
        <path id="Path_13262" data-name="Path 13262" d="M355.721,179.917h23.134c5.238,0,9.488,5.334,9.488,11.914v7.936c0,6.581-4.25,11.914-9.488,11.914H360.779l-5.057,5.844V211.68c-5.24,0-9.488-5.333-9.488-11.914v-7.936c0-6.58,4.248-11.914,9.488-11.914" transform="translate(-257.964 -133.907)" fill="#ef6aa0" fill-rule="evenodd"/>
        <path id="Path_13285" data-name="Path 13285" d="M408.15,203.118a5.663,5.663,0,0,1,.76-3.188,11.254,11.254,0,0,1,2.722-2.455,4.948,4.948,0,0,0,1.6-1.586,4.065,4.065,0,0,0,.585-2.181,2.944,2.944,0,0,0-.684-2.043,2.478,2.478,0,0,0-1.941-.762,2.428,2.428,0,0,0-1.7.626,2.482,2.482,0,0,0-.714,1.86h-5.005l-.031-.092a5.542,5.542,0,0,1,2.015-4.765,8.641,8.641,0,0,1,5.435-1.594,8.3,8.3,0,0,1,5.693,1.831,6.255,6.255,0,0,1,2.08,4.94,5.944,5.944,0,0,1-1.133,3.508,8.051,8.051,0,0,1-2.881,2.517,3.494,3.494,0,0,0-1.314,1.38,4.848,4.848,0,0,0-.342,2Zm5.13,6.344h-5.108v-4.118h5.108Z" transform="translate(-301.795 -136.123)" fill="#fff"/>
      </g>
      <g id="idea">
        <path id="Path_13277" data-name="Path 13277" d="M222.117,345.765a6.795,6.795,0,1,1-6.775,6.795,6.783,6.783,0,0,1,6.775-6.795" transform="translate(-160.443 -257.344)" fill="#5b285b" fill-rule="evenodd"/>
        <path id="Path_13279" data-name="Path 13279" d="M228.959,378.259a.114.114,0,0,0-.1-.073h-.774a1.442,1.442,0,0,0-1.422,1.437v.714a.105.105,0,0,0,.1.105h2.876a.114.114,0,0,0,.11-.117.115.115,0,0,0-.005-.031Zm3.713-.063H231.9a.126.126,0,0,0-.1.073l-.784,2.025a.105.105,0,0,0,.054.138.11.11,0,0,0,.051.009h2.876a.105.105,0,0,0,.1-.105v-.714a1.427,1.427,0,0,0-1.423-1.427" transform="translate(-168.874 -281.474)" fill="#fff"/>
        <path id="Path_13280" data-name="Path 13280" d="M228.712,355.995h-.429a.273.273,0,0,0,0,.546h.429a.273.273,0,0,0,0-.546m2.667-2.36a.271.271,0,0,0,.272-.269v-.423a.271.271,0,0,0-.268-.273h0a.262.262,0,0,0-.262.262s0,.007,0,.011v.43a.266.266,0,0,0,.261.262m-2,.451-.3-.3a.267.267,0,0,0-.376.377l.3.3a.268.268,0,0,0,.188.073.252.252,0,0,0,.188-.073.267.267,0,0,0,0-.376l0,0m2,5.172a.326.326,0,0,0-.324.325v1.5a.324.324,0,1,0,.648,0v-1.5a.327.327,0,0,0-.324-.325m2.688-5.476a.264.264,0,0,0-.375,0l0,0-.3.3a.266.266,0,0,0,0,.376l0,0a.278.278,0,0,0,.377,0l.3-.3a.267.267,0,0,0,0-.376h0m-2.919.346a2.126,2.126,0,0,0-1.747,1.742,2.016,2.016,0,0,0,.544,1.636,1.282,1.282,0,0,1,.356.892.3.3,0,0,0,.3.3h1.537a.3.3,0,0,0,.3-.3,1.257,1.257,0,0,1,.356-.881,1.96,1.96,0,0,0,.565-1.4,1.982,1.982,0,0,0-2.218-1.994m.5,2.78v.734a.272.272,0,1,1-.544,0v-.734a.7.7,0,0,1-.534-.65.272.272,0,1,1,.544,0c0,.073.115.156.261.156s.261-.084.261-.156a.272.272,0,1,1,.544,0,.7.7,0,0,1-.532.65Zm2.824-.912h-.43a.273.273,0,0,0,0,.546h.43a.271.271,0,0,0,.272-.269v0a.264.264,0,0,0-.255-.273h-.017" transform="translate(-169.882 -262.482)" fill="#fff"/>
      </g>
    </g>
    <g id="stars" transform="translate(-29.015 -148)">
      <g id="star" transform="translate(1)">
        <path id="Path_13326" data-name="Path 13326" d="M204.245,639.163a.348.348,0,0,0,.349-.348v-4.909a.349.349,0,0,0-.7,0v4.909a.348.348,0,0,0,.349.348" transform="translate(-138.915 -371.538)" fill="#ed5588"/>
        <path id="Path_13327" data-name="Path 13327" d="M214.678,664h2.374a.348.348,0,1,0,0-.7h-2.374a.348.348,0,1,0,0,.7" transform="translate(-146.688 -393.725)" fill="#ed5588"/>
        <path id="Path_13328" data-name="Path 13328" d="M204.245,679.344a.348.348,0,0,0,.349-.348v-4.909a.349.349,0,0,0-.7,0V679a.348.348,0,0,0,.349.348" transform="translate(-138.915 -401.507)" fill="#ed5588"/>
        <path id="Path_13329" data-name="Path 13329" d="M184.5,664h2.375a.348.348,0,1,0,0-.7H184.5a.348.348,0,1,0,0,.7" transform="translate(-124.201 -393.725)" fill="#ed5588"/>
      </g>
      <g id="star-2" data-name="star">
        <path id="Path_13338" data-name="Path 13338" d="M494.129,414.953a.233.233,0,0,0,.233-.232v-3.277a.233.233,0,0,0-.466,0v3.277a.233.233,0,0,0,.233.232" transform="translate(-386.982 -206.7)" fill="#ed5588"/>
        <path id="Path_13339" data-name="Path 13339" d="M501.1,431.536h1.585a.232.232,0,1,0,0-.464H501.1a.232.232,0,1,0,0,.464" transform="translate(-392.172 -221.514)" fill="#ed5588"/>
        <path id="Path_13340" data-name="Path 13340" d="M494.129,441.779a.233.233,0,0,0,.233-.232v-3.277a.233.233,0,0,0-.466,0v3.277a.233.233,0,0,0,.233.232" transform="translate(-386.982 -226.709)" fill="#ed5588"/>
        <path id="Path_13341" data-name="Path 13341" d="M480.944,431.536h1.585a.232.232,0,1,0,0-.464h-1.585a.232.232,0,1,0,0,.464" transform="translate(-377.158 -221.514)" fill="#ed5588"/>
      </g>
      <g id="star-3" data-name="star">
        <path id="Path_13508" data-name="Path 13508" d="M494.129,414.953a.233.233,0,0,0,.233-.232v-3.277a.233.233,0,0,0-.466,0v3.277a.233.233,0,0,0,.233.232" transform="translate(-292.982 -267.7)" fill="#ed5588"/>
        <path id="Path_13509" data-name="Path 13509" d="M501.1,431.536h1.585a.232.232,0,1,0,0-.464H501.1a.232.232,0,1,0,0,.464" transform="translate(-298.172 -282.514)" fill="#ed5588"/>
        <path id="Path_13510" data-name="Path 13510" d="M494.129,441.779a.233.233,0,0,0,.233-.232v-3.277a.233.233,0,0,0-.466,0v3.277a.233.233,0,0,0,.233.232" transform="translate(-292.982 -287.709)" fill="#ed5588"/>
        <path id="Path_13511" data-name="Path 13511" d="M480.944,431.536h1.585a.232.232,0,1,0,0-.464h-1.585a.232.232,0,1,0,0,.464" transform="translate(-283.158 -282.514)" fill="#ed5588"/>
      </g>
      <g id="star-4" data-name="star">
        <path id="Path_13512" data-name="Path 13512" d="M494.129,414.953a.233.233,0,0,0,.233-.232v-3.277a.233.233,0,0,0-.466,0v3.277a.233.233,0,0,0,.233.232" transform="translate(-189.982 -170.7)" fill="#ed5588"/>
        <path id="Path_13513" data-name="Path 13513" d="M501.1,431.536h1.585a.232.232,0,1,0,0-.464H501.1a.232.232,0,1,0,0,.464" transform="translate(-195.172 -185.514)" fill="#ed5588"/>
        <path id="Path_13514" data-name="Path 13514" d="M494.129,441.779a.233.233,0,0,0,.233-.232v-3.277a.233.233,0,0,0-.466,0v3.277a.233.233,0,0,0,.233.232" transform="translate(-189.982 -190.709)" fill="#ed5588"/>
        <path id="Path_13515" data-name="Path 13515" d="M480.944,431.536h1.585a.232.232,0,1,0,0-.464h-1.585a.232.232,0,1,0,0,.464" transform="translate(-180.158 -185.514)" fill="#ed5588"/>
      </g>
    </g>
    <g id="peragraph" transform="translate(-29.015 -148)">
      <path id="Path_13316" data-name="Path 13316" d="M521.444,98.691h-3.09a.37.37,0,0,1-.371-.37v-3.08a.37.37,0,0,1,.371-.37h3.09a.37.37,0,0,1,.371.37v3.08a.37.37,0,0,1-.371.37m-2.719-.739h2.348V95.611h-2.348Z" transform="translate(-261.929 73.24)" fill="#5b285b"/>
      <path id="Path_13317" data-name="Path 13317" d="M521.444,123.23h-3.09a.37.37,0,0,1-.371-.37v-3.08a.37.37,0,0,1,.371-.37h3.09a.37.37,0,0,1,.371.37v3.08a.37.37,0,0,1-.371.37m-2.719-.739h2.348V120.15h-2.348Z" transform="translate(-261.929 54.938)" fill="#5b285b"/>
      <path id="Path_13318" data-name="Path 13318" d="M555.363,95.611h-7.276a.37.37,0,1,1,0-.739h7.276a.37.37,0,1,1,0,.739" transform="translate(-284.081 73.24)" fill="#5b285b"/>
      <path id="Path_13319" data-name="Path 13319" d="M562.41,105.791H548.087a.37.37,0,1,1,0-.739H562.41a.37.37,0,1,1,0,.739" transform="translate(-284.081 65.647)" fill="#5b285b"/>
      <path id="Path_13320" data-name="Path 13320" d="M606.565,95.611h-17.98a.37.37,0,1,1,0-.739h17.98a.37.37,0,1,1,0,.739" transform="translate(-314.255 73.24)" fill="#5b285b"/>
      <path id="Path_13321" data-name="Path 13321" d="M559.958,120.15H548.087a.37.37,0,1,1,0-.739h11.871a.37.37,0,1,1,0,.739" transform="translate(-284.081 54.938)" fill="#5b285b"/>
      <path id="Path_13322" data-name="Path 13322" d="M554.533,130.33h-6.446a.37.37,0,1,1,0-.739h6.446a.37.37,0,1,1,0,.739" transform="translate(-284.081 47.345)" fill="#5b285b"/>
      <path id="Path_13323" data-name="Path 13323" d="M613.858,120.15h-7.25a.37.37,0,1,1,0-.739h7.25a.37.37,0,1,1,0,.739" transform="translate(-327.683 54.938)" fill="#5b285b"/>
    </g>
  </g>
</svg> -->
