import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/settings/settingsLocalDataSource.dart';
import 'package:flutterquiz/ui/styles/theme/appTheme.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';

/// 🎨 حالات الثيم المتاحة
enum ThemeMode {
  light,
  dark,
  system, // ثيم تلقائي حسب النظام
}

/// 🎨 حالة الثيم الحالية
class ThemeState {
  const ThemeState({
    required this.appTheme,
    required this.themeMode,
    required this.isSystemDark,
  });

  final AppTheme appTheme;
  final ThemeMode themeMode;
  final bool isSystemDark;

  /// الثيم الفعلي المستخدم
  AppTheme get effectiveTheme {
    switch (themeMode) {
      case ThemeMode.light:
        return AppTheme.light;
      case ThemeMode.dark:
        return AppTheme.dark;
      case ThemeMode.system:
        return isSystemDark ? AppTheme.dark : AppTheme.light;
    }
  }

  ThemeState copyWith({
    AppTheme? appTheme,
    ThemeMode? themeMode,
    bool? isSystemDark,
  }) {
    return ThemeState(
      appTheme: appTheme ?? this.appTheme,
      themeMode: themeMode ?? this.themeMode,
      isSystemDark: isSystemDark ?? this.isSystemDark,
    );
  }
}

/// 🎨 مدير الثيمات المحسن
class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit(this.settingsLocalDataSource)
      : super(
          ThemeState(
            appTheme: _getInitialTheme(settingsLocalDataSource),
            themeMode: _getInitialThemeMode(settingsLocalDataSource),
            isSystemDark: _getSystemBrightness() == Brightness.dark,
          ),
        ) {
    _listenToSystemChanges();
  }

  final SettingsLocalDataSource settingsLocalDataSource;

  /// الحصول على الثيم الأولي
  static AppTheme _getInitialTheme(SettingsLocalDataSource dataSource) {
    final savedTheme = dataSource.theme;
    if (savedTheme == systemThemeKey) {
      return _getSystemBrightness() == Brightness.dark 
          ? AppTheme.dark 
          : AppTheme.light;
    }
    return savedTheme == darkThemeKey ? AppTheme.dark : AppTheme.light;
  }

  /// الحصول على وضع الثيم الأولي
  static ThemeMode _getInitialThemeMode(SettingsLocalDataSource dataSource) {
    final savedTheme = dataSource.theme;
    switch (savedTheme) {
      case darkThemeKey:
        return ThemeMode.dark;
      case systemThemeKey:
        return ThemeMode.system;
      default:
        return ThemeMode.light;
    }
  }

  /// الحصول على سطوع النظام
  static Brightness _getSystemBrightness() {
    return WidgetsBinding.instance.platformDispatcher.platformBrightness;
  }

  /// الاستماع لتغييرات النظام
  void _listenToSystemChanges() {
    WidgetsBinding.instance.platformDispatcher.onPlatformBrightnessChanged = () {
      if (state.themeMode == ThemeMode.system) {
        final isSystemDark = _getSystemBrightness() == Brightness.dark;
        emit(state.copyWith(
          isSystemDark: isSystemDark,
          appTheme: isSystemDark ? AppTheme.dark : AppTheme.light,
        ));
      }
    };
  }

  /// تغيير الثيم
  void changeTheme(ThemeMode themeMode) {
    final isSystemDark = _getSystemBrightness() == Brightness.dark;
    
    AppTheme newTheme;
    String themeKey;
    
    switch (themeMode) {
      case ThemeMode.light:
        newTheme = AppTheme.light;
        themeKey = lightThemeKey;
        break;
      case ThemeMode.dark:
        newTheme = AppTheme.dark;
        themeKey = darkThemeKey;
        break;
      case ThemeMode.system:
        newTheme = isSystemDark ? AppTheme.dark : AppTheme.light;
        themeKey = systemThemeKey;
        break;
    }
    
    // حفظ الإعداد
    settingsLocalDataSource.theme = themeKey;
    
    // تحديث شريط الحالة
    _updateSystemUI(newTheme);
    
    // إرسال الحالة الجديدة
    emit(ThemeState(
      appTheme: newTheme,
      themeMode: themeMode,
      isSystemDark: isSystemDark,
    ));
  }

  /// تحديث واجهة النظام
  void _updateSystemUI(AppTheme theme) {
    final isLight = theme == AppTheme.light;
    
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isLight ? Brightness.dark : Brightness.light,
        statusBarBrightness: isLight ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: isLight 
            ? const Color(0xFFF8F9FA) 
            : const Color(0xFF1A1A1A),
        systemNavigationBarIconBrightness: isLight 
            ? Brightness.dark 
            : Brightness.light,
      ),
    );
  }

  /// التبديل السريع بين الفاتح والداكن
  void toggleTheme() {
    final currentTheme = state.effectiveTheme;
    changeTheme(currentTheme == AppTheme.light ? ThemeMode.dark : ThemeMode.light);
  }

  /// التحقق من الثيم الحالي
  bool get isLightTheme => state.effectiveTheme == AppTheme.light;
  bool get isDarkTheme => state.effectiveTheme == AppTheme.dark;
  bool get isSystemTheme => state.themeMode == ThemeMode.system;
}
