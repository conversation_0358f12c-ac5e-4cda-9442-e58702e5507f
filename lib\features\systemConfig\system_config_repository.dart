import 'dart:convert';
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:flutterquiz/features/systemConfig/model/answer_mode.dart';
import 'package:flutterquiz/features/systemConfig/model/supportedQuestionLanguage.dart';
import 'package:flutterquiz/features/systemConfig/model/systemConfigModel.dart';
import 'package:flutterquiz/features/systemConfig/system_config_exception.dart';
import 'package:flutterquiz/features/systemConfig/system_config_remote_data_source.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SystemConfigRepository {
  factory SystemConfigRepository() {
    _systemConfigRepository._systemConfigRemoteDataSource =
        SystemConfigRemoteDataSource();
    return _systemConfigRepository;
  }

  SystemConfigRepository._internal();

  static final SystemConfigRepository _systemConfigRepository =
      SystemConfigRepository._internal();
  late SystemConfigRemoteDataSource _systemConfigRemoteDataSource;

  Future<SystemConfigModel> getSystemConfig() async {
    try {
      final result = await _systemConfigRemoteDataSource.getSystemConfig();
      log(name: 'System Config', result.toString());
      final config = SystemConfigModel.fromJson(result);
      final prefs = await SharedPreferences.getInstance();
      final mode = prefs.getString('answerMode');

      if (mode != null) {
        if (mode == 'noAnswerCorrectness') {
          config.answerMode = AnswerMode.noAnswerCorrectness;
        } else if (mode == 'showAnswerCorrectness') {
          config.answerMode = AnswerMode.showAnswerCorrectness;
        } else {
          config.answerMode = AnswerMode.showAnswerCorrectnessAndCorrectAnswer;
        }
      } else {
        // تعيين قيمة افتراضية وحفظها
        config.answerMode = AnswerMode.showAnswerCorrectness;
        await prefs.setString('answerMode', 'showAnswerCorrectness');
        log(
            name: 'System Config',
            'Answer mode set to default: showAnswerCorrectness');
      }

      //  config.answerMode = AnswerMode.noAnswerCorrectness;
      return config;
    } catch (e) {
      log(name: 'System Config Exception', e.toString());
      throw SystemConfigException(errorMessageCode: e.toString());
    }
  }

  Future<List<SupportedLanguage>> getSupportedQuestionLanguages() async {
    try {
      final result =
          await _systemConfigRemoteDataSource.getSupportedQuestionLanguages();
      return result
          .map((e) => SupportedLanguage.fromJson(Map.from(e)))
          .toList();
    } catch (e) {
      throw SystemConfigException(errorMessageCode: e.toString());
    }
  }

  Future<List<SupportedAppLanguage>> getSupportedLanguageList() async {
    try {
      final result =
          await _systemConfigRemoteDataSource.getSupportedLanguageList();

      return result.map(SupportedAppLanguage.fromJson).toList();
    } catch (e) {
      throw SystemConfigException(errorMessageCode: e.toString());
    }
  }

  Future<String> getAppSettings(String type) async {
    try {
      final result = await _systemConfigRemoteDataSource.getAppSettings(type);
      return result;
    } catch (e) {
      throw SystemConfigException(errorMessageCode: e.toString());
    }
  }

  Future<List<String>> getImagesFromFile(String fileName) async {
    try {
      final result = await rootBundle.loadString(fileName);
      final images = (jsonDecode(result) as Map)['images'] as List;
      return images.map((e) => e.toString()).toList();
    } catch (e) {
      throw SystemConfigException(errorMessageCode: errorCodeDefaultMessage);
    }
  }
}
