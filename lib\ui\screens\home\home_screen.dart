import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/ads/interstitial_ad_cubit.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/features/auth/cubits/referAndEarnCubit.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/battleRoomCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/multiUserBattleRoomCubit.dart';
import 'package:flutterquiz/features/exam/cubits/examCubit.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardAllTimeCubit.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardDailyCubit.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardMonthlyCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/deleteAccountCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateUserDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/uploadProfileCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementLocalDataSource.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/cubits/contestCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizzone_category_cubit.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/models/contest.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/battle/create_or_join_screen.dart';
import 'package:flutterquiz/ui/screens/home/<USER>';
import 'package:flutterquiz/ui/screens/home/<USER>';
import 'package:flutterquiz/ui/screens/home/<USER>/app_under_maintenance_dialog.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/quiz_zone_categories.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/update_app_container.dart';
import 'package:flutterquiz/utils/constants/app_constants.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/user_achievements.dart';
import 'package:flutterquiz/ui/screens/menu/menu_screen.dart';
import 'package:flutterquiz/ui/screens/quiz/categoryScreen.dart';
import 'package:flutterquiz/ui/screens/subscription/SubscriptionService.dart';
import 'package:flutterquiz/ui/screens/subscription/paywall.dart';
import 'package:flutterquiz/ui/widgets/all.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/reminder_manager.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:iconsax/iconsax.dart';
import 'package:iconsax_plus/iconsax_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:salomon_bottom_bar/salomon_bottom_bar.dart';
import 'package:url_launcher/url_launcher_string.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({required this.isGuest, super.key});

  final bool isGuest;

  @override
  State<HomeScreen> createState() => _HomeScreenState();

  static Route<HomeScreen> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<ReferAndEarnCubit>(
            create: (_) => ReferAndEarnCubit(AuthRepository()),
          ),
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
          BlocProvider<UpdateUserDetailCubit>(
            create: (_) => UpdateUserDetailCubit(ProfileManagementRepository()),
          ),
        ],
        child: HomeScreen(isGuest: routeSettings.arguments! as bool),
      ),
    );
  }
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  /// Quiz Zone globals
  int oldCategoriesToShowCount = 0;
  bool isCateListExpanded = false;
  bool canExpandCategoryList = false;

  final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  ReminderManager reminderManager = ReminderManager();

  void checkReminder() async {
    //   final pres = await SharedPreferences.getInstance();
    // await pres.clear();
    await reminderManager.handleReminderSetup(context);
  }

  Future<void> _checkStoreKitConfig() async {
    try {
      final offerings = await Purchases.getOfferings();
      print('''
🔍 فحص StoreKit:
- هل توجد عروض: ${offerings.all.isNotEmpty}
- عدد العروض: ${offerings.all.length}
- العرض الحالي: ${offerings.current?.identifier}
- المنتجات المتاحة: ${offerings.current?.availablePackages.map((p) => p.storeProduct.identifier).join(', ')}
''');
    } catch (e) {
      print('❌ خطأ في فحص StoreKit: $e');
    }
  }

  List<String> battleName = ['groupPlay', 'battleQuiz'];

  List<String> battleImg = [Assets.groupBattleIcon, Assets.oneVsOneIcon];

  List<String> examSelf = ['exam', 'selfChallenge'];

  List<String> examSelfDesc = ['desExam', 'challengeYourselfLbl'];

  List<String> examSelfImg = [Assets.examQuizIcon, Assets.selfChallengeIcon];

  List<String> battleDesc = ['desGroupPlay', 'desBattleQuiz'];

  List<String> playDifferentZone = [
    //'dailyQuiz',
    'funAndLearn',
    // 'guessTheWord',
    'audioQuestions',
    // 'mathMania',
    // 'truefalse',
  ];

  List<String> playDifferentImg = [
    // Assets.dailyQuizIcon,
    // Assets.funNLearnIcon,
    // // Assets.guessTheWordIcon,
    // Assets.audioQuizIcon,
    // Assets.mathsQuizIcon,
    // Assets.trueFalseQuizIcon,
  ];

  List<String> playDifferentZoneDesc = [
    // 'desDailyQuiz',
    // 'desFunAndLearn',
    // 'desGuessTheWord',
    // 'desAudioQuestions',
    // 'desMathMania',
    // 'desTrueFalse',
  ];

  // Screen dimensions
  double get scrWidth => MediaQuery.of(context).size.width;

  double get scrHeight => MediaQuery.of(context).size.height;

  // HomeScreen horizontal margin, change from here
  double get hzMargin => scrWidth * UiUtils.hzMarginPct;

  double get _statusBarPadding => MediaQuery.of(context).padding.top;

  // TextStyles
  // check build() method
  late var _boldTextStyle = TextStyle(
    fontWeight: FontWeights.bold,
    fontSize: 18,
    color: Theme.of(context).colorScheme.onSurface,
  );

  ///
  late String _currLangId;
  late final SystemConfigCubit _sysConfigCubit;
  final _quizZoneId =
      UiUtils.getCategoryTypeNumberFromQuizType(QuizTypes.quizZone);

  @override
  void didChangeDependencies() {
    if (_navBodies.isEmpty) {
      _navBodies
        ..add(
          buildHomeBody(),
        )
        // ..add(
        //    const PdfsScreen(
        //      fromNav: true,
        //    ),
        // )
        ..add(
          MultiBlocProvider(
            providers: [
              BlocProvider<LeaderBoardMonthlyCubit>(
                create: (_) => LeaderBoardMonthlyCubit(),
              ),
              BlocProvider<LeaderBoardDailyCubit>(
                create: (_) => LeaderBoardDailyCubit(),
              ),
              BlocProvider<LeaderBoardAllTimeCubit>(
                create: (_) => LeaderBoardAllTimeCubit(),
              ),
            ],
            child: const LeaderBoardScreen(
              fromNav: true,
            ),
          ),
        )
        ..add(
          const SettingScreen(),
        );

      WidgetsBinding.instance.addPostFrameCallback(
        (_) {
          if (context.read<SystemConfigCubit>().isQuizZoneEnabled) {
            _navBodies.insert(
              1,
              const CategoryScreen(
                quizType: QuizTypes.quizZone,
                fromNav: true,
              ),
            );
          }
        },
      );
    }
    super.didChangeDependencies();
  }

  // bool _isPremium = false;
  @override
  void initState() {
    super.initState();
    checkReminder();
    _checkStoreKitConfig();
    Future.delayed(
        const Duration(milliseconds: 500), _requestTrackingPermission);

    setupInteractedMessage();
    _initLocalNotification();
    setQuizMenu();
    checkForUpdates();
    showAppUnderMaintenanceDialog();
    Future.delayed(Duration.zero, () async {
      // ignore: use_build_context_synchronously
      context.read<InterstitialAdCubit>().createInterstitialAd(context);
      // تحديث حالة الاشتراك
      // ignore: use_build_context_synchronously
      context.read<SubscriptionCubit>().checkSubscription();
    });

    WidgetsBinding.instance.addObserver(this);

    ///
    _currLangId = UiUtils.getCurrentQuizLanguageId(context);
    _sysConfigCubit = context.read<SystemConfigCubit>();
    final quizCubit = context.read<QuizCategoryCubit>();
    final quizZoneCubit = context.read<QuizoneCategoryCubit>();

    if (widget.isGuest) {
      quizCubit.getQuizCategory(languageId: _currLangId, type: _quizZoneId);
      quizZoneCubit.getQuizCategory(languageId: _currLangId);
    } else {
      fetchUserDetails();

      quizCubit.getQuizCategoryWithUserId(
        languageId: _currLangId,
        type: _quizZoneId,
      );
      quizZoneCubit.getQuizCategoryWithUserId(languageId: _currLangId);
      context.read<ContestCubit>().getContest(languageId: _currLangId);
    }

    // أضف هذا الكود في بداية initState
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _checkFirstTimeAndShowInterests();
    // });
  }

  Future<void> _requestTrackingPermission() async {
    // التأكد من أن الإذن يُطلب قبل أي عملية جمع بيانات
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      if (status == TrackingStatus.notDetermined) {
        // تأخير بسيط للتأكد من ظهور الطلب بعد تحميل التطبيق
        await Future.delayed(const Duration(seconds: 1));
        await AppTrackingTransparency.requestTrackingAuthorization();
      }
      final uuid = await AppTrackingTransparency.getAdvertisingIdentifier();
      debugPrint('IDFA: $uuid');
    });
  }

  void showAppUnderMaintenanceDialog() {
    Future.delayed(Duration.zero, () {
      if (_sysConfigCubit.isAppUnderMaintenance) {
        showDialog<void>(
          // ignore: use_build_context_synchronously
          context: context,
          builder: (_) => const AppUnderMaintenanceDialog(),
        );
      }
    });
  }

  Future<void> _initLocalNotification() async {
    const initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    final initializationSettingsIOS = const DarwinInitializationSettings();

    final initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onTapLocalNotification,
    );

    /// Request Permissions for IOS
    if (Platform.isIOS) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions();
    }
  }

  void setQuizMenu() {
    Future.delayed(Duration.zero, () {
      if (!_sysConfigCubit.isDailyQuizEnabled) {
        playDifferentZone.removeWhere((e) => e == 'dailyQuiz');
        playDifferentImg.removeWhere((e) => e == Assets.dailyQuizIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desDailyQuiz');
      }

      if (!_sysConfigCubit.isTrueFalseQuizEnabled) {
        playDifferentZone.removeWhere((e) => e == 'truefalse');
        playDifferentImg.removeWhere((e) => e == Assets.trueFalseQuizIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desTrueFalse');
      }

      if (!_sysConfigCubit.isFunNLearnEnabled) {
        playDifferentZone.removeWhere((e) => e == 'funAndLearn');
        playDifferentImg.removeWhere((e) => e == Assets.funNLearnIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desFunAndLearn');
      }

      if (!_sysConfigCubit.isAudioQuizEnabled) {
        playDifferentZone.removeWhere((e) => e == 'audioQuestions');
        playDifferentImg.removeWhere((e) => e == Assets.guessTheWordIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desAudioQuestions');
      }

      if (!_sysConfigCubit.isMathQuizEnabled) {
        playDifferentZone.removeWhere((e) => e == 'mathMania');
        playDifferentImg.removeWhere((e) => e == Assets.mathsQuizIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desMathMania');
      }

      if (!_sysConfigCubit.isExamQuizEnabled) {
        examSelf.removeWhere((e) => e == 'exam');
        examSelfDesc.removeWhere((e) => e == 'desExam');
        examSelfImg.removeWhere((e) => e == Assets.examQuizIcon);
      }

      if (!_sysConfigCubit.isSelfChallengeQuizEnabled) {
        examSelf.removeWhere((e) => e == 'selfChallenge');
        examSelfDesc.removeWhere((e) => e == 'challengeYourselfLbl');
        examSelfImg.removeWhere((e) => e == Assets.selfChallengeIcon);
      }

      if (!_sysConfigCubit.isGroupBattleEnabled) {
        battleName.removeWhere((e) => e == 'groupPlay');
        battleImg.removeWhere((e) => e == Assets.groupBattleIcon);
        battleDesc.removeWhere((e) => e == 'desGroupPlay');
      }

      if (!_sysConfigCubit.isOneVsOneBattleEnabled &&
          !_sysConfigCubit.isRandomBattleEnabled) {
        battleName.removeWhere((e) => e == 'battleQuiz');
        battleImg.removeWhere((e) => e == Assets.groupBattleIcon);
        battleDesc.removeWhere((e) => e == 'desBattleQuiz');
      }
      setState(() {});
    });
  }

  late bool showUpdateContainer = false;

  Future<void> checkForUpdates() async {
    await Future<void>.delayed(Duration.zero);
    if (_sysConfigCubit.isForceUpdateEnable) {
      try {
        final forceUpdate =
            await UiUtils.forceUpdate(_sysConfigCubit.appVersion);

        if (forceUpdate) {
          setState(() => showUpdateContainer = true);
        }
      } catch (e) {
        log('Force Update Error', error: e);
      }
    }
  }

  Future<void> setupInteractedMessage() async {
    if (Platform.isIOS) {
      await FirebaseMessaging.instance.requestPermission(
        announcement: true,
        provisional: true,
      );
    } else {
      final isGranted = (await Permission.notification.status).isGranted;
      if (!isGranted) await Permission.notification.request();
    }
    await FirebaseMessaging.instance.getInitialMessage();
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
    // handle background notification
    FirebaseMessaging.onBackgroundMessage(UiUtils.onBackgroundMessage);
    //handle foreground notification
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      log('Notification arrives : ${message.toMap()}');
      final data = message.data;
      log(data.toString(), name: 'notification data msg');
      final title = data['title'].toString();
      final body = data['body'].toString();
      final type = data['type'].toString();
      final image = data['image'].toString();

      //if notification type is badges then update badges in cubit list
      if (type == 'badges') {
        Future.delayed(Duration.zero, () {
          context.read<BadgesCubit>().unlockBadge(data['badge_type'] as String);
        });
      }

      if (type == 'payment_request') {
        Future.delayed(Duration.zero, () {
          context.read<UserDetailsCubit>().updateCoins(
                addCoin: true,
                coins: int.parse(data['coins'] as String),
              );
        });
      }
      log(image, name: 'notification image data');
      //payload is some data you want to pass in local notification
      if (image != 'null' && image.isNotEmpty) {
        log('image ${image.runtimeType}');
        generateImageNotification(title, body, image, type, type);
      } else {
        generateSimpleNotification(title, body, type);
      }
    });
  }

  //quiz_type according to the notification category
  QuizTypes _getQuizTypeFromCategory(String category) {
    return switch (category) {
      // تم إزالة 'audio-question-category' لأن الأسئلة الصوتية لم تعد مدعومة
      // تم إزالة 'guess-the-word-category' لأن اختبارات تخمين الكلمة لم تعد مدعومة
      'fun-n-learn-category' => QuizTypes.funAndLearn,
      _ => QuizTypes.quizZone,
    };
  }

  // notification type is category then move to category screen
  Future<void> _handleMessage(RemoteMessage message) async {
    try {
      if (message.data['type'].toString().contains('category')) {
        await Navigator.of(context).pushNamed(
          Routes.category,
          arguments: {
            'quizType':
                _getQuizTypeFromCategory(message.data['type'] as String),
          },
        );
      } else if (message.data['type'] == 'badges') {
        //if user open app by tapping
        UiUtils.updateBadgesLocally(context);
        await Navigator.of(context).pushNamed(Routes.badges);
      } else if (message.data['type'] == 'payment_request') {
        await Navigator.of(context).pushNamed(Routes.wallet);
      }
    } catch (e) {
      log(e.toString(), error: e);
    }
  }

  Future<void> _onTapLocalNotification(NotificationResponse? payload) async {
    final type = payload!.payload ?? '';
    if (type == 'badges') {
      await Navigator.of(context).pushNamed(Routes.badges);
    } else if (type.contains('category')) {
      await Navigator.of(context).pushNamed(
        Routes.category,
        arguments: {
          'quizType': _getQuizTypeFromCategory(type),
        },
      );
    } else if (type == 'payment_request') {
      await Navigator.of(context).pushNamed(Routes.wallet);
    }
  }

  Future<void> generateImageNotification(
    String title,
    String msg,
    String image,
    String payloads,
    String type,
  ) async {
    final largeIconPath = await _downloadAndSaveFile(image, 'largeIcon');
    final bigPicturePath = await _downloadAndSaveFile(image, 'bigPicture');
    final bigPictureStyleInformation = BigPictureStyleInformation(
      FilePathAndroidBitmap(bigPicturePath),
      hideExpandedLargeIcon: true,
      contentTitle: title,
      htmlFormatContentTitle: true,
      summaryText: msg,
      htmlFormatSummaryText: true,
    );
    final androidPlatformChannelSpecifics = AndroidNotificationDetails(
      packageName,
      appName,
      icon: '@drawable/ic_notification',
      channelDescription: appName,
      largeIcon: FilePathAndroidBitmap(largeIconPath),
      styleInformation: bigPictureStyleInformation,
    );
    final platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
    );
    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      msg,
      platformChannelSpecifics,
      payload: payloads,
    );
  }

  Future<String> _downloadAndSaveFile(String url, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/$fileName';
    final response = await http.get(Uri.parse(url));
    final file = File(filePath);
    await file.writeAsBytes(response.bodyBytes);

    return filePath;
  }

  // notification on foreground
  Future<void> generateSimpleNotification(
    String title,
    String body,
    String payloads,
  ) async {
    const androidPlatformChannelSpecifics = AndroidNotificationDetails(
      packageName, //channel id
      appName, //channel name
      channelDescription: appName,
      importance: Importance.max,
      priority: Priority.high,
      ticker: 'ticker',
      icon: '@drawable/ic_notification',
    );

    const platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: DarwinNotificationDetails(),
    );

    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      platformChannelSpecifics,
      payload: payloads,
    );
  }

  @override
  void dispose() {
    ProfileManagementLocalDataSource.updateReversedCoins(0);
    WidgetsBinding.instance.removeObserver(this);
    // إزالة المستمع عند إغلاق الشاشة
    Purchases.removeCustomerInfoUpdateListener((_) {});
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    //show you left the game
    if (state == AppLifecycleState.resumed) {
      UiUtils.needToUpdateCoinsLocally(context);
    } else {
      ProfileManagementLocalDataSource.updateReversedCoins(0);
    }
  }

  // void _onTapProfile() => Navigator.of(context).pushNamed(
  //       Routes.menuScreen,
  //       arguments: widget.isGuest,
  //     );

  // void _onTapLeaderboard() => Navigator.of(context).pushNamed(
  //       widget.isGuest ? Routes.login : Routes.leaderBoard,
  //     );

  // void _onPressedZone(String index) {
  //   if (widget.isGuest) {
  //     _showLoginDialog();
  //     return;
  //   }

  //   switch (index) {
  //     case 'dailyQuiz':
  //       Navigator.of(context).pushNamed(
  //         Routes.quiz,
  //         arguments: {
  //           'quizType': QuizTypes.dailyQuiz,
  //           'numberOfPlayer': 1,
  //           'quizName': 'Daily Quiz',
  //         },
  //       );
  //       return;
  //     case 'funAndLearn':
  //       Navigator.of(context).pushNamed(
  //         Routes.category,
  //         arguments: {
  //           'quizType': QuizTypes.funAndLearn,
  //         },
  //       );
  //       return;
  //     case 'guessTheWord':
  //       Navigator.of(context).pushNamed(
  //         Routes.category,
  //         arguments: {
  //           'quizType': QuizTypes.guessTheWord,
  //         },
  //       );
  //       return;
  //     case 'audioQuestions':
  //       Navigator.of(context).pushNamed(
  //         Routes.category,
  //         arguments: {
  //           'quizType': QuizTypes.audioQuestions,
  //         },
  //       );
  //       return;
  //     case 'mathMania':
  //       Navigator.of(context).pushNamed(
  //         Routes.category,
  //         arguments: {
  //           'quizType': QuizTypes.mathMania,
  //         },
  //       );
  //       return;
  //     case 'truefalse':
  //       Navigator.of(context).pushNamed(
  //         Routes.quiz,
  //         arguments: {
  //           'quizType': QuizTypes.trueAndFalse,
  //           'numberOfPlayer': 1,
  //           'quizName': 'True/False Quiz',
  //         },
  //       );
  //       return;
  //   }
  // }

  void _onPressedSelfExam(String index) {
    if (widget.isGuest) {
      _showLoginDialog();
      return;
    }

    if (index == 'exam') {
      context.read<ExamCubit>().updateState(ExamInitial());
      Navigator.of(context).pushNamed(Routes.exams);
    } else if (index == 'selfChallenge') {
      context.read<QuizCategoryCubit>().updateState(QuizCategoryInitial());
      context.read<SubCategoryCubit>().updateState(SubCategoryInitial());
      Navigator.of(context).pushNamed(Routes.selfChallenge);
    }
  }

  void _onPressedBattle(String index) {
    if (widget.isGuest) {
      _showLoginDialog();
      return;
    }

    context.read<QuizCategoryCubit>().updateState(QuizCategoryInitial());
    if (index == 'groupPlay') {
      context
          .read<MultiUserBattleRoomCubit>()
          .updateState(MultiUserBattleRoomInitial());

      Navigator.of(context).push(
        CupertinoPageRoute<void>(
          builder: (_) => BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (context) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
            child: CreateOrJoinRoomScreen(
              quizType: QuizTypes.groupPlay,
              title: context.tr('groupPlay')!,
            ),
          ),
        ),
      );
    } else if (index == 'battleQuiz') {
      context.read<BattleRoomCubit>().updateState(
            BattleRoomInitial(),
            cancelSubscription: true,
          );

      if (_sysConfigCubit.isRandomBattleEnabled) {
        // TODO(J): if only random battle is enabled then in home screen also it should show random battle ? maybe
        Navigator.of(context).pushNamed(Routes.randomBattle);
      } else {
        Navigator.of(context).push(
          CupertinoPageRoute<CreateOrJoinRoomScreen>(
            builder: (_) => BlocProvider<UpdateScoreAndCoinsCubit>(
              create: (_) =>
                  UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
              child: CreateOrJoinRoomScreen(
                quizType: QuizTypes.oneVsOneBattle,
                title: context.tr('playWithFrdLbl')!,
              ),
            ),
          ),
        );
      }
    }
  }

  Future<void> _showLoginDialog() {
    return showLoginDialog(
      context,
      onTapYes: () {
        Navigator.of(context).pop();
        Navigator.of(context).pushNamed(Routes.login);
      },
    );
  }

  late String _userName = context.tr('guest')!;
  late String _userProfileImg = Assets.profile('3.png');

  Widget _buildProfileContainer() {
    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      builder: (context, state) {
        final size = MediaQuery.of(context).size;
        return Container(
          key: ValueKey(Theme.of(context)
              .brightness), // إضافة key لإجبار إعادة البناء عند تغيير الثيم
          margin: EdgeInsets.only(
            top: _statusBarPadding * .2,
            left: hzMargin,
            right: hzMargin,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor.withOpacity(0.1),
                  Theme.of(context).primaryColor.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Profile Image
                Container(
                  padding: const EdgeInsets.all(3),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor,
                        Theme.of(context).primaryColor.withOpacity(0.7),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).primaryColor.withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Theme.of(context).scaffoldBackgroundColor,
                    ),
                    child: ClipOval(
                      child: SizedBox(
                        width: size.width * 0.13,
                        height: size.width * 0.13,
                        child: QImage.circular(imageUrl: _userProfileImg),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: size.width * .03),

                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${context.tr(helloKey)!} ${widget.isGuest ? context.tr('guest')! : _userName}',
                        maxLines: 1,
                        style: _boldTextStyle.copyWith(
                          fontSize: 18,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      // const SizedBox(height: 4),
                      // Text(
                      //   context.tr(letsPlay)!,
                      //   style: TextStyle(
                      //     fontSize: 14,
                      //     color: Theme.of(context)
                      //         .colorScheme
                      //         .onTertiary
                      //         .withOpacity(0.7),
                      //   ),
                      //   maxLines: 1,
                      //   overflow: TextOverflow.ellipsis,
                      // ),
                    ],
                  ),
                ),

                // Premium Button
                // Premium Button
                BlocBuilder<SubscriptionCubit, SubscriptionState>(
                  builder: (context, subscriptionState) {
                    return Container(
                      width: size.width * 0.11,
                      height: size.width * 0.11,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        gradient: LinearGradient(
                          colors: subscriptionState.isSubscribed
                              ? [
                                  Theme.of(context).colorScheme.secondary,
                                  Theme.of(context)
                                      .colorScheme
                                      .secondary
                                      .withOpacity(0.8)
                                ]
                              : [
                                  Theme.of(context).colorScheme.tertiary,
                                  Theme.of(context)
                                      .colorScheme
                                      .tertiary
                                      .withOpacity(0.8)
                                ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: (subscriptionState.isSubscribed
                                    ? Theme.of(context).colorScheme.secondary
                                    : Theme.of(context).colorScheme.tertiary)
                                .withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(15),
                          onTap: subscriptionState.isSubscribed
                              ? null
                              : () async {
                                  try {
                                    final offerings =
                                        await Purchases.getOfferings();
                                    if (!mounted) return;

                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (_) => MultiBlocProvider(
                                          providers: [
                                            BlocProvider<UpdateUserDetailCubit>(
                                              create: (_) => UpdateUserDetailCubit(
                                                  ProfileManagementRepository()),
                                            ),
                                            BlocProvider<UserDetailsCubit>(
                                              create: (_) => UserDetailsCubit(
                                                  ProfileManagementRepository()),
                                            ),
                                          ],
                                          child: Paywall(
                                            offering: offerings.current,
                                            onError: (String message) {
                                              return Center(
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.error_outline,
                                                      size: 64,
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .error,
                                                    ),
                                                    const SizedBox(height: 16),
                                                    Text(
                                                      message,
                                                      textAlign:
                                                          TextAlign.center,
                                                      style: TextStyle(
                                                        fontSize: 16,
                                                        color: Theme.of(context)
                                                            .colorScheme
                                                            .onSurface,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 24),
                                                    ElevatedButton(
                                                      onPressed: () =>
                                                          Navigator.pop(
                                                              context),
                                                      child:
                                                          const Text('العودة'),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    );
                                  } catch (e) {
                                    if (!mounted) return;
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (_) => MultiBlocProvider(
                                          providers: [
                                            BlocProvider<UpdateUserDetailCubit>(
                                              create: (_) => UpdateUserDetailCubit(
                                                  ProfileManagementRepository()),
                                            ),
                                            BlocProvider<UserDetailsCubit>(
                                              create: (_) => UserDetailsCubit(
                                                  ProfileManagementRepository()),
                                            ),
                                          ],
                                          child: const Paywall(
                                            offering: null,
                                          ),
                                        ),
                                      ),
                                    );
                                  }
                                },
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                subscriptionState.isSubscribed
                                    ? Icons.verified
                                    : Icons.workspace_premium,
                                color: Theme.of(context).colorScheme.onPrimary,
                                size: size.width * 0.045,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                subscriptionState.isSubscribed
                                    ? AppConstants.premiumTitle
                                    : AppConstants.upgradeTitle,
                                style: TextStyle(
                                  color:
                                      Theme.of(context).colorScheme.onPrimary,
                                  fontSize: size.width * 0.022,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
                SizedBox(width: size.width * 0.02),

                // Profile Button
                Container(
                  width: size.width * 0.11,
                  height: size.width * 0.11,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                  ),
                  child: IconButton(
                    onPressed: () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => MultiBlocProvider(
                          providers: [
                            BlocProvider<DeleteAccountCubit>(
                              create: (_) => DeleteAccountCubit(
                                  ProfileManagementRepository()),
                            ),
                            BlocProvider<UploadProfileCubit>(
                              create: (_) => UploadProfileCubit(
                                  ProfileManagementRepository()),
                            ),
                            BlocProvider<UpdateUserDetailCubit>(
                              create: (_) => UpdateUserDetailCubit(
                                  ProfileManagementRepository()),
                            ),
                          ],
                          child: MenuScreen(
                              isGuest: widget.isGuest, fromNav: false),
                        ),
                      ),
                    ),
                    icon: Icon(
                      Icons.person,
                      color: Theme.of(context).colorScheme.onSurface,
                      size: size.width * 0.06,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategory() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: hzMargin),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.04,
              vertical: MediaQuery.of(context).size.height * 0.015,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor.withOpacity(0.08),
                  Theme.of(context).primaryColor.withOpacity(0.03),
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding:
                      EdgeInsets.all(MediaQuery.of(context).size.width * 0.025),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor,
                        Theme.of(context).primaryColor.withOpacity(0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).primaryColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Iconsax.category,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: MediaQuery.of(context).size.width * 0.05,
                  ),
                ),
                SizedBox(width: MediaQuery.of(context).size.width * 0.03),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppConstants.coursesAndTrainingTitle,
                        style: _boldTextStyle.copyWith(
                          fontSize: MediaQuery.of(context).size.width * 0.045,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    widget.isGuest
                        ? _showLoginDialog()
                        : Navigator.of(context).pushNamed(
                            Routes.category,
                            arguments: {'quizType': QuizTypes.quizZone},
                          );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.03,
                      vertical: MediaQuery.of(context).size.height * 0.008,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).primaryColor.withOpacity(0.15),
                          Theme.of(context).primaryColor.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          context.tr(viewAllKey) ?? viewAllKey,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 10,
                          color: Theme.of(context).primaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Wrap(
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Positioned(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      // color: Theme.of(context).dialogBackgroundColor,
                    ),
                    margin: EdgeInsets.only(
                      left: hzMargin,
                      right: hzMargin,
                      top: 10,
                      bottom: 26,
                    ),
                    width: MediaQuery.of(context).size.width,
                    child: quizZoneCategories(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget quizZoneCategories() {
    return QuizZoneCategories(isGuest: widget.isGuest);
  }

  Widget _buildLiveContestSection() {
    final size = MediaQuery.of(context).size;

    void onTapViewAll() {
      if (_sysConfigCubit.isContestEnabled) {
        Navigator.of(context).pushNamed(Routes.contest);
      } else {
        UiUtils.showSnackBar(
          context.tr(currentlyNotAvailableKey)!,
          context,
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Modern section header with enhanced styling
        Container(
          margin: EdgeInsets.symmetric(horizontal: hzMargin),
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.04,
            vertical: size.height * 0.015,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor.withOpacity(0.08),
                Theme.of(context).primaryColor.withOpacity(0.03),
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(size.width * 0.025),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Iconsax.medal_star,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: size.width * 0.05,
                ),
              ),
              SizedBox(width: size.width * 0.03),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "قسم المسابقات",
                      style: _boldTextStyle.copyWith(
                        fontSize: size.width * 0.045,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: onTapViewAll,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: size.width * 0.03,
                    vertical: size.height * 0.008,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor.withOpacity(0.15),
                        Theme.of(context).primaryColor.withOpacity(0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        context.tr(viewAllKey) ?? viewAllKey,
                        style: TextStyle(
                          fontSize: size.width * 0.03,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      SizedBox(width: size.width * 0.01),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: size.width * 0.03,
                        color: Theme.of(context).primaryColor,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: size.height * 0.02),

        // محتوى المسابقات
        BlocConsumer<ContestCubit, ContestState>(
          bloc: context.read<ContestCubit>(),
          listener: (context, state) {
            if (state is ContestFailure) {
              if (state.errorMessage == errorCodeUnauthorizedAccess) {
                showAlreadyLoggedInDialog(context);
              }
            }
          },
          builder: (context, state) {
            if (state is ContestFailure) {
              return _buildErrorContainer(state.errorMessage);
            }

            if (state is ContestSuccess) {
              final live = state.contestList.live;

              if (live.contestDetails.isEmpty || live.errorMessage.isNotEmpty) {
                return _buildEmptyContests();
              }

              return SizedBox(
                height: 280,
                child: ListView.builder(
                  physics: const BouncingScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  itemCount: live.contestDetails.length,
                  itemBuilder: (context, index) {
                    final contest = live.contestDetails[index];
                    return _buildContestCard(contest);
                  },
                ),
              );
            }

            return const Center(
              child: CircularProgressContainer(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildContestCard(ContestDetails contest) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final cardWidth = isTablet ? 300.0 : 260.0;

    return GestureDetector(
      onTap: () {
        if (!widget.isGuest) {
          Navigator.of(context).pushNamed(
            Routes.quiz,
            arguments: {
              'numberOfPlayer': 1,
              'quizType': QuizTypes.contest,
              'contestId': contest.id,
            },
          );
        } else {
          _showLoginDialog();
        }
      },
      child: Container(
        width: cardWidth,
        height: isTablet ? 360 : 320,
        margin: EdgeInsets.only(right: size.width * 0.04),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.12),
              blurRadius: 16,
              spreadRadius: 0,
              offset: const Offset(0, 6),
            ),
            BoxShadow(
              color: Theme.of(context).shadowColor.withOpacity(0.04),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.08),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // صورة المسابقة والعنوان
              Container(
                height: isTablet ? 140 : 120,
                child: Stack(
                  children: [
                    // الصورة الأساسية
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: NetworkImage(
                              contest.image ??
                                  'https://placeholder.com/300x200',
                            ),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    // تدرج للنص
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Theme.of(context).brightness == Brightness.dark
                                  ? Theme.of(context)
                                      .shadowColor
                                      .withOpacity(0.8)
                                  : Theme.of(context)
                                      .shadowColor
                                      .withOpacity(0.6),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // شارة المنافسة
                    Positioned(
                      top: 10,
                      right: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).primaryColor,
                              Theme.of(context).primaryColor.withOpacity(0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context)
                                  .shadowColor
                                  .withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Iconsax.medal_star,
                              color: Theme.of(context).colorScheme.onPrimary,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              AppConstants.contestBadgeTitle,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onPrimary,
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // عنوان المسابقة
                    Positioned(
                      bottom: 10,
                      right: 12,
                      left: 12,
                      child: Text(
                        contest.name ?? '',
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontSize: isTablet ? 16 : 14,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? Theme.of(context)
                                      .shadowColor
                                      .withOpacity(0.7)
                                  : Theme.of(context)
                                      .shadowColor
                                      .withOpacity(0.5),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              // تفاصيل المسابقة
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(18),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // وصف المسابقة
                      if (contest.description != null &&
                          contest.description!.isNotEmpty)
                        Expanded(
                          flex: 2,
                          child: Text(
                            contest.description!,
                            style: TextStyle(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withOpacity(0.7),
                              fontSize: isTablet ? 14 : 12,
                              height: 1.5,
                              fontWeight: FontWeight.w400,
                            ),
                            maxLines: 4,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                          ),
                        ),

                      const SizedBox(height: 16),

                      // معلومات المشاركين والزر
                      Row(
                        children: [
                          // عدد المشاركين
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Iconsax.people,
                                  color: Theme.of(context).primaryColor,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  "${contest.participants ?? '0'}",
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const Spacer(),

                          // زر المشاركة
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Theme.of(context).primaryColor,
                                  Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.8),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Iconsax.play,
                                  color:
                                      Theme.of(context).colorScheme.onPrimary,
                                  size: 16,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  AppConstants.participateNowTitle,
                                  style: TextStyle(
                                    color:
                                        Theme.of(context).colorScheme.onPrimary,
                                    fontWeight: FontWeight.bold,
                                    fontSize: isTablet ? 14 : 13,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorContainer(String errorMessage) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(15),
      ),
      alignment: Alignment.center,
      child: Text(
        context.tr(convertErrorCodeToLanguageKey(errorMessage))!,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 16,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildEmptyContests() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(15),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.event_available_outlined,
            size: 40,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 12),
          Text(
            'لا توجد مسابقات متاحة حالياً',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'تفقد هذا القسم لاحقاً للمشاركة في المسابقات الجديدة',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHorizontalSections() {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    // Combine all challenge items
    final allChallenges = <Map<String, dynamic>>[
      ...battleName.asMap().entries.map((entry) => {
            'name': entry.value,
            'icon': entry.key == 0 ? Iconsax.people : Iconsax.profile_2user,
            'description': battleDesc[entry.key],
            'onTap': () => _onPressedBattle(entry.value),
            'color': Theme.of(context).primaryColor,
          }),
      ...examSelf.asMap().entries.map((entry) => {
            'name': entry.value,
            'icon': entry.key == 0 ? Iconsax.clipboard_text : Iconsax.medal,
            'description': examSelfDesc[entry.key],
            'onTap': () => _onPressedSelfExam(entry.value),
            'color': Theme.of(context).colorScheme.secondary != Colors.white
                ? Theme.of(context).colorScheme.secondary
                : Theme.of(context).primaryColor,
          }),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Simple section header
        Container(
          margin: EdgeInsets.symmetric(horizontal: hzMargin),
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.04,
            vertical: size.height * 0.01,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.secondary.withOpacity(0.08),
                Theme.of(context).colorScheme.secondary.withOpacity(0.03),
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(size.width * 0.025),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.secondary,
                      Theme.of(context).colorScheme.secondary.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context)
                          .colorScheme
                          .secondary
                          .withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Iconsax.medal,
                  color: Theme.of(context).colorScheme.onSecondary,
                  size: size.width * 0.05,
                ),
              ),
              SizedBox(width: size.width * 0.03),
              Text(
                "قسم التحديات",
                style: _boldTextStyle.copyWith(
                  fontSize: size.width * 0.045,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: size.height * 0.015),

        // Grid layout for challenges
        Container(
          margin: EdgeInsets.symmetric(horizontal: hzMargin),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: size.width * 0.02,
              mainAxisSpacing: size.height * 0.015,
              childAspectRatio: isTablet ? 0.85 : 0.75,
            ),
            itemCount: allChallenges.length,
            itemBuilder: (context, index) {
              final challenge = allChallenges[index];
              return _buildGridChallengeItem(
                challenge['name'],
                challenge['icon'],
                challenge['description'],
                challenge['onTap'],
                challenge['color'],
              );
            },
          ),
        ),
      ],
    );
  }

  // Grid challenge item builder
  Widget _buildGridChallengeItem(
    String name,
    IconData icon,
    String description,
    VoidCallback onTap,
    Color primaryColor,
  ) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return GestureDetector(
      onTap: widget.isGuest ? _showLoginDialog : onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: primaryColor.withOpacity(0.1),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: primaryColor.withOpacity(0.15),
            width: 1,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(size.width * 0.025),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon container with smaller size
              Container(
                padding: EdgeInsets.all(size.width * 0.02),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: isTablet ? 24 : 20,
                  color: primaryColor,
                ),
              ),
              SizedBox(height: size.height * 0.008),

              // Title text
              Flexible(
                child: Text(
                  context.tr(name)!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 13 : 11,
                    height: 1.2,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(height: size.height * 0.004),

              // Description text
              Flexible(
                child: Text(
                  context.tr(description)!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6),
                    fontSize: isTablet ? 10 : 9,
                    height: 1.1,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Enhanced helper function to build modern section items
  List<Widget> buildEnhancedSectionItems(
      List<String> names,
      List<IconData> icons,
      List<String> descriptions,
      Function(int) onTapCallback,
      {required Color primaryColor,
      required List<Color> gradientColors}) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;

    return List.generate(
      names.length,
      (i) => GestureDetector(
        onTap: () => widget.isGuest ? _showLoginDialog() : onTapCallback(i),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: isTablet ? 160 : 150,
          margin: EdgeInsets.only(
            right: size.width * 0.03,
            bottom: size.height * 0.008,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).cardColor,
                Theme.of(context).cardColor.withOpacity(0.95),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withOpacity(0.15),
                blurRadius: 15,
                spreadRadius: 0,
                offset: const Offset(0, 5),
              ),
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: primaryColor.withOpacity(0.1),
              width: 1.5,
            ),
          ),
          child: Stack(
            children: [
              // Background pattern
              Positioned(
                top: -20,
                right: -20,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        primaryColor.withOpacity(0.1),
                        primaryColor.withOpacity(0.05),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),

              // Main content
              Padding(
                padding: EdgeInsets.all(size.width * 0.04),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Enhanced icon container
                    Container(
                      padding: EdgeInsets.all(size.width * 0.035),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: gradientColors,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: primaryColor.withOpacity(0.4),
                            blurRadius: 12,
                            spreadRadius: 0,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        icons[i],
                        size: size.width * 0.07,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                    SizedBox(height: size.height * 0.015),

                    // Title with enhanced styling
                    Text(
                      context.tr(names[i])!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                        fontSize: size.width * 0.035,
                        height: 1.3,
                        letterSpacing: 0.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: size.height * 0.005),

                    // Subtitle/description
                    Text(
                      context.tr(descriptions[i])!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                        fontSize: size.width * 0.028,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Hover effect indicator
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.6),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Keep the old function for backward compatibility
  List<Widget> buildSectionItems(List<String> names, List<IconData> icons,
      List<String> descriptions, Function(int) onTapCallback,
      {required Color primaryColor}) {
    return buildEnhancedSectionItems(
      names,
      icons,
      descriptions,
      onTapCallback,
      primaryColor: primaryColor,
      gradientColors: [primaryColor, primaryColor.withOpacity(0.7)],
    );
  }

  String _userRank = '0';

  //String _userCoins = '0';
  String _userScore = '0';

  Widget _buildHome() {
    return Stack(
      children: [
        RefreshIndicator(
          key: ValueKey(Theme.of(context)
              .brightness), // إضافة key لإجبار إعادة البناء عند تغيير الثيم
          color: Theme.of(context).primaryColor,
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          onRefresh: () async {
            fetchUserDetails();

            _currLangId = UiUtils.getCurrentQuizLanguageId(context);
            final quizCubit = context.read<QuizCategoryCubit>();
            final quizZoneCubit = context.read<QuizoneCategoryCubit>();

            if (widget.isGuest) {
              await quizCubit.getQuizCategory(
                languageId: _currLangId,
                type: _quizZoneId,
              );
              await quizZoneCubit.getQuizCategory(languageId: _currLangId);
            } else {
              await quizCubit.getQuizCategoryWithUserId(
                languageId: _currLangId,
                type: _quizZoneId,
              );

              await quizZoneCubit.getQuizCategoryWithUserId(
                languageId: _currLangId,
              );
              await context
                  .read<ContestCubit>()
                  .getContest(languageId: _currLangId);
            }
            setState(() {});
          },
          child: ListView(
            children: [
              _buildProfileContainer(),
              UserAchievements(
                userRank: _userRank,
                userScore: _userScore,
              ),
              BlocBuilder<QuizoneCategoryCubit, QuizoneCategoryState>(
                builder: (context, state) {
                  if (state is QuizoneCategoryFailure &&
                      state.errorMessage == errorCodeDataNotFound) {
                    return const SizedBox.shrink();
                  }

                  if (_sysConfigCubit.isQuizZoneEnabled) {
                    return _buildCategory();
                  }

                  return const SizedBox.shrink();
                },
              ),
              if (_sysConfigCubit.isContestEnabled && !widget.isGuest) ...[
                _buildLiveContestSection(),
              ],
              const SizedBox(height: 20),
              _buildHorizontalSections(),
              const SizedBox(height: 30),
              _buildContactAndShareSection(),
              const SizedBox(height: 30),
            ],
          ),
        ),
        if (showUpdateContainer) const UpdateAppContainer(),
      ],
    );
  }

  void fetchUserDetails() {
    context.read<UserDetailsCubit>().fetchUserDetails();
  }

  bool profileComplete = false;

  final List<Widget> _navBodies = [];

  int _navIndex = 0;

  Widget buildHomeBody() {
    return SafeArea(
      child: widget.isGuest
          ? _buildHome()
          : BlocConsumer<UserDetailsCubit, UserDetailsState>(
              bloc: context.read<UserDetailsCubit>(),
              listener: (context, state) {
                if (state is UserDetailsFetchSuccess) {
                  UiUtils.fetchBookmarkAndBadges(
                    context: context,
                    userId: state.userProfile.userId!,
                  );
                  if (state.userProfile.profileUrl!.isEmpty ||
                      state.userProfile.name!.isEmpty) {
                    if (!profileComplete) {
                      profileComplete = true;

                      Navigator.of(context).pushNamed(
                        Routes.selectProfile,
                        arguments: false,
                      );
                    }
                    return;
                  }
                } else if (state is UserDetailsFetchFailure) {
                  if (state.errorMessage == errorCodeUnauthorizedAccess) {
                    showAlreadyLoggedInDialog(context);
                  }
                }
              },
              builder: (context, state) {
                if (state is UserDetailsFetchInProgress ||
                    state is UserDetailsInitial) {
                  return const Center(child: CircularProgressContainer());
                }
                if (state is UserDetailsFetchFailure) {
                  return Center(
                    child: ErrorContainer(
                      showBackButton: true,
                      errorMessage:
                          convertErrorCodeToLanguageKey(state.errorMessage),
                      onTapRetry: fetchUserDetails,
                      showErrorImage: true,
                    ),
                  );
                }

                final user = (state as UserDetailsFetchSuccess).userProfile;

                _userName = user.name!;
                _userProfileImg = user.profileUrl!;
                _userRank = user.allTimeRank!;
                //    _userCoins = user.coins!;
                _userScore = user.allTimeScore!;

                return _buildHome();
              },
            ),
    );
  }

  @override
  Widget build(BuildContext context) {
    /// need to add this here, cause textStyle doesn't update automatically when changing theme.
    _boldTextStyle = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 18,
      color: Theme.of(context).colorScheme.onSurface,
    );

    return Scaffold(
      key: ValueKey(Theme.of(context)
          .brightness), // إضافة key لإجبار إعادة البناء عند تغيير الثيم
      body: _navBodies.isEmpty
          ? buildHomeBody()
          : IndexedStack(
              index: _navIndex,
              children: _navBodies,
            ),
      bottomNavigationBar: Container(
        // border
        decoration: BoxDecoration(
          color: Theme.of(context).dialogBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withOpacity(0.2),
              blurRadius: 6,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: SalomonBottomBar(
          margin: EdgeInsets.only(
            left: hzMargin,
            right: hzMargin,
            bottom: 15,
            top: 12,
          ),
          onTap: (index) {
            if ((index == 1 || index == 2) && widget.isGuest) {
              _showLoginDialog();
              return;
            }
            setState(
              () {
                _navIndex = index;
              },
            );
          },
          currentIndex: _navIndex,
          unselectedItemColor:
              Theme.of(context).colorScheme.onSurface.withOpacity(0.85),
          selectedItemColor: Theme.of(context).primaryColor,
          items: [
            SalomonBottomBarItem(
              icon: const Icon(Icons.home_filled),
              title: Text(
                context.tr('homeBtn') ?? 'Home',
                style: TextStyle(
                  fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
                ),
              ),
            ),
            if (_sysConfigCubit.isQuizZoneEnabled)
              SalomonBottomBarItem(
                icon: const Icon(IconsaxPlusBold.category),
                title: Text(
                  context.tr('categoriesLbl') ?? 'Categories',
                  style: TextStyle(
                    fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
                  ),
                ),
              ),
            SalomonBottomBarItem(
              icon: const Icon(
                Icons.leaderboard_rounded,
              ),
              title: Text(
                context.tr('leaderboardLbl') ?? 'Leaderboard',
                style: TextStyle(
                  fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
                ),
              ),
            ),
            SalomonBottomBarItem(
              icon: const Icon(Icons.settings),
              title: Text(
                context.tr('settingLbl') ?? 'Settings',
                style: TextStyle(
                  fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactAndShareSection() {
    // حساب حجم الشاشة للتكيف مع مختلف الأجهزة
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;

    // تحديد الهوامش بناءً على حجم الشاشة
    final horizontalPadding = isTablet ? size.width * 0.08 : hzMargin;

    // تحديد ما إذا كان يجب استخدام تخطيط عمودي أو أفقي
    final useVerticalLayout = size.width < 480;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              children: [
                Icon(
                  Icons.support_agent,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  "تواصل معنا",
                  style: _boldTextStyle,
                ),
              ],
            ),
          ),

          // بطاقات التواصل
          useVerticalLayout
              ? Column(
                  children: [
                    _buildActionCard(
                      icon: FontAwesomeIcons.whatsapp,
                      title: "تواصل معنا",
                      subtitle: "لأي استفسارات أو الإقتراحات",
                      onTap: _openWhatsAppChat,
                      gradientColors: [
                        Theme.of(context).primaryColor.withOpacity(0.8),
                        Theme.of(context).primaryColor,
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildActionCard(
                      icon: Icons.share_rounded,
                      title: "شارك التطبيق",
                      subtitle: "مع أصدقائك",
                      onTap: _shareApp,
                      gradientColors: [
                        Theme.of(context)
                            .colorScheme
                            .secondary
                            .withOpacity(0.8),
                        Theme.of(context).colorScheme.secondary,
                      ],
                    ),
                  ],
                )
              : Row(
                  children: [
                    Expanded(
                      child: _buildActionCard(
                        icon: FontAwesomeIcons.whatsapp,
                        title: AppConstants.contactUsTitle,
                        subtitle: AppConstants.contactUsSubtitle,
                        onTap: _openWhatsAppChat,
                        gradientColors: [
                          Theme.of(context).primaryColor.withOpacity(0.8),
                          Theme.of(context).primaryColor,
                        ],
                      ),
                    ),
                    SizedBox(width: isTablet ? 20 : 12),
                    Expanded(
                      child: _buildActionCard(
                        icon: Icons.share_rounded,
                        title: AppConstants.shareAppTitle,
                        subtitle: AppConstants.shareAppSubtitle,
                        onTap: _shareApp,
                        gradientColors: [
                          Theme.of(context)
                              .colorScheme
                              .secondary
                              .withOpacity(0.8),
                          Theme.of(context).colorScheme.secondary,
                        ],
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required List<Color> gradientColors,
  }) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;
    final isTablet = size.width >= 600;
    final isLargeScreen = size.width >= 900;

    // تحديد الارتفاع بناءً على حجم الشاشة
    final cardHeight = isLargeScreen
        ? 120.0
        : (isTablet ? 110.0 : (isSmallScreen ? 90.0 : 100.0));

    // تحديد حجم الخط بناءً على حجم الشاشة
    final titleFontSize = isLargeScreen
        ? 18.0
        : (isTablet ? 17.0 : (isSmallScreen ? 14.0 : 16.0));

    final subtitleFontSize = isLargeScreen
        ? 14.0
        : (isTablet ? 13.0 : (isSmallScreen ? 11.0 : 12.0));

    // تحديد حجم الأيقونة بناءً على حجم الشاشة
    final iconSize = isLargeScreen
        ? 24.0
        : (isTablet ? 22.0 : (isSmallScreen ? 18.0 : 20.0));

    // تحديد حجم وسادة الأيقونة
    final iconPadding =
        isLargeScreen ? 14.0 : (isTablet ? 12.0 : (isSmallScreen ? 8.0 : 10.0));

    return Container(
      height: cardHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.08),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 4),
          ),
        ],
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 16 : 12),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(iconPadding),
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .onPrimary
                        .withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).shadowColor.withOpacity(0.05),
                        blurRadius: 5,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: iconSize,
                  ),
                ),
                SizedBox(width: isTablet ? 16 : 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontSize: titleFontSize,
                          fontWeight: FontWeight.bold,
                          height: 1.2,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: isTablet ? 4 : 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Theme.of(context)
                              .colorScheme
                              .onPrimary
                              .withOpacity(0.9),
                          fontSize: subtitleFontSize,
                          height: 1.2,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Open WhatsApp chat with the support number
  Future<void> _openWhatsAppChat() async {
    try {
      if (await canLaunchUrlString(AppConstants.whatsappUrl)) {
        await launchUrlString(AppConstants.whatsappUrl,
            mode: LaunchMode.externalApplication);
      } else {
        if (await canLaunchUrlString(AppConstants.whatsappFallbackUrl)) {
          await launchUrlString(AppConstants.whatsappFallbackUrl,
              mode: LaunchMode.externalApplication);
        } else {
          if (!mounted) return;
          UiUtils.showSnackBar(AppConstants.whatsappNotInstalledError, context);
        }
      }
    } catch (e) {
      log('Error opening WhatsApp: $e');
      if (!mounted) return;
      UiUtils.showSnackBar(AppConstants.whatsappOpenError, context);
    }
  }

  // Share app functionality
  Future<void> _shareApp() async {
    try {
      UiUtils.share(
        AppConstants.shareMessage,
        context: context,
      );
    } catch (e) {
      log('Error sharing app: $e');
      if (!mounted) return;

      UiUtils.showSnackBar(e.toString(), context);
    }
  }
}
