import 'dart:async';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:flutterquiz/features/systemConfig/cubits/appSettingsCubit.dart';
import 'package:flutterquiz/features/systemConfig/system_config_repository.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:url_launcher/url_launcher.dart';
class AppSettingsScreen extends StatefulWidget {
  const AppSettingsScreen({required this.title, super.key});

  final String title;

  static Route<AppSettingsScreen> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => BlocProvider<AppSettingsCubit>(
        create: (_) => AppSettingsCubit(SystemConfigRepository()),
        child: AppSettingsScreen(title: routeSettings.arguments! as String),
      ),
    );
  }

  @override
  State<AppSettingsScreen> createState() => _AppSettingsScreenState();
}

class _AppSettingsScreenState extends State<AppSettingsScreen> {
  late final _settingType = switch (widget.title) {
    aboutUs => 'about_us',
    privacyPolicy => 'privacy_policy',
    termsAndConditions => 'terms_conditions',
    contactUs => 'contact_us',
    howToPlayLbl => 'instructions',
    _ => '',
  };
  late final _screenTitle = context.tr(widget.title)!;

  @override
  void initState() {
    super.initState();
    fetchAppSetting();
  }

  void fetchAppSetting() {
    Future.delayed(Duration.zero, () {
      context.read<AppSettingsCubit>().getAppSetting(_settingType);
    });
  }

  FutureOr<bool> _onTapUrl(String url) async {
    final canLaunch = await canLaunchUrl(Uri.parse(url));
    if (canLaunch) {
      await launchUrl(Uri.parse(url));
    } else {
      log('Error Launching URL : $url', name: 'Launch URL');
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColor.withOpacity(0.8),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).scaffoldBackgroundColor,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          _screenTitle,
          style: TextStyle(
            color: Theme.of(context).scaffoldBackgroundColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.8),
              Theme.of(context).scaffoldBackgroundColor,
            ],
          ),
        ),
        child: BlocBuilder<AppSettingsCubit, AppSettingsState>(
          bloc: context.read<AppSettingsCubit>(),
          builder: (context, state) {
            if (state is AppSettingsFetchFailure) {
              return Center(
                child: ErrorContainer(
                  errorMessage: convertErrorCodeToLanguageKey(state.errorCode),
                  onTapRetry: fetchAppSetting,
                  showErrorImage: true,
                  errorMessageColor: Theme.of(context).scaffoldBackgroundColor,
                ),
              );
            }

            if (state is AppSettingsFetchSuccess) {
              return Container(
                margin: const EdgeInsets.only(top: 20),
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Header Icon
                    Container(
                      margin: const EdgeInsets.only(top: 20),
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getHeaderIcon(),
                        size: 40,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    // Content
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(15),
                          child: HtmlWidget(
                            state.settingsData,
                            onErrorBuilder: (_, e, err) => Text('$e error: $err'),
                            onLoadingBuilder: (_, e, l) => const Center(
                              child: CircularProgressIndicator(),
                            ),
                            textStyle: TextStyle(
                              fontSize: 16,
                              height: 1.8,
                              color: Theme.of(context).colorScheme.onTertiary,
                            ),
                            onTapUrl: _onTapUrl,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            return Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).scaffoldBackgroundColor,
              ),
            );
          },
        ),
      ),
    );
  }

  IconData _getHeaderIcon() {
    return switch (widget.title) {
      aboutUs => Icons.info_outline,
      privacyPolicy => Icons.security_outlined,
      termsAndConditions => Icons.gavel_outlined,
      contactUs => Icons.contact_support_outlined,
      howToPlayLbl => Icons.help_outline,
      _ => Icons.article_outlined,
    };
  }
}
