import 'dart:io';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:developer';
import 'package:path/path.dart' as path;

/// خدمة لضغط الصور قبل رفعها إلى السيرفر
/// تقوم بتقليل حجم الصور مع الحفاظ على جودة مقبولة
/// وتتحقق من صحة الصور قبل رفعها لمنع رفع ملفات خبيثة
class ImageCompressionService {
  // إعدادات ثابتة للضغط
  static const int maxWidth = 800;
  static const int maxHeight = 800;
  static const int quality = 85;
  static const int maxSizeInBytes = 100 * 1024; // 100 كيلوبايت

  // الحد الأقصى لحجم الملف (5 ميجابايت)
  static const int maxAllowedFileSize = 5 * 1024 * 1024;

  // امتدادات الصور المسموح بها
  static const List<String> validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

  /// دالة لضغط الصورة
  /// تقوم بضغط الصورة إذا كان حجمها أكبر من الحد الأقصى
  /// وتعيد الصورة كما هي إذا كان حجمها أقل من الحد الأقصى
  static Future<File> compressImage(File imageFile) async {
    try {
      // التحقق من حجم الملف
      final fileSize = await imageFile.length();
      log('Original image size: ${(fileSize / 1024).toStringAsFixed(2)} KB');

      // إذا كان الحجم أقل من الحد الأقصى، نعيد الملف كما هو
      if (fileSize <= maxSizeInBytes) {
        log('Image size is already within limits, no compression needed');
        return imageFile;
      }

      // الحصول على مسار مؤقت لحفظ الصورة المضغوطة
      final tempDir = await getTemporaryDirectory();
      final targetPath = '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';

      // ضغط الصورة
      final result = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        targetPath,
        minWidth: maxWidth,
        minHeight: maxHeight,
        quality: quality,
      );

      // إذا فشلت عملية الضغط، نعيد الملف الأصلي
      if (result == null) {
        log('Compression failed, returning original image');
        return imageFile;
      }

      // التحقق من حجم الصورة بعد الضغط
      final compressedSize = await File(result.path).length();
      log('Compressed image size: ${(compressedSize / 1024).toStringAsFixed(2)} KB');
      log('Compression ratio: ${(compressedSize / fileSize * 100).toStringAsFixed(2)}%');

      return File(result.path);
    } catch (e) {
      // في حالة حدوث خطأ، نعيد الملف الأصلي
      log('Error during image compression: $e');
      return imageFile;
    }
  }

  /// دالة للتحقق من صحة الصورة
  /// تتحقق من امتداد الملف وحجمه ومحتواه للتأكد من أنه صورة حقيقية
  /// وليس ملفاً خبيثاً
  static Future<Map<String, dynamic>> isValidImage(File file) async {
    try {
      // التحقق من امتداد الملف
      final extension = path.extension(file.path).toLowerCase();
      if (!validExtensions.contains(extension)) {
        log('Invalid file extension: $extension');
        return {
          'isValid': false,
          'message': 'نوع الملف غير مدعوم. الأنواع المدعومة هي: JPG, JPEG, PNG, GIF, WEBP'
        };
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      if (fileSize > maxAllowedFileSize) {
        log('File size exceeds maximum allowed size: ${(fileSize / (1024 * 1024)).toStringAsFixed(2)} MB');
        return {
          'isValid': false,
          'message': 'حجم الملف كبير جداً. الحد الأقصى المسموح به هو 5 ميجابايت'
        };
      }

      // التحقق من محتوى الملف
      try {
        // قراءة أول بضعة بايتات من الملف للتحقق من توقيع الملف (file signature)
        final bytes = await file.openRead(0, 12).toList();
        if (bytes.isEmpty) {
          log('Could not read file bytes');
          return {
            'isValid': false,
            'message': 'لا يمكن قراءة محتوى الملف'
          };
        }

        final firstBytes = bytes.first;

        // التحقق من توقيعات الملفات الشائعة للصور
        // JPEG: FF D8 FF
        // PNG: 89 50 4E 47
        // GIF: 47 49 46 38
        // WEBP: 52 49 46 46 ... 57 45 42 50

        if (firstBytes.length < 3) {
          log('File too small to be a valid image');
          return {
            'isValid': false,
            'message': 'الملف صغير جداً ليكون صورة صالحة'
          };
        }

        // التحقق من توقيع JPEG
        if (extension == '.jpg' || extension == '.jpeg') {
          if (!(firstBytes[0] == 0xFF && firstBytes[1] == 0xD8 && firstBytes[2] == 0xFF)) {
            log('Invalid JPEG signature');
            return {
              'isValid': false,
              'message': 'الملف ليس صورة JPEG صالحة'
            };
          }
        }

        // التحقق من توقيع PNG
        else if (extension == '.png') {
          if (firstBytes.length < 4 ||
              !(firstBytes[0] == 0x89 && firstBytes[1] == 0x50 &&
                firstBytes[2] == 0x4E && firstBytes[3] == 0x47)) {
            log('Invalid PNG signature');
            return {
              'isValid': false,
              'message': 'الملف ليس صورة PNG صالحة'
            };
          }
        }

        // التحقق من توقيع GIF
        else if (extension == '.gif') {
          if (firstBytes.length < 4 ||
              !(firstBytes[0] == 0x47 && firstBytes[1] == 0x49 &&
                firstBytes[2] == 0x46 && firstBytes[3] == 0x38)) {
            log('Invalid GIF signature');
            return {
              'isValid': false,
              'message': 'الملف ليس صورة GIF صالحة'
            };
          }
        }

      } catch (e) {
        log('Error checking file content: $e');
        return {
          'isValid': false,
          'message': 'حدث خطأ أثناء التحقق من محتوى الملف'
        };
      }

      // إذا اجتاز الملف جميع الفحوصات، فهو صورة صالحة
      log('File is a valid image');
      return {
        'isValid': true,
        'message': 'الملف صورة صالحة'
      };
    } catch (e) {
      log('Error validating image: $e');
      return {
        'isValid': false,
        'message': 'حدث خطأ أثناء التحقق من الصورة'
      };
    }
  }

  /// دالة للتحقق من صحة الصورة وضغطها في خطوة واحدة
  /// تتحقق أولاً من صحة الصورة، ثم تقوم بضغطها إذا كانت صالحة
  static Future<Map<String, dynamic>> validateAndCompressImage(File imageFile) async {
    try {
      // التحقق من صحة الصورة
      final validationResult = await isValidImage(imageFile);
      if (!validationResult['isValid']) {
        return validationResult;
      }

      // ضغط الصورة
      final compressedImage = await compressImage(imageFile);

      return {
        'isValid': true,
        'message': 'تم التحقق من الصورة وضغطها بنجاح',
        'file': compressedImage
      };
    } catch (e) {
      log('Error in validateAndCompressImage: $e');
      return {
        'isValid': false,
        'message': 'حدث خطأ أثناء التحقق من الصورة وضغطها'
      };
    }
  }
}
